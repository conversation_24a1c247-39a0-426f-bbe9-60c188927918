#!/usr/bin/env python3
"""
Installation script for Phishing Awareness Platform
"""

import os
import sys
import subprocess
import shutil

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "uploads",
        "cloned_sites",
        "logs",
        "static",
        "templates"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"ℹ️  Directory exists: {directory}")

def create_env_file():
    """Create .env file from example"""
    if not os.path.exists('.env'):
        if os.path.exists('config.env.example'):
            shutil.copy('config.env.example', '.env')
            print("✅ Created .env file from example")
        else:
            print("⚠️  No .env.example found, creating basic .env file")
            with open('.env', 'w') as f:
                f.write("""# Phishing Awareness Platform Configuration
SECRET_KEY=your-secret-key-change-this-in-production
SECURITY_KEY=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
FLASK_ENV=development
""")
    else:
        print("ℹ️  .env file already exists")

def run_tests():
    """Run platform tests"""
    print("🧪 Running platform tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_platform.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Platform tests passed")
            return True
        else:
            print("❌ Platform tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def main():
    """Main installation function"""
    print("🚀 Installing Phishing Awareness Platform...")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Installation failed at dependency step")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create environment file
    create_env_file()
    
    # Run tests
    if not run_tests():
        print("⚠️  Tests failed, but installation completed")
        print("You may need to configure the platform manually")
    
    print("\n" + "=" * 50)
    print("🎉 Installation completed!")
    print("\n📝 Next steps:")
    print("1. Edit .env file with your configuration")
    print("2. Run: python run.py")
    print("3. Access at: http://localhost:5000")
    print("4. Login with default credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("   Security Key: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6")
    print("\n⚠️  IMPORTANT: Change default credentials in production!")
    print("📚 This platform is for educational purposes only.")

if __name__ == "__main__":
    main() 