<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AMADIOHA-M257</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto Mono', monospace;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
        }

        .login-card {
            background: #000000;
            border: 2px solid #ffffff;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
            overflow: hidden;
        }

        .login-header {
            background: #000000;
            color: #000000;
            padding: 20px;
            text-align: center;
            border-bottom: 2px solid #ffffff;
            border-inline: 2px solid #07c054 ;
        }

        .login-header h4 {
            margin: 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .login-body {
            padding: 30px;
        }

        .form-label {
            color: #ffffff;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .form-control {
            background: #000000 !important;
            border: 1px solid #ffffff !important;
            color: #ffffff !important;
            font-family: 'Roboto Mono', monospace !important;
            border-radius: 4px !important;
            padding: 12px !important;
        }

        .form-control:focus {
            border-color: #ffffff !important;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3) !important;
            background: #000000 !important;
            color: #ffffff !important;
        }

        .form-control::placeholder {
            color: #888888 !important;
            opacity: 0.7 !important;
        }

        .btn-login {
            background: #000000 !important;
            border: 2px solid #ffffff !important;
            color: #ffffff !important;
            font-family: 'Roboto Mono', monospace !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            font-weight: bold !important;
            padding: 12px !important;
            transition: all 0.2s !important;
            width: 100%;
        }

        .btn-login:hover {
            background: #ffffff !important;
            color: #000000 !important;
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.5) !important;
            transform: translateY(-2px) !important;
        }

        .credentials-info {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid #ffffff;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
        }

        .credentials-info h6 {
            color: #ffffff;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .credentials-info p {
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .alert-error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff0000;
            color: #ff0000;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="{{ url_for('static', filename='images/amadioha-logo.svg') }}" alt="AMADIOHA-M257" style="max-width: 250px; height: auto; margin: 0 auto; display: block;">
            </div>
            <div class="login-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert-error">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Enter username" required>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Enter password" required>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt"></i> AMADIOHA POSSESS ME
                    </button>
                </form>

                <div class="credentials-info">
                    <h6><i class="fas fa-info-circle"></i> Default Access</h6>
                    <p><strong>OWNLGAXINGXOBIBIN</strong></p>
                    
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
