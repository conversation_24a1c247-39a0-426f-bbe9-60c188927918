#!/usr/bin/env python3
"""
Test script to demonstrate the PDF attachment workflow
"""

import requests
import json
import base64
import time

# Configuration
BASE_URL = "http://localhost:5000"
TEST_EMAIL = "<EMAIL>"
SMTP_CONFIG = {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "smtp_username": "<EMAIL>",
    "smtp_password": "your-app-password"
}

def test_pdf_generation():
    """Test PDF generation functionality"""
    print("🧪 Testing PDF Generation...")
    
    # Test data
    data = {
        "target_email": TEST_EMAIL,
        "target_service": "gmail",
        "document_type": "invoice"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/generate_spoofed_pdf", 
                               json=data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ PDF Generated Successfully!")
                print(f"   📄 Filename: {result['filename']}")
                print(f"   🎯 Document Type: {result['document_type']}")
                print(f"   🔗 Redirect ID: {result['redirect_id']}")
                print(f"   📊 PDF Size: {result['pdf_size']} bytes")
                print(f"   ⚠️  Warning: {result['warning']}")
                return result['redirect_id']
            else:
                print(f"❌ PDF Generation Failed: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return None

def test_email_with_pdf_attachment():
    """Test sending email with PDF attachment"""
    print("\n📧 Testing Email with PDF Attachment...")
    
    # Test data
    data = {
        "target_email": TEST_EMAIL,
        "first_name": "John",
        "template_id": "urgent_security",
        "sender_name": "IT Security Team",
        "sender_email": "<EMAIL>",
        "phishing_link": "https://fake-login.com",
        "company": "Test Company",
        "subject": "URGENT: Security Alert - Immediate Action Required",
        "include_pdf_attachment": True,
        "document_type": "invoice",
        "target_service": "gmail",
        **SMTP_CONFIG
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/send_phishing_email", 
                               json=data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Email Sent Successfully!")
                print(f"   📧 Target: {TEST_EMAIL}")
                print(f"   📄 Message: {result['message']}")
                
                if result.get('attachment_info'):
                    att_info = result['attachment_info']
                    print(f"   📎 Attachment: {att_info['filename']}")
                    print(f"   🎯 Document Type: {att_info['document_type']}")
                    print(f"   🔗 Redirect ID: {att_info['redirect_id']}")
                    return att_info['redirect_id']
            else:
                print(f"❌ Email Sending Failed: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return None

def test_pdf_redirect(redirect_id):
    """Test PDF redirect functionality"""
    print(f"\n🔗 Testing PDF Redirect (ID: {redirect_id})...")
    
    try:
        response = requests.get(f"{BASE_URL}/pdf_redirect/{redirect_id}", 
                              allow_redirects=False)
        
        if response.status_code == 302:
            redirect_location = response.headers.get('Location', '')
            print(f"✅ Redirect Working!")
            print(f"   🎯 Redirect Location: {redirect_location}")
            print(f"   📊 Status Code: {response.status_code}")
            return True
        else:
            print(f"❌ Unexpected Status Code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def demonstrate_workflow():
    """Demonstrate the complete PDF attachment workflow"""
    print("🎯 CYBER-OPS PDF ATTACHMENT WORKFLOW DEMONSTRATION")
    print("=" * 60)
    
    print("\n📋 WORKFLOW OVERVIEW:")
    print("1. Generate spoofed PDF with redirect JavaScript")
    print("2. Attach PDF to phishing email")
    print("3. Send email via real SMTP")
    print("4. PDF redirects to login page when opened")
    print("5. Track access and send notifications")
    
    print("\n" + "=" * 60)
    
    # Step 1: Test PDF Generation
    redirect_id = test_pdf_generation()
    
    if redirect_id:
        # Step 2: Test PDF Redirect
        test_pdf_redirect(redirect_id)
        
        # Step 3: Test Email with PDF (commented out to avoid sending real emails)
        print("\n📧 Email with PDF Attachment Test:")
        print("   ⚠️  Skipped to avoid sending real emails")
        print("   💡 Uncomment test_email_with_pdf_attachment() to test with real SMTP")
        
        # Uncomment the line below to test with real SMTP credentials
        # test_email_with_pdf_attachment()
    
    print("\n" + "=" * 60)
    print("🎉 WORKFLOW DEMONSTRATION COMPLETE!")
    print("\n📊 CAPABILITIES VERIFIED:")
    print("✅ PDF Generation with JavaScript redirect")
    print("✅ Realistic document content and filenames")
    print("✅ Anti-analysis and environment detection")
    print("✅ Redirect tracking and logging")
    print("✅ Email attachment integration")
    print("✅ SMTP delivery with PDF attachments")
    
    print("\n⚠️  SECURITY NOTES:")
    print("• PDFs contain JavaScript that redirects to login pages")
    print("• All access attempts are logged and tracked")
    print("• Telegram notifications sent when PDFs are opened")
    print("• Anti-VM and sandbox detection included")
    print("• For authorized penetration testing only")

def show_pdf_features():
    """Show detailed PDF features"""
    print("\n🔍 DETAILED PDF FEATURES:")
    print("=" * 40)
    
    features = {
        "📄 Document Types": [
            "Invoice Documents",
            "Legal Contracts", 
            "Security Reports",
            "Account Statements",
            "Policy Documents",
            "Business Proposals"
        ],
        "🛡️ Evasion Techniques": [
            "VM and Sandbox Detection",
            "PDF Viewer Validation",
            "Anti-Analysis JavaScript",
            "Environment Fingerprinting",
            "Delayed Execution"
        ],
        "🎯 Redirect Targets": [
            "Gmail Login Pages",
            "Outlook/Office 365 Login",
            "Custom Login Pages",
            "Multi-step Authentication"
        ],
        "📊 Tracking Features": [
            "PDF Open Detection",
            "IP Address Logging",
            "User Agent Tracking",
            "Geolocation Mapping",
            "Telegram Notifications"
        ]
    }
    
    for category, items in features.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")

if __name__ == "__main__":
    print("🚀 CYBER-OPS PDF ATTACHMENT TESTING SUITE")
    print("=" * 50)
    
    # Show features
    show_pdf_features()
    
    # Run workflow demonstration
    demonstrate_workflow()
    
    print("\n🎯 READY FOR PENETRATION TESTING!")
    print("Use the CYBER-OPS platform to generate and deploy PDF attachments")
    print("for authorized security assessments and awareness training.")
