{% extends 'base.html' %}
{% block title %}Payload Generator | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-bomb text-danger"></i> Payload Generator</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-shield-alt"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Reverse Shell Generator -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-terminal text-success"></i> Reverse Shell Generator</h5>
                </div>
                <div class="card-body">
                    <form id="reverseShellForm">
                        <div class="mb-3">
                            <label class="form-label">Listener Host (LHOST)</label>
                            <input type="text" class="form-control" id="lhost" value="127.0.0.1" required>
                            <div class="form-text">IP address of your listening machine</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Listener Port (LPORT)</label>
                            <input type="number" class="form-control" id="lport" value="4444" min="1" max="65535" required>
                            <div class="form-text">Port to connect back to</div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-code"></i> Generate Reverse Shell
                        </button>
                    </form>
                    
                    <div id="reverseShellResult" class="mt-3" style="display: none;">
                        <h6>Generated Payload:</h6>
                        <div class="alert alert-dark">
                            <textarea id="reverseShellCode" class="form-control" rows="15" readonly></textarea>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('reverseShellCode')">
                            <i class="fas fa-copy"></i> Copy Payload
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadPayload('reverse_shell.py', 'reverseShellCode')">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Keylogger Generator -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-keyboard text-warning"></i> Educational Keylogger</h5>
                </div>
                <div class="card-body">
                    <form id="keyloggerForm">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Educational Purpose:</strong> This keylogger is for learning about security vulnerabilities.
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-code"></i> Generate Keylogger
                        </button>
                    </form>
                    
                    <div id="keyloggerResult" class="mt-3" style="display: none;">
                        <h6>Generated Keylogger:</h6>
                        <div class="alert alert-dark">
                            <textarea id="keyloggerCode" class="form-control" rows="15" readonly></textarea>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('keyloggerCode')">
                            <i class="fas fa-copy"></i> Copy Code
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadPayload('keylogger.py', 'keyloggerCode')">
                            <i class="fas fa-download"></i> Download
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payload Information -->
    <div class="row">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-info-circle"></i> Payload Information & Usage</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Reverse Shell:</h6>
                            <ul>
                                <li>Connects back to attacker's machine</li>
                                <li>Provides remote command execution</li>
                                <li>Bypasses firewalls (outbound connection)</li>
                                <li>Requires listener: <code>nc -lvp 4444</code></li>
                            </ul>
                            
                            <h6>Keylogger:</h6>
                            <ul>
                                <li>Records keyboard input</li>
                                <li>Saves to local file</li>
                                <li>Educational implementation only</li>
                                <li>Press ESC to stop logging</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Detection Methods:</h6>
                            <ul>
                                <li>Network monitoring for unusual connections</li>
                                <li>Process monitoring and behavioral analysis</li>
                                <li>Antivirus and endpoint protection</li>
                                <li>File integrity monitoring</li>
                            </ul>
                            
                            <h6>Prevention:</h6>
                            <ul>
                                <li>Application whitelisting</li>
                                <li>Network segmentation</li>
                                <li>User education and awareness</li>
                                <li>Regular security assessments</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-danger mt-3">
                        <strong>Legal Warning:</strong> These payloads are for educational and authorized penetration testing only. 
                        Using them without explicit permission is illegal and may result in criminal charges.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('reverseShellForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        payload_type: 'reverse_shell',
        lhost: document.getElementById('lhost').value,
        lport: parseInt(document.getElementById('lport').value)
    };
    
    fetch('/api/generate_payload', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('reverseShellCode').value = data.payload;
            document.getElementById('reverseShellResult').style.display = 'block';
        }
    });
});

document.getElementById('keyloggerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        payload_type: 'keylogger'
    };
    
    fetch('/api/generate_payload', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('keyloggerCode').value = data.payload;
            document.getElementById('keyloggerResult').style.display = 'block';
        }
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}

function downloadPayload(filename, elementId) {
    const content = document.getElementById(elementId).value;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

{% endblock %}
