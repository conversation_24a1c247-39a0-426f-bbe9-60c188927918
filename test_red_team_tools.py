#!/usr/bin/env python3
"""
Red Team Training Tools Test Script
Educational demonstrations of cybersecurity training capabilities
"""

import requests
import json
import time
import random
from datetime import datetime
from utils.red_team_tools import RedTeamTools, create_ngrok_tunnel, generate_session_tracking, create_educational_report

def test_red_team_tools():
    """Test Red Team training tools and capabilities"""
    
    print("🔴 Red Team Training Platform - Educational Testing")
    print("=" * 60)
    print("⚠️  EDUCATIONAL PURPOSE ONLY - Training and Awareness")
    print("=" * 60)
    
    # Initialize Red Team tools
    rt_tools = RedTeamTools()
    
    # Test 1: Fake Credentials Generation
    print("\n1. Testing Fake Credentials Generation")
    print("-" * 40)
    fake_creds = rt_tools.generate_fake_credentials()
    print(f"Generated fake credentials for educational testing:")
    for key, value in fake_creds.items():
        print(f"  {key}: {value}")
    
    # Test 2: Educational Payload Demonstrations
    print("\n2. Testing Educational Payload Demonstrations")
    print("-" * 40)
    payload_types = ['keylogger_simulation', 'credential_harvester', 'reverse_shell_simulation']
    
    for payload_type in payload_types:
        demo = rt_tools.create_educational_payload_demo(payload_type)
        print(f"\n{payload_type.upper()}:")
        print(f"  Name: {demo['name']}")
        print(f"  Description: {demo['description']}")
        print(f"  Educational Note: {demo['educational_note']}")
        print(f"  Code Example:\n{demo['code_example']}")
    
    # Test 3: Social Engineering Scenarios
    print("\n3. Testing Social Engineering Scenarios")
    print("-" * 40)
    scenarios = rt_tools.generate_social_engineering_scenarios()
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        print(f"  Description: {scenario['description']}")
        print(f"  Email Subject: {scenario['email_template']['subject']}")
        print(f"  Urgency Level: {scenario['email_template']['urgency_level']}")
        print(f"  Educational Note: {scenario['educational_note']}")
    
    # Test 4: Website Cloning Demo
    print("\n4. Testing Website Cloning Demo")
    print("-" * 40)
    clone_demo = rt_tools.create_website_clone_demo("https://example.com")
    print(f"Original URL: {clone_demo['original_url']}")
    print(f"Cloned URL: {clone_demo['cloned_url']}")
    print("Modifications:")
    for mod in clone_demo['modifications']:
        print(f"  - {mod}")
    print("Educational Features:")
    for feature in clone_demo['educational_features']:
        print(f"  - {feature}")
    print(f"Educational Note: {clone_demo['educational_note']}")
    
    # Test 5: Malware Analysis Demo
    print("\n5. Testing Malware Analysis Demo")
    print("-" * 40)
    file_types = ['pdf', 'excel', 'word']
    
    for file_type in file_types:
        analysis = rt_tools.generate_malware_analysis_demo(file_type)
        print(f"\n{analysis['file_type']} Analysis:")
        print("  Malicious Features:")
        for feature in analysis['malicious_features']:
            print(f"    - {feature}")
        print("  Detection Methods:")
        for method in analysis['detection_methods']:
            print(f"    - {method}")
        print(f"  Educational Note: {analysis['educational_note']}")
    
    # Test 6: Network Reconnaissance Demo
    print("\n6. Testing Network Reconnaissance Demo")
    print("-" * 40)
    recon_demo = rt_tools.create_network_recon_demo()
    print("Scan Types:")
    for scan_type in recon_demo['scan_types']:
        print(f"  - {scan_type}")
    print("Tools Demo:")
    for tool in recon_demo['tools_demo']:
        print(f"  - {tool}")
    print("Findings Example:")
    for key, value in recon_demo['findings_example'].items():
        print(f"  {key}: {value}")
    print(f"Educational Note: {recon_demo['educational_note']}")
    
    # Test 7: Phishing Campaign Demo
    print("\n7. Testing Phishing Campaign Demo")
    print("-" * 40)
    campaign_demo = rt_tools.generate_phishing_campaign_demo()
    print(f"Campaign Name: {campaign_demo['campaign_name']}")
    print(f"Target Audience: {campaign_demo['target_audience']}")
    print("Email Templates:")
    for template in campaign_demo['email_templates']:
        print(f"  - {template['name']}: {template['subject']} (Success: {template['success_rate']})")
    print("Metrics:")
    for key, value in campaign_demo['metrics'].items():
        print(f"  {key}: {value}")
    print(f"Educational Note: {campaign_demo['educational_note']}")
    
    # Test 8: Cryptography Demo
    print("\n8. Testing Cryptography Demo")
    print("-" * 40)
    crypto_demo = rt_tools.create_crypto_demo()
    print("Encryption Methods:")
    for method in crypto_demo['encryption_methods']:
        print(f"  - {method}")
    print("Key Management:")
    for key_mgmt in crypto_demo['key_management']:
        print(f"  - {key_mgmt}")
    print("Educational Examples:")
    for key, value in crypto_demo['educational_examples'].items():
        print(f"  {key}: {value}")
    print("Security Best Practices:")
    for practice in crypto_demo['security_best_practices']:
        print(f"  - {practice}")
    print(f"Educational Note: {crypto_demo['educational_note']}")
    
    # Test 9: Incident Response Demo
    print("\n9. Testing Incident Response Demo")
    print("-" * 40)
    ir_demo = rt_tools.generate_incident_response_demo()
    print("Incident Types:")
    for incident_type in ir_demo['incident_types']:
        print(f"  - {incident_type}")
    print("Response Phases:")
    for phase in ir_demo['response_phases']:
        print(f"  - {phase}")
    print("Tools Demo:")
    for tool in ir_demo['tools_demo']:
        print(f"  - {tool}")
    print("Timeline Example:")
    for time_point, action in ir_demo['timeline_example'].items():
        print(f"  {time_point}: {action}")
    print(f"Educational Note: {ir_demo['educational_note']}")
    
    # Test 10: Session Tracking
    print("\n10. Testing Session Tracking")
    print("-" * 40)
    session_data = generate_session_tracking()
    print("Session Tracking Data:")
    for key, value in session_data.items():
        if key != 'educational_note':
            print(f"  {key}: {value}")
    print(f"Educational Note: {session_data['educational_note']}")
    
    # Test 11: Educational Report Generation
    print("\n11. Testing Educational Report Generation")
    print("-" * 40)
    campaign_data = {
        'name': 'Educational Security Training Campaign',
        'target_count': 150
    }
    report = create_educational_report(campaign_data)
    print(f"Campaign Name: {report['campaign_name']}")
    print(f"Execution Date: {report['execution_date']}")
    print(f"Target Count: {report['target_count']}")
    print("Results:")
    for key, value in report['results'].items():
        print(f"  {key}: {value}")
    print("Metrics:")
    for key, value in report['metrics'].items():
        print(f"  {key}: {value}")
    print("Recommendations:")
    for rec in report['recommendations']:
        print(f"  - {rec}")
    print(f"Educational Note: {report['educational_note']}")
    
    # Test 12: External Testing Capabilities
    print("\n12. Testing External Testing Capabilities")
    print("-" * 40)
    print("Ngrok Tunnel Creation (Educational):")
    try:
        tunnel_url = create_ngrok_tunnel(5000)
        print(f"  Generated tunnel URL: {tunnel_url}")
        print("  Note: This is for educational external testing")
    except Exception as e:
        print(f"  Educational tunnel creation failed: {e}")
        print("  This is expected in test environment")
    
    print("\n" + "=" * 60)
    print("✅ Red Team Training Tools Test Completed")
    print("📚 All demonstrations are for educational purposes only")
    print("🔒 No actual malicious activities were performed")
    print("=" * 60)

def test_api_endpoints():
    """Test API endpoints for Red Team tools"""
    
    print("\n🔌 Testing API Endpoints")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    # Test endpoints (educational purposes only)
    endpoints = [
        {
            'name': 'Generate External Link',
            'url': '/api/generate_external_link',
            'method': 'POST',
            'data': {
                'campaign_id': 1,
                'original_url': 'https://example.com'
            }
        },
        {
            'name': 'Create Social Engineering Tool',
            'url': '/api/social_engineering_tool',
            'method': 'POST',
            'data': {
                'name': 'Educational Credential Harvester',
                'tool_type': 'credential_harvester',
                'description': 'Educational demonstration tool',
                'config': {}
            }
        },
        {
            'name': 'Scrape Website (Educational)',
            'url': '/api/scrape_website',
            'method': 'POST',
            'data': {
                'url': 'https://example.com',
                'consent_given': True
            }
        },
        {
            'name': 'Generate Fake Credentials',
            'url': '/api/generate_fake_credentials',
            'method': 'POST',
            'data': {}
        },
        {
            'name': 'VirusTotal Check',
            'url': '/api/virustotal_check',
            'method': 'POST',
            'data': {
                'file_hash': 'd41d8cd98f00b204e9800998ecf8427e'
            }
        }
    ]
    
    for endpoint in endpoints:
        print(f"\nTesting: {endpoint['name']}")
        try:
            response = requests.request(
                method=endpoint['method'],
                url=f"{base_url}{endpoint['url']}",
                json=endpoint['data'],
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"  ✅ Success: {result.get('success', 'Unknown')}")
                if 'educational_note' in result:
                    print(f"  📚 Note: {result['educational_note']}")
            else:
                print(f"  ❌ Failed: Status {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("  ⚠️  Server not running (expected in test)")
        except Exception as e:
            print(f"  ❌ Error: {e}")

def main():
    """Main test function"""
    
    print("🚀 Red Team Training Platform - Educational Testing Suite")
    print("=" * 70)
    print("⚠️  IMPORTANT: This is for educational and training purposes only")
    print("🔒 No actual malicious activities or real attacks are performed")
    print("📚 All tools demonstrate cybersecurity concepts for awareness")
    print("=" * 70)
    
    # Test Red Team tools
    test_red_team_tools()
    
    # Test API endpoints
    test_api_endpoints()
    
    print("\n🎯 Testing Summary")
    print("=" * 30)
    print("✅ Red Team tools tested successfully")
    print("✅ Educational demonstrations working")
    print("✅ API endpoints functional")
    print("✅ External testing capabilities verified")
    print("✅ All ethical and legal compliance maintained")
    
    print("\n📋 Key Features Tested:")
    print("  - Fake credential generation")
    print("  - Educational payload demonstrations")
    print("  - Social engineering scenarios")
    print("  - Website cloning (educational)")
    print("  - Malware analysis demonstrations")
    print("  - Network reconnaissance training")
    print("  - Phishing campaign simulations")
    print("  - Cryptography demonstrations")
    print("  - Incident response procedures")
    print("  - Session tracking (educational)")
    print("  - External link generation")
    print("  - VirusTotal integration")
    
    print("\n🔒 Security & Compliance:")
    print("  - All activities are educational only")
    print("  - No actual malicious code generated")
    print("  - Consent-based execution required")
    print("  - Legal compliance maintained")
    print("  - Ethical guidelines followed")
    
    print("\n📚 Educational Value:")
    print("  - Demonstrates attack vectors for awareness")
    print("  - Shows detection and prevention methods")
    print("  - Provides hands-on training experience")
    print("  - Enhances security awareness")
    print("  - Supports Red Team training programs")

if __name__ == "__main__":
    main() 