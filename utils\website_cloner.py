import requests
from bs4 import BeautifulSoup
import re
import os
from urllib.parse import urljoin, urlparse
import hashlib
import time

class WebsiteCloner:
    """Clone websites for phishing simulations with consent-based execution"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.cloned_content = {}
    
    def get_consent_status(self, target_url):
        """Check if website cloning is allowed (consent-based)"""
        try:
            # Check robots.txt
            robots_url = urljoin(target_url, '/robots.txt')
            robots_response = self.session.get(robots_url, timeout=10)
            
            if robots_response.status_code == 200:
                robots_content = robots_response.text.lower()
                if 'disallow: /' in robots_content or 'noindex' in robots_content:
                    return False, "Website disallows crawling"
            
            # Check for consent mechanisms
            response = self.session.get(target_url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check for privacy policy or terms of service
                privacy_links = soup.find_all('a', href=re.compile(r'privacy|terms|legal', re.I))
                if privacy_links:
                    return True, "Privacy policy found - proceed with caution"
                
                return True, "No explicit restrictions found"
            
            return False, f"HTTP {response.status_code}"
            
        except Exception as e:
            return False, f"Error checking consent: {str(e)}"
    
    def clone_website(self, target_url, max_depth=2, include_assets=True):
        """Clone a website for educational purposes"""
        
        # Check consent first
        consent_allowed, consent_message = self.get_consent_status(target_url)
        if not consent_allowed:
            raise Exception(f"Consent check failed: {consent_message}")
        
        print(f"Consent status: {consent_message}")
        print(f"Cloning {target_url} for educational purposes...")
        
        try:
            # Get main page
            response = self.session.get(target_url, timeout=15)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Store original content
            self.cloned_content['original_url'] = target_url
            self.cloned_content['title'] = soup.title.string if soup.title else 'Cloned Website'
            self.cloned_content['html'] = str(soup)
            self.cloned_content['assets'] = {}
            
            if include_assets:
                self._extract_assets(soup, target_url)
            
            # Modify content for educational purposes
            modified_html = self._modify_for_education(soup)
            self.cloned_content['modified_html'] = modified_html
            
            return {
                'success': True,
                'original_url': target_url,
                'cloned_content': self.cloned_content,
                'consent_message': consent_message
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'original_url': target_url
            }
    
    def _extract_assets(self, soup, base_url):
        """Extract and store website assets"""
        assets = {
            'css': [],
            'js': [],
            'images': [],
            'fonts': []
        }
        
        # Extract CSS files
        for css_link in soup.find_all('link', rel='stylesheet'):
            href = css_link.get('href')
            if href:
                full_url = urljoin(base_url, href)
                try:
                    css_response = self.session.get(full_url, timeout=10)
                    if css_response.status_code == 200:
                        assets['css'].append({
                            'url': full_url,
                            'content': css_response.text
                        })
                except:
                    pass
        
        # Extract JavaScript files
        for script in soup.find_all('script', src=True):
            src = script.get('src')
            if src:
                full_url = urljoin(base_url, src)
                try:
                    js_response = self.session.get(full_url, timeout=10)
                    if js_response.status_code == 200:
                        assets['js'].append({
                            'url': full_url,
                            'content': js_response.text
                        })
                except:
                    pass
        
        # Extract images
        for img in soup.find_all('img', src=True):
            src = img.get('src')
            if src:
                full_url = urljoin(base_url, src)
                try:
                    img_response = self.session.get(full_url, timeout=10)
                    if img_response.status_code == 200:
                        assets['images'].append({
                            'url': full_url,
                            'content': img_response.content,
                            'type': img_response.headers.get('content-type', 'image/jpeg')
                        })
                except:
                    pass
        
        self.cloned_content['assets'] = assets
    
    def _modify_for_education(self, soup):
        """Modify cloned content for educational purposes"""
        
        # Add educational warning
        warning_div = soup.new_tag('div')
        warning_div['style'] = 'background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px; border-radius: 5px;'
        warning_div['class'] = 'educational-warning'
        
        warning_content = soup.new_tag('p')
        warning_content['style'] = 'margin: 0; color: #856404; font-weight: bold;'
        warning_content.string = '⚠️ EDUCATIONAL PURPOSE: This is a cloned website for phishing awareness training. This is NOT the real website.'
        
        warning_div.append(warning_content)
        
        # Insert warning at the top
        if soup.body:
            soup.body.insert(0, warning_div)
        
        # Modify forms to capture data for educational purposes
        for form in soup.find_all('form'):
            form['action'] = '/api/phishing/capture'
            form['method'] = 'POST'
            
            # Add hidden fields for tracking
            campaign_id_input = soup.new_tag('input')
            campaign_id_input['type'] = 'hidden'
            campaign_id_input['name'] = 'campaign_id'
            campaign_id_input['value'] = 'educational'
            form.append(campaign_id_input)
        
        # Modify links to track clicks
        for link in soup.find_all('a', href=True):
            original_href = link.get('href')
            if original_href and not original_href.startswith('#'):
                link['onclick'] = f"trackClick('{original_href}'); return false;"
        
        return str(soup)
    
    def generate_clone_hash(self):
        """Generate hash of cloned content for tracking"""
        content_string = str(self.cloned_content)
        return hashlib.sha256(content_string.encode()).hexdigest()
    
    def save_clone(self, output_dir="cloned_sites"):
        """Save cloned website to local directory"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        clone_hash = self.generate_clone_hash()
        clone_dir = os.path.join(output_dir, clone_hash)
        
        if not os.path.exists(clone_dir):
            os.makedirs(clone_dir)
        
        # Save main HTML
        with open(os.path.join(clone_dir, 'index.html'), 'w', encoding='utf-8') as f:
            f.write(self.cloned_content['modified_html'])
        
        # Save assets
        assets_dir = os.path.join(clone_dir, 'assets')
        if not os.path.exists(assets_dir):
            os.makedirs(assets_dir)
        
        for asset_type, assets in self.cloned_content['assets'].items():
            asset_type_dir = os.path.join(assets_dir, asset_type)
            if not os.path.exists(asset_type_dir):
                os.makedirs(asset_type_dir)
            
            for i, asset in enumerate(assets):
                if asset_type == 'images':
                    ext = asset['type'].split('/')[-1] if '/' in asset['type'] else 'jpg'
                    filename = f"image_{i}.{ext}"
                    with open(os.path.join(asset_type_dir, filename), 'wb') as f:
                        f.write(asset['content'])
                else:
                    ext = 'css' if asset_type == 'css' else 'js'
                    filename = f"{asset_type}_{i}.{ext}"
                    with open(os.path.join(asset_type_dir, filename), 'w', encoding='utf-8') as f:
                        f.write(asset['content'])
        
        return clone_dir
    
    def create_phishing_simulation(self, target_url, campaign_name="Educational Training"):
        """Create a complete phishing simulation"""
        
        # Clone the website
        clone_result = self.clone_website(target_url)
        
        if not clone_result['success']:
            return clone_result
        
        # Save to local directory
        clone_path = self.save_clone()
        
        # Create simulation metadata
        simulation_data = {
            'campaign_name': campaign_name,
            'original_url': target_url,
            'clone_path': clone_path,
            'clone_hash': self.generate_clone_hash(),
            'created_at': time.time(),
            'educational_purpose': True,
            'consent_verified': True
        }
        
        return {
            'success': True,
            'simulation_data': simulation_data,
            'cloned_content': self.cloned_content
        }

# Example usage
if __name__ == "__main__":
    cloner = WebsiteCloner()
    
    # Example: Clone a simple website for educational purposes
    test_url = "https://httpbin.org/html"
    
    try:
        result = cloner.create_phishing_simulation(test_url, "Test Campaign")
        if result['success']:
            print("Simulation created successfully!")
            print(f"Clone hash: {result['simulation_data']['clone_hash']}")
            print(f"Saved to: {result['simulation_data']['clone_path']}")
        else:
            print(f"Failed to create simulation: {result}")
    except Exception as e:
        print(f"Error: {e}") 