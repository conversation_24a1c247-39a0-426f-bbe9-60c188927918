{% extends 'base.html' %}
{% block title %}Enhanced Social Engineering | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-secret text-warning"></i> ENHANCED SOCIAL ENGINEERING TOOLKIT</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> REAL SMTP CAPABILITIES
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Real SMTP Phishing Email Sender -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-envelope-open-text text-danger"></i> REAL SMTP PHISHING SENDER</h5>
                </div>
                <div class="card-body">
                    <form id="realPhishingForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">TARGET EMAIL</label>
                                    <input type="email" class="form-control" id="targetEmail" placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">TARGET FIRST NAME</label>
                                    <input type="text" class="form-control" id="firstName" placeholder="John" value="User">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">EMAIL TEMPLATE</label>
                                    <select class="form-select" id="emailTemplate" required>
                                        <option value="urgent_security">🚨 Urgent Security Alert</option>
                                        <option value="document_share">📄 Document Shared</option>
                                        <option value="invoice_payment">💰 Invoice Payment</option>
                                        <option value="system_update">🔧 System Update</option>
                                        <option value="hr_policy">👥 HR Policy Update</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDER NAME</label>
                                    <input type="text" class="form-control" id="senderName" placeholder="IT Administrator" value="IT Security Team">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDER EMAIL</label>
                                    <input type="email" class="form-control" id="senderEmail" placeholder="<EMAIL>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">PHISHING URL</label>
                                    <input type="url" class="form-control" id="phishingUrl" placeholder="https://fake-login.com" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">COMPANY NAME</label>
                                    <input type="text" class="form-control" id="companyName" placeholder="Target Company Inc." value="Acme Corporation">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SMTP SERVER</label>
                                    <input type="text" class="form-control" id="smtpServer" placeholder="smtp.gmail.com" value="smtp.gmail.com">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SMTP PORT</label>
                                    <input type="number" class="form-control" id="smtpPort" placeholder="587" value="587">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SMTP USERNAME</label>
                                    <input type="email" class="form-control" id="smtpUsername" placeholder="<EMAIL>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SMTP PASSWORD</label>
                                    <input type="password" class="form-control" id="smtpPassword" placeholder="App Password" required>
                                    <div class="form-text">Use app-specific password for Gmail</div>
                                </div>
                            </div>
                        </div>

                        <!-- PDF Attachment Options -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-danger mb-3">
                                    <div class="card-header bg-danger text-white">
                                        <h6><i class="fas fa-file-pdf"></i> PDF ATTACHMENT OPTIONS</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includePdfAttachment">
                                                    <label class="form-check-label" for="includePdfAttachment">
                                                        <strong>Include PDF Attachment</strong>
                                                    </label>
                                                </div>
                                                <small class="text-muted">Attach spoofed PDF that redirects to login page</small>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">DOCUMENT TYPE</label>
                                                <select class="form-select form-select-sm" id="documentType">
                                                    <option value="invoice">📄 Invoice</option>
                                                    <option value="contract">📋 Contract</option>
                                                    <option value="report">📊 Security Report</option>
                                                    <option value="statement">💰 Account Statement</option>
                                                    <option value="policy">📜 Policy Document</option>
                                                    <option value="proposal">💼 Business Proposal</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">TARGET SERVICE</label>
                                                <select class="form-select form-select-sm" id="targetService">
                                                    <option value="gmail">Gmail</option>
                                                    <option value="outlook">Outlook/Office 365</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="alert alert-info mt-2 mb-0">
                                            <small><i class="fas fa-info-circle"></i> <strong>How it works:</strong> The PDF attachment contains JavaScript that redirects users to a spoofed login page when opened in a PDF viewer.</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Real SMTP Sending:</strong> This will send actual emails using your SMTP configuration.
                            Ensure you have proper authorization before sending phishing emails.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-paper-plane"></i> SEND PHISHING EMAIL
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="generateEmailPreview()">
                                <i class="fas fa-eye"></i> PREVIEW EMAIL
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="generateEmailList()">
                                <i class="fas fa-list"></i> GENERATE EMAIL LIST
                            </button>
                        </div>
                    </form>
                    
                    <div id="phishingResult" class="mt-4" style="display: none;">
                        <h6>EMAIL SENDING RESULT:</h6>
                        <div class="alert" id="resultAlert">
                            <div id="resultMessage"></div>
                        </div>
                    </div>
                    
                    <div id="emailPreview" class="mt-4" style="display: none;">
                        <h6>EMAIL PREVIEW:</h6>
                        <div class="alert alert-dark">
                            <div id="previewContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line text-success"></i> CAMPAIGN STATS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Emails Sent:</span>
                            <span class="badge bg-primary" id="emailsSent">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Links Clicked:</span>
                            <span class="badge bg-warning" id="linksClicked">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Credentials Captured:</span>
                            <span class="badge bg-success" id="credentialsCaptured">0</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>SUCCESS RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 75%">75%</div>
                    </div>
                    <small class="text-muted">Average success rate</small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb text-warning"></i> SMTP TIPS</h5>
                </div>
                <div class="card-body">
                    <ul class="small">
                        <li><strong>Gmail:</strong> Use app-specific passwords</li>
                        <li><strong>Outlook:</strong> Enable SMTP authentication</li>
                        <li><strong>Custom SMTP:</strong> Check port and TLS settings</li>
                        <li><strong>Rate Limiting:</strong> Send emails slowly to avoid detection</li>
                        <li><strong>SPF/DKIM:</strong> Configure DNS records for authenticity</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Email List Generator -->
    <div class="row">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-users"></i> EMAIL LIST GENERATOR</h5>
                </div>
                <div class="card-body" id="emailListSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Generated Email List:</h6>
                            <div class="alert alert-light">
                                <textarea id="generatedEmailList" class="form-control" rows="10" readonly style="color: #000;"></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>List Configuration:</h6>
                            <div class="mb-3">
                                <label class="form-label">Domain</label>
                                <input type="text" class="form-control" id="targetDomain" placeholder="company.com" value="company.com">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Count</label>
                                <input type="number" class="form-control" id="emailCount" placeholder="100" value="50" min="1" max="1000">
                            </div>
                            <button class="btn btn-info w-100" onclick="generateRealisticEmailList()">
                                <i class="fas fa-magic"></i> GENERATE LIST
                            </button>
                            <button class="btn btn-outline-dark w-100 mt-2" onclick="copyEmailList()">
                                <i class="fas fa-copy"></i> COPY LIST
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let emailsSentCount = 0;

document.getElementById('realPhishingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_email: document.getElementById('targetEmail').value,
        first_name: document.getElementById('firstName').value,
        template_id: document.getElementById('emailTemplate').value,
        sender_name: document.getElementById('senderName').value,
        sender_email: document.getElementById('senderEmail').value,
        phishing_link: document.getElementById('phishingUrl').value,
        company: document.getElementById('companyName').value,
        smtp_server: document.getElementById('smtpServer').value,
        smtp_port: parseInt(document.getElementById('smtpPort').value),
        smtp_username: document.getElementById('smtpUsername').value,
        smtp_password: document.getElementById('smtpPassword').value,
        subject: getSubjectForTemplate(document.getElementById('emailTemplate').value),
        include_pdf_attachment: document.getElementById('includePdfAttachment').checked,
        document_type: document.getElementById('documentType').value,
        target_service: document.getElementById('targetService').value
    };
    
    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> SENDING...';
    submitBtn.disabled = true;
    
    fetch('/api/send_phishing_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('phishingResult');
        const alertDiv = document.getElementById('resultAlert');
        const messageDiv = document.getElementById('resultMessage');
        
        if (data.success) {
            alertDiv.className = 'alert alert-success';
            let successMessage = '<i class="fas fa-check"></i> <strong>Email sent successfully!</strong><br>Target: ' + document.getElementById('targetEmail').value;

            // Add PDF attachment info if included
            if (data.attachment_info) {
                successMessage += '<br><br><i class="fas fa-file-pdf text-danger"></i> <strong>PDF Attachment:</strong><br>';
                successMessage += '📄 Filename: ' + data.attachment_info.filename + '<br>';
                successMessage += '🎯 Document Type: ' + data.attachment_info.document_type + '<br>';
                successMessage += '🔗 Redirect ID: ' + data.attachment_info.redirect_id + '<br>';
                successMessage += '<small class="text-warning">⚠️ PDF will redirect to login page when opened</small>';
            }

            messageDiv.innerHTML = successMessage;

            // Update statistics
            emailsSentCount++;
            document.getElementById('emailsSent').textContent = emailsSentCount;
        } else {
            alertDiv.className = 'alert alert-danger';
            messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Failed to send email:</strong><br>' + data.error;
        }
        
        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        const resultDiv = document.getElementById('phishingResult');
        const alertDiv = document.getElementById('resultAlert');
        const messageDiv = document.getElementById('resultMessage');
        
        alertDiv.className = 'alert alert-danger';
        messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Error:</strong> ' + error;
        resultDiv.style.display = 'block';
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function getSubjectForTemplate(templateId) {
    const subjects = {
        'urgent_security': 'URGENT: Security Alert - Immediate Action Required',
        'document_share': 'Document Shared - Review Required',
        'invoice_payment': 'Invoice Payment Due - Action Required',
        'system_update': 'Critical System Update Required',
        'hr_policy': 'New HR Policy - Mandatory Review Required'
    };
    return subjects[templateId] || 'Important Notice';
}

function generateEmailPreview() {
    const templateId = document.getElementById('emailTemplate').value;
    const firstName = document.getElementById('firstName').value;
    const companyName = document.getElementById('companyName').value;
    const phishingUrl = document.getElementById('phishingUrl').value;
    
    const templates = {
        'urgent_security': `
Subject: URGENT: Security Alert - Immediate Action Required

Dear ${firstName},

Our security systems have detected suspicious activity on your account. 
Immediate verification is required to prevent account suspension.

Account: ${document.getElementById('targetEmail').value}
Alert Time: ${new Date().toLocaleString()}
Risk Level: HIGH

Please verify your account immediately by clicking the link below:
${phishingUrl}

This verification must be completed within 24 hours to maintain account access.

Best regards,
IT Security Team
${companyName} Information Security Department
`,
        'document_share': `
Subject: Document Shared - Review Required

Hello ${firstName},

${document.getElementById('senderName').value} has shared an important document with you that requires your review.

Document: Confidential_Report_${Math.floor(Math.random() * 1000)}.pdf
Shared by: ${document.getElementById('senderName').value}
Access Level: Confidential
Expires: ${new Date(Date.now() + 7*24*60*60*1000).toLocaleDateString()}

Access Document: ${phishingUrl}

Note: You may need to sign in to verify your identity before accessing this confidential document.

Best regards,
Document Management System
${companyName}
`
    };
    
    const preview = templates[templateId] || templates['urgent_security'];
    document.getElementById('previewContent').innerHTML = '<pre style="color: #fff; white-space: pre-wrap;">' + preview + '</pre>';
    document.getElementById('emailPreview').style.display = 'block';
    document.getElementById('emailPreview').scrollIntoView({ behavior: 'smooth' });
}

function generateEmailList() {
    document.getElementById('emailListSection').style.display = 'block';
    document.getElementById('emailListSection').scrollIntoView({ behavior: 'smooth' });
}

function generateRealisticEmailList() {
    const domain = document.getElementById('targetDomain').value;
    const count = parseInt(document.getElementById('emailCount').value);
    
    const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Ashley', 'Chris', 'Amanda', 'Daniel', 'Jessica', 'Matthew'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson'];
    
    let emailList = [];
    
    for (let i = 0; i < count; i++) {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
        
        // Generate realistic email formats
        const formats = [
            `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`,
            `${firstName.toLowerCase()}${lastName.toLowerCase()}@${domain}`,
            `${firstName[0].toLowerCase()}${lastName.toLowerCase()}@${domain}`,
            `${firstName.toLowerCase()}${lastName[0].toLowerCase()}@${domain}`,
            `${firstName.toLowerCase()}${Math.floor(Math.random() * 99)}@${domain}`
        ];
        
        const email = formats[Math.floor(Math.random() * formats.length)];
        emailList.push(email);
    }
    
    // Remove duplicates
    emailList = [...new Set(emailList)];
    
    document.getElementById('generatedEmailList').value = emailList.join('\n');
}

function copyEmailList() {
    const emailList = document.getElementById('generatedEmailList');
    emailList.select();
    document.execCommand('copy');
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}
</script>

{% endblock %}
