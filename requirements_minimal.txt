# AMADIOHA-M257 Minimal Requirements (Core functionality)
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1
python-dotenv==1.0.0
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3

# Twilio (Essential for phone operations)
twilio==8.10.0

# Phone validation
phonenumbers==8.13.22

# Security
cryptography==41.0.4
bcrypt==4.0.1

# Web scraping
fake-useragent==1.4.0

# Scheduling
schedule==1.2.0

# Network tools (optional - will install if available)
dnspython==2.4.2

# Date utilities
python-dateutil==2.8.2

# Email validation
email-validator==2.0.0
