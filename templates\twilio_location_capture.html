{% extends 'base.html' %}
{% block title %}Twilio Location Capture | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-phone-alt text-primary"></i> TWILIO LOCATION CAPTURE</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Target Configuration -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-crosshairs"></i> Target Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="targetPhone" class="form-label">Target Phone Number</label>
                        <input type="text" class="form-control" id="targetPhone" placeholder="+2349063978612" required>
                        <div class="form-text">Nigerian format: +234803... or 0803...</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="captureType" class="form-label">Capture Method</label>
                        <select class="form-control" id="captureType">
                            <option value="emergency_verification">💰 Unsubscribe High Data Charges</option>
                            <option value="network_survey">📊 Network Quality Survey</option>
                            <option value="delivery_confirmation">📦 Delivery Confirmation</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="callLanguage" class="form-label">Call Language</label>
                        <select class="form-control" id="callLanguage">
                            <option value="english">🇬🇧 English</option>
                            <option value="pidgin">🇳🇬 Nigerian Pidgin</option>
                        </select>
                    </div>
                    
                    <button type="button" class="btn btn-danger w-100" id="initiateCallBtn">
                        <i class="fas fa-phone"></i> INITIATE LOCATION CAPTURE CALL
                    </button>
                </div>
            </div>

            <!-- Call Status -->
            <div class="card mb-4" id="callStatusCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-phone-volume"></i> Call Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2"><strong>Call SID:</strong> <span id="callSid" class="font-monospace">-</span></div>
                    <div class="mb-2"><strong>Status:</strong> <span id="callStatus" class="badge bg-secondary">-</span></div>
                    <div class="mb-2"><strong>Duration:</strong> <span id="callDuration">-</span></div>
                    <div class="mb-2"><strong>Recording:</strong> <span id="callRecording">-</span></div>
                </div>
            </div>
        </div>

        <!-- Call Scripts Preview -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-script"></i> Call Script Preview</h5>
                </div>
                <div class="card-body">
                    <div id="scriptPreview" class="alert alert-info">
                        Select a capture method to see the call script preview
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>📍 Expected Responses:</h6>
                            <div id="expectedResponses">
                                <div class="mb-1">Press 1 → Unsubscribe (Lagos State)</div>
                                <div class="mb-1">Press 2 → Unsubscribe (Abuja FCT)</div>
                                <div class="mb-1">Press 3 → Unsubscribe (Other State)</div>
                                <div class="mb-1">Press 0 → Speak with Customer Service</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>🎯 Success Probability:</h6>
                            <div id="successProbability">
                                <div class="mb-1">📞 Answer Rate: 85%</div>
                                <div class="mb-1">📍 Location Response: 90%</div>
                                <div class="mb-1">🎯 Accuracy: State-level</div>
                                <div class="mb-1">⏱️ Duration: 60-90 seconds</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Capture Results -->
            <div class="card mb-4" id="locationResultsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-map-marker-alt"></i> Location Capture Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2"><strong>📱 Phone Number:</strong> <span id="resultPhone">-</span></div>
                            <div class="mb-2"><strong>🔢 User Response:</strong> <span id="userResponse" class="badge bg-success">-</span></div>
                            <div class="mb-2"><strong>📍 Captured Location:</strong> <span id="capturedLocation" class="text-primary">-</span></div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2"><strong>⏰ Response Time:</strong> <span id="responseTime">-</span></div>
                            <div class="mb-2"><strong>🎯 Confidence:</strong> <span id="locationConfidence" class="badge bg-warning">-</span></div>
                            <div class="mb-2"><strong>📊 Method:</strong> <span id="captureMethod">-</span></div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>🔍 Additional Analysis:</h6>
                        <div id="additionalAnalysis" class="alert alert-secondary">
                            Waiting for call completion...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Capture History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Location Capture History</h5>
                    <button class="btn btn-sm btn-outline-primary" id="refreshHistoryBtn">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Phone Number</th>
                                    <th>Capture Type</th>
                                    <th>Language</th>
                                    <th>Response</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Duration</th>
                                    <th>Initiated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="captureHistoryBody">
                                <!-- History will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentCallSid = null;
let statusCheckInterval = null;

// Call scripts for different capture types
const callScripts = {
    emergency_verification: {
        english: `"Hello, this is MTN Nigeria customer service. We have noticed that your line is subscribed to high data bundle charges that are costing you extra money. We can help you unsubscribe from these charges. Press 1 to unsubscribe if you are in Lagos State, Press 2 for Abuja, Press 3 for other state, Press 0 for customer service."`,
        pidgin: `"Hello, na MTN Nigeria customer service be dis. We notice say your line dey use plenty data bundle wey dey cost you extra money. We fit help you stop dis high charges. Press 1 to unsubscribe if you dey Lagos, Press 2 if you dey Abuja, Press 3 if you dey another state."`
    },
    network_survey: {
        english: `"Hello, this is MTN Nigeria customer service. We are conducting a network quality survey in your area. Press 1 for Victoria Island/Ikoyi, Press 2 for Ikeja/Allen Avenue, Press 3 for Surulere/Yaba, Press 4 for Lekki/Ajah, Press 5 for other Lagos areas, Press 0 for outside Lagos."`,
        pidgin: `"Hello, na MTN Nigeria customer service be dis. We dey do network survey for your area. Press number wey match where you dey now."`
    },
    delivery_confirmation: {
        english: `"Hello, this is Jumia delivery service. We have a package for delivery to your location today. Press 1 if you are at home, Press 2 if you are at office, Press 3 if you are at different location, Press 0 to speak with delivery agent."`,
        pidgin: `"Hello, na Jumia delivery service be dis. We get package wey we wan deliver give you today. Press 1 if you dey house, Press 2 if you dey office."`
    }
};

// Update script preview when capture type or language changes
document.getElementById('captureType').addEventListener('change', updateScriptPreview);
document.getElementById('callLanguage').addEventListener('change', updateScriptPreview);

function updateScriptPreview() {
    const captureType = document.getElementById('captureType').value;
    const language = document.getElementById('callLanguage').value;
    const preview = document.getElementById('scriptPreview');
    
    if (callScripts[captureType] && callScripts[captureType][language]) {
        preview.innerHTML = callScripts[captureType][language];
        preview.className = 'alert alert-warning';
    } else {
        preview.innerHTML = 'Select a capture method to see the call script preview';
        preview.className = 'alert alert-info';
    }
    
    // Update expected responses based on capture type
    updateExpectedResponses(captureType);
}

function updateExpectedResponses(captureType) {
    const responsesDiv = document.getElementById('expectedResponses');
    
    if (captureType === 'emergency_verification') {
        responsesDiv.innerHTML = `
            <div class="mb-1">Press 1 → Unsubscribe (Lagos State)</div>
            <div class="mb-1">Press 2 → Unsubscribe (Abuja FCT)</div>
            <div class="mb-1">Press 3 → Unsubscribe (Other State)</div>
            <div class="mb-1">Press 0 → Speak with Customer Service</div>
        `;
    } else if (captureType === 'network_survey') {
        responsesDiv.innerHTML = `
            <div class="mb-1">Press 1 → Victoria Island/Ikoyi</div>
            <div class="mb-1">Press 2 → Ikeja/Allen Avenue</div>
            <div class="mb-1">Press 3 → Surulere/Yaba</div>
            <div class="mb-1">Press 4 → Lekki/Ajah</div>
            <div class="mb-1">Press 5 → Other Lagos Areas</div>
            <div class="mb-1">Press 0 → Outside Lagos</div>
        `;
    } else if (captureType === 'delivery_confirmation') {
        responsesDiv.innerHTML = `
            <div class="mb-1">Press 1 → At Home Address</div>
            <div class="mb-1">Press 2 → At Office Address</div>
            <div class="mb-1">Press 3 → Different Location</div>
            <div class="mb-1">Press 0 → Speak with Agent</div>
        `;
    }
}

// Initiate location capture call
document.getElementById('initiateCallBtn').addEventListener('click', function() {
    const phoneNumber = document.getElementById('targetPhone').value;
    const captureType = document.getElementById('captureType').value;
    const language = document.getElementById('callLanguage').value;
    
    if (!phoneNumber) {
        alert('Please enter a phone number');
        return;
    }
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Initiating Call...';
    this.disabled = true;
    
    fetch('/api/twilio/location_call', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: phoneNumber,
            capture_type: captureType,
            language: language
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentCallSid = data.call_sid;
            displayCallStatus(data);
            startStatusMonitoring();
            alert('Location capture call initiated successfully!');
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to initiate location capture call');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-phone"></i> INITIATE LOCATION CAPTURE CALL';
        this.disabled = false;
    });
});

function displayCallStatus(data) {
    document.getElementById('callSid').textContent = data.call_sid;
    document.getElementById('callStatus').textContent = data.status;
    document.getElementById('callStatus').className = `badge bg-${getStatusColor(data.status)}`;
    document.getElementById('callDuration').textContent = data.duration || 'In progress...';
    document.getElementById('callRecording').textContent = data.recording ? 'Enabled' : 'Disabled';
    
    document.getElementById('callStatusCard').style.display = 'block';
}

function startStatusMonitoring() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(() => {
        if (currentCallSid) {
            checkCallStatus(currentCallSid);
        }
    }, 5000); // Check every 5 seconds
}

function checkCallStatus(callSid) {
    fetch(`/api/twilio/call_status/${callSid}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCallStatus(data);
            
            // Stop monitoring if call is completed
            if (data.status === 'completed' || data.status === 'failed') {
                clearInterval(statusCheckInterval);
                loadLocationResults(callSid);
            }
        }
    })
    .catch(error => console.error('Status check error:', error));
}

function updateCallStatus(data) {
    document.getElementById('callStatus').textContent = data.status;
    document.getElementById('callStatus').className = `badge bg-${getStatusColor(data.status)}`;
    
    if (data.duration) {
        document.getElementById('callDuration').textContent = `${data.duration} seconds`;
    }
}

function loadLocationResults(callSid) {
    // Load location capture results from database
    fetch('/api/location_capture_history')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.location_captures.length > 0) {
            const latestCapture = data.location_captures.find(c => c.call_sid === callSid);
            if (latestCapture && latestCapture.captured_location) {
                displayLocationResults(latestCapture);
            }
        }
    })
    .catch(error => console.error('Location results error:', error));
}

function displayLocationResults(capture) {
    document.getElementById('resultPhone').textContent = capture.phone_number;
    document.getElementById('userResponse').textContent = capture.user_response || 'No response';
    document.getElementById('capturedLocation').textContent = capture.captured_location || 'Location not captured';
    document.getElementById('responseTime').textContent = capture.duration ? `${capture.duration}s` : 'Unknown';
    document.getElementById('locationConfidence').textContent = capture.captured_location ? 'High' : 'Low';
    document.getElementById('captureMethod').textContent = capture.capture_type.replace('_', ' ').toUpperCase();
    
    // Additional analysis
    let analysis = 'Call completed successfully. ';
    if (capture.captured_location) {
        analysis += `Target confirmed location: ${capture.captured_location}. `;
        analysis += 'Location capture successful with high confidence.';
    } else {
        analysis += 'No location response received. Target may be suspicious or call quality issues.';
    }
    
    document.getElementById('additionalAnalysis').innerHTML = analysis;
    document.getElementById('additionalAnalysis').className = capture.captured_location ? 'alert alert-success' : 'alert alert-warning';
    
    document.getElementById('locationResultsCard').style.display = 'block';
}

function getStatusColor(status) {
    const colors = {
        'initiated': 'primary',
        'ringing': 'info',
        'answered': 'success',
        'completed': 'success',
        'failed': 'danger',
        'busy': 'warning',
        'no-answer': 'secondary'
    };
    return colors[status] || 'secondary';
}

// Load capture history on page load
document.addEventListener('DOMContentLoaded', function() {
    updateScriptPreview();
    loadCaptureHistory();
});

function loadCaptureHistory() {
    fetch('/api/location_capture_history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCaptureHistory(data.location_captures);
        }
    })
    .catch(error => console.error('History load error:', error));
}

function displayCaptureHistory(captures) {
    const tbody = document.getElementById('captureHistoryBody');
    tbody.innerHTML = '';
    
    captures.slice(0, 20).forEach(capture => {
        const row = tbody.insertRow();
        
        row.innerHTML = `
            <td class="font-monospace">${capture.phone_number}</td>
            <td>${capture.capture_type.replace('_', ' ')}</td>
            <td>${capture.language}</td>
            <td><span class="badge bg-info">${capture.user_response || 'No response'}</span></td>
            <td>${capture.captured_location || 'Not captured'}</td>
            <td><span class="badge bg-${getStatusColor(capture.status)}">${capture.status}</span></td>
            <td>${capture.duration ? capture.duration + 's' : '-'}</td>
            <td>${new Date(capture.initiated_at).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewCaptureDetails('${capture.call_sid}')">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
    });
}

function viewCaptureDetails(callSid) {
    // View detailed capture information
    alert(`View details for call: ${callSid}`);
}

// Refresh history button
document.getElementById('refreshHistoryBtn').addEventListener('click', loadCaptureHistory);
</script>

{% endblock %}
