{% extends "base.html" %}

{% block title %}Campaigns - AMADIOHA-M257{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-bullhorn"></i> Campaigns</h2>
            <a href="{{ url_for('new_campaign') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Campaign
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> All Campaigns</h5>
                </div>
                <div class="card-body">
                    {% if campaigns %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Target URL</th>
                                        <th>Created</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign in campaigns %}
                                    <tr>
                                        <td>
                                            <strong>{{ campaign.name }}</strong>
                                            {% if campaign.description %}
                                                <br><small class="text-secondary">{{ campaign.description }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ campaign.template_type or 'N/A' }}</span>
                                        </td>
                                        <td>
                                            {% if campaign.target_url %}
                                                <a href="{{ campaign.target_url }}" target="_blank" class="text-primary">
                                                    {{ campaign.target_url[:50] }}...
                                                </a>
                                            {% else %}
                                                <span class="text-secondary">N/A</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ campaign.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if campaign.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bullhorn fa-3x text-secondary mb-3"></i>
                            <h5 class="text-secondary">No campaigns yet</h5>
                            <p class="text-secondary">Create your first campaign to get started</p>
                            <a href="{{ url_for('new_campaign') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Campaign
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
