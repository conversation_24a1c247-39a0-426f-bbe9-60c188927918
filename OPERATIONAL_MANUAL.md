# 📖 CYBER-OPS PLATFORM OPERATIONAL MANUAL

## 🎯 **PLATFORM OVERVIEW**

The CYBER-OPS platform is a comprehensive cybersecurity testing environment that provides:
- **Real functional malware** (keyloggers, credential stealers)
- **Advanced phishing systems** (perfect login replicas, PDF attacks)
- **Network reconnaissance tools**
- **Vulnerability exploitation capabilities**
- **Real-time monitoring and notifications**

---

## 🚀 **GETTING STARTED**

### **Initial Access**
1. Navigate to: `https://your-domain.com`
2. Login with admin credentials
3. Complete initial setup wizard
4. Configure security settings

### **Dashboard Overview**
- **Active Sessions**: Monitor deployed malware
- **Credential Harvesting**: View captured credentials
- **Campaign Statistics**: Track phishing success rates
- **System Health**: Monitor platform status

---

## 🦠 **MALWARE DEPLOYMENT**

### **Real Keylogger Deployment**

#### **Step 1: Access Deployment Center**
```
Navigation: Real Malware Deployment → Keylogger Section
```

#### **Step 2: Configure Keylogger**
- **Target OS**: Windows/Linux/macOS
- **Callback URL**: C2 server endpoint
- **Persistence**: Enable for long-term access
- **Stealth Mode**: Enable anti-detection features

#### **Step 3: Generate Payload**
```python
# Example configuration
{
    "target_os": "windows",
    "callback_url": "https://your-domain.com/api/keylog_callback",
    "persistence": true,
    "stealth_mode": true
}
```

#### **Step 4: Deploy and Monitor**
1. Download generated payload
2. Deploy to target systems (with authorization)
3. Monitor active sessions in dashboard
4. Review captured data in real-time

### **Browser Credential Stealer**

#### **Configuration Options**
- **Target Browsers**: Chrome, Firefox, Edge
- **Extraction Methods**: Passwords, cookies, sessions
- **Exfiltration**: Real-time or batch upload

#### **Deployment Process**
1. Select target browsers
2. Configure exfiltration settings
3. Generate stealer payload
4. Deploy and monitor results

---

## 🎣 **PHISHING OPERATIONS**

### **Real SMTP Phishing Campaigns**

#### **Step 1: Configure SMTP**
```
Navigation: Enhanced Social Engineering → SMTP Configuration
```

Required settings:
- **SMTP Server**: smtp.gmail.com
- **Port**: 587 (TLS)
- **Username**: <EMAIL>
- **Password**: App-specific password

#### **Step 2: Create Email Campaign**
- **Target Email**: <EMAIL>
- **Template**: Choose from 5 professional templates
- **Sender Spoofing**: Configure sender name/email
- **PDF Attachment**: Optional malicious PDF

#### **Step 3: Launch Campaign**
1. Review email preview
2. Test SMTP connectivity
3. Send phishing email
4. Monitor click-through rates

### **PDF Attachment Attacks**

#### **PDF Generation Process**
```
Navigation: PDF Attachment Generator
```

Configuration:
- **Document Type**: Invoice, Contract, Report, etc.
- **Target Service**: Gmail, Outlook
- **Redirect Delay**: 1-5 seconds

#### **PDF Capabilities**
- **JavaScript Execution**: Runs when PDF is opened
- **Anti-Analysis**: VM and sandbox detection
- **Browser Redirect**: Opens phishing page automatically
- **Credential Harvesting**: Integrates with login replicas

### **Perfect Login Replicas**

#### **Automatic Generation**
The platform automatically generates pixel-perfect replicas:
- **Gmail Login**: Identical to accounts.google.com
- **Outlook Login**: Exact Microsoft login page
- **Custom Providers**: Configurable for other services

#### **Features**
- **Real-time Harvesting**: Captures credentials as typed
- **Browser Fingerprinting**: Collects detailed system info
- **Stored Credential Access**: Attempts to access saved passwords
- **Anti-Developer Tools**: Prevents inspection

---

## 🔍 **NETWORK RECONNAISSANCE**

### **Subdomain Enumeration**

#### **Methods Available**
- **DNS Bruteforce**: Dictionary-based discovery
- **Certificate Transparency**: CT log searches
- **Search Engine**: Google/Bing dorking

#### **Usage**
```
Navigation: Network Reconnaissance → Subdomain Enumeration
```

Configuration:
- **Target Domain**: company.com
- **Method**: DNS bruteforce (recommended)
- **Threads**: 50 (adjust based on target)

### **Advanced Port Scanning**

#### **Scan Types**
- **Stealth SYN**: Stealthy connection attempts
- **Connect Scan**: Full TCP connections
- **UDP Scan**: UDP service discovery

#### **Configuration**
- **Target**: IP address or hostname
- **Port Range**: 1-1000 (or custom)
- **Timing**: Aggressive/Normal/Polite

---

## 🛡️ **VULNERABILITY EXPLOITATION**

### **2025 CVE Database**

#### **Available CVEs**
The platform includes 10 critical 2025 vulnerabilities:
- **CVE-2025-21311**: Windows NTLMv1 bypass
- **CVE-2025-24085**: Apple Core Media RCE
- **CVE-2025-0282/0283**: Ivanti Connect Secure
- **CVE-2025-1337**: Chrome V8 Use-After-Free
- **CVE-2025-5555**: Exchange Server RCE

#### **Exploit Generation**
1. Search CVE database
2. Select target vulnerability
3. Configure target information
4. Generate working exploit code

### **Advanced Payload Generation**

#### **Payload Types**
- **Reverse Shells**: Python, PowerShell, Bash
- **Keyloggers**: Multi-threaded with persistence
- **Credential Stealers**: Browser-specific extraction

#### **Evasion Techniques**
- **AES-256 Encryption**: Military-grade payload encryption
- **Polymorphic Code**: Runtime code mutation
- **Anti-VM Detection**: Comprehensive evasion
- **Process Hollowing**: Advanced injection techniques

---

## 📊 **MONITORING & ANALYTICS**

### **Real-time Dashboard**

#### **Key Metrics**
- **Active Malware Sessions**: Live keylogger/stealer instances
- **Credentials Harvested**: Real-time credential capture
- **Phishing Success Rate**: Campaign effectiveness
- **Network Scan Results**: Discovered services/vulnerabilities

#### **Alert System**
- **Telegram Integration**: Real-time notifications
- **Email Alerts**: Critical event notifications
- **Dashboard Alerts**: In-platform notifications

### **Data Analysis**

#### **Credential Analysis**
- **Provider Breakdown**: Gmail, Outlook, etc.
- **Password Strength**: Complexity analysis
- **Reuse Patterns**: Cross-platform password reuse

#### **Campaign Effectiveness**
- **Open Rates**: Email opening statistics
- **Click Rates**: Link/attachment interaction
- **Conversion Rates**: Credential submission rates

---

## 🔧 **ADVANCED FEATURES**

### **Ngrok Integration**

#### **Tunnel Management**
```
Navigation: Advanced Tools → Ngrok Tunnels
```

Features:
- **HTTP/TCP Tunnels**: Public URL generation
- **Dynamic URLs**: Automatic URL retrieval
- **Tunnel Monitoring**: Active tunnel tracking

#### **Use Cases**
- **C2 Communication**: Malware callback URLs
- **Phishing Hosting**: Public phishing page access
- **Payload Delivery**: Malware distribution

### **Telegram Bot Integration**

#### **Setup Process**
1. Create Telegram bot via @BotFather
2. Get bot token and chat ID
3. Configure in platform settings
4. Test connectivity

#### **Notification Types**
- **Malware Callbacks**: New session alerts
- **Credential Capture**: Real-time harvesting alerts
- **PDF Access**: Document opening notifications
- **System Alerts**: Platform status updates

---

## 🛡️ **SECURITY OPERATIONS**

### **Access Control**

#### **User Management**
- **Role-based Access**: Admin, Operator, Viewer
- **Session Management**: Timeout and security
- **Audit Logging**: All actions logged

#### **IP Whitelisting**
Configure allowed IP ranges:
```python
ALLOWED_IPS = [
    '***********/24',  # Internal network
    '10.0.0.0/8',      # VPN network
]
```

### **Data Protection**

#### **Encryption**
- **AES-256-GCM**: All sensitive data encrypted
- **Key Management**: Secure key storage
- **Transport Security**: HTTPS/TLS encryption

#### **Data Retention**
- **Automatic Cleanup**: Configurable retention periods
- **Secure Deletion**: Cryptographic data wiping
- **Backup Procedures**: Encrypted backup storage

---

## 🚨 **INCIDENT RESPONSE**

### **Detection Procedures**

#### **Monitoring Checklist**
- [ ] Unusual login attempts
- [ ] Unexpected network traffic
- [ ] System performance degradation
- [ ] Unauthorized access attempts

#### **Alert Thresholds**
- **Failed Logins**: > 5 attempts in 10 minutes
- **Data Exfiltration**: > 100MB in 1 hour
- **Session Anomalies**: Unusual geographic access

### **Response Procedures**

#### **Immediate Actions**
1. **Isolate Systems**: Disconnect from network
2. **Preserve Evidence**: Capture logs and memory
3. **Notify Stakeholders**: Alert security team
4. **Document Incident**: Record all actions

#### **Recovery Steps**
1. **Assess Damage**: Determine impact scope
2. **Restore Services**: From clean backups
3. **Update Security**: Patch vulnerabilities
4. **Lessons Learned**: Improve procedures

---

## 📋 **OPERATIONAL PROCEDURES**

### **Daily Operations**

#### **Morning Checklist**
- [ ] Check system status
- [ ] Review overnight alerts
- [ ] Verify active sessions
- [ ] Update threat intelligence

#### **Monitoring Tasks**
- [ ] Review credential harvesting
- [ ] Check phishing campaign status
- [ ] Monitor network scans
- [ ] Verify backup completion

### **Weekly Operations**

#### **Maintenance Tasks**
- [ ] Update CVE database
- [ ] Review security logs
- [ ] Performance optimization
- [ ] Documentation updates

#### **Security Reviews**
- [ ] Access control audit
- [ ] Configuration review
- [ ] Vulnerability assessment
- [ ] Incident analysis

---

## 🎯 **BEST PRACTICES**

### **Operational Security**

#### **Access Management**
- Use strong, unique passwords
- Enable two-factor authentication
- Regular access reviews
- Principle of least privilege

#### **Data Handling**
- Encrypt all sensitive data
- Secure data transmission
- Proper data classification
- Regular data purging

### **Campaign Management**

#### **Phishing Campaigns**
- Obtain proper authorization
- Use realistic scenarios
- Monitor success rates
- Provide security awareness training

#### **Malware Deployment**
- Test in isolated environments
- Monitor system impact
- Maintain stealth operations
- Document all activities

---

## 📞 **TROUBLESHOOTING**

### **Common Issues**

#### **Platform Access Problems**
```bash
# Check service status
sudo systemctl status cyber-ops

# Review logs
tail -f /opt/cyber-ops/logs/cyber-ops.log

# Test connectivity
curl -k https://localhost:5000/health
```

#### **Malware Communication Issues**
- Verify callback URLs
- Check firewall rules
- Test network connectivity
- Review Ngrok tunnels

#### **Phishing Campaign Problems**
- Verify SMTP credentials
- Check email templates
- Test PDF generation
- Monitor delivery rates

### **Performance Optimization**

#### **Database Optimization**
```sql
-- Clean old sessions
DELETE FROM malware_logs WHERE created_at < DATE('now', '-30 days');

-- Optimize database
VACUUM;
ANALYZE;
```

#### **System Tuning**
- Monitor resource usage
- Optimize database queries
- Configure caching
- Load balancing setup

---

## 📚 **REFERENCE MATERIALS**

### **API Documentation**
- **Malware Deployment**: `/api/deploy_keylogger`
- **Credential Harvesting**: `/api/harvest_credentials`
- **Phishing Campaigns**: `/api/send_phishing_email`
- **Network Scanning**: `/api/port_scan`

### **Configuration Files**
- **Main Config**: `config.py`
- **Database**: `database.db`
- **Logs**: `/opt/cyber-ops/logs/`
- **Templates**: `/templates/`

### **External Resources**
- **CVE Database**: https://cve.mitre.org/
- **Threat Intelligence**: Various feeds
- **Security Updates**: Vendor notifications
- **Best Practices**: Industry standards

---

**🎯 CYBER-OPS PLATFORM OPERATIONAL MANUAL COMPLETE**

This manual provides comprehensive guidance for operating the CYBER-OPS platform safely and effectively. Always ensure proper authorization before conducting any security testing activities.
