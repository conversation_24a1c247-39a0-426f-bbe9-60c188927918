# Phishing Awareness Platform Environment Configuration

# Flask Configuration
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# Database Configuration
DATABASE_URL=sqlite:///phishing_platform.db

# Security Configuration
SECURITY_KEY=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6

# VirusTotal API Configuration
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
VIRUSTOTAL_BASE_URL=https://www.virustotal.com/vtapi/v2

# Email Configuration (for notifications)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# CVE Database Configuration
CVE_UPDATE_INTERVAL=3600
NVD_API_URL=https://services.nvd.nist.gov/rest/json/cves/2.0

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads
ALLOWED_EXTENSIONS=pdf,xlsx,docx,txt

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=phishing_platform.log

# Development Settings
DEBUG=True
TESTING=False 