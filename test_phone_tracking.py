#!/usr/bin/env python3
"""
Test script for AMADIOHA-M257 Phone Tracking System
"""

import requests
import json
import time
import sys

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_PHONE = "+1234567890"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """Login to get session"""
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"❌ Failed to access login page: {response.status_code}")
        return None
    
    # Login
    login_data = {
        'username': USERNAME,
        'password': PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_phone_info(session):
    """Test phone number information API"""
    print("\n🔍 Testing Phone Information API...")
    
    data = {
        'phone_number': TEST_PHONE
    }
    
    response = session.post(f"{BASE_URL}/api/phone_info", json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Phone info API working")
            print(f"   Number: {result['data'].get('number')}")
            print(f"   Country: {result['data'].get('country')}")
            print(f"   Carrier: {result['data'].get('carrier')}")
            print(f"   Valid: {result['data'].get('is_valid')}")
            return result.get('tracker_id')
        else:
            print(f"❌ Phone info API error: {result.get('error')}")
    else:
        print(f"❌ Phone info API failed: {response.status_code}")
    
    return None

def test_start_tracking(session):
    """Test starting real-time tracking"""
    print("\n🛰️ Testing Start Tracking API...")
    
    data = {
        'phone_number': TEST_PHONE,
        'duration': 5  # 5 minutes for testing
    }
    
    response = session.post(f"{BASE_URL}/api/start_tracking", json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Tracking started successfully")
            print(f"   Tracking ID: {result.get('tracking_id')}")
            print(f"   Duration: {result.get('duration_minutes')} minutes")
            return result.get('tracking_id')
        else:
            print(f"❌ Start tracking error: {result.get('error')}")
    else:
        print(f"❌ Start tracking failed: {response.status_code}")
    
    return None

def test_tracking_status(session, tracking_id):
    """Test tracking status API"""
    print(f"\n📊 Testing Tracking Status API...")
    
    response = session.get(f"{BASE_URL}/api/tracking_status/{tracking_id}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Tracking status API working")
            print(f"   Status: {result.get('status')}")
            print(f"   Phone: {result.get('phone_number')}")
            print(f"   Locations: {result.get('total_locations')}")
            if result.get('current_location'):
                loc = result['current_location']
                print(f"   Current Location: {loc.get('latitude')}, {loc.get('longitude')}")
                print(f"   Accuracy: ±{loc.get('accuracy')}m")
                print(f"   Source: {loc.get('source')}")
            return True
        else:
            print(f"❌ Tracking status error: {result.get('error')}")
    else:
        print(f"❌ Tracking status failed: {response.status_code}")
    
    return False

def test_tracked_numbers(session):
    """Test tracked numbers list API"""
    print("\n📋 Testing Tracked Numbers API...")
    
    response = session.get(f"{BASE_URL}/api/tracked_numbers")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Tracked numbers API working")
            numbers = result.get('tracked_numbers', [])
            print(f"   Total tracked numbers: {len(numbers)}")
            for number in numbers[:3]:  # Show first 3
                print(f"   - {number.get('phone_number')} ({number.get('tracking_status')})")
            return True
        else:
            print(f"❌ Tracked numbers error: {result.get('error')}")
    else:
        print(f"❌ Tracked numbers failed: {response.status_code}")
    
    return False

def test_stop_tracking(session, tracking_id):
    """Test stopping tracking"""
    print(f"\n🛑 Testing Stop Tracking API...")
    
    data = {
        'tracking_id': tracking_id
    }
    
    response = session.post(f"{BASE_URL}/api/stop_tracking", json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ Tracking stopped successfully")
            return True
        else:
            print(f"❌ Stop tracking error: {result.get('error')}")
    else:
        print(f"❌ Stop tracking failed: {response.status_code}")
    
    return False

def main():
    """Main test function"""
    print("🚀 AMADIOHA-M257 Phone Tracking System Test")
    print("=" * 50)
    
    # Login
    session = login()
    if not session:
        print("❌ Cannot proceed without login")
        sys.exit(1)
    
    # Test phone info
    tracker_id = test_phone_info(session)
    
    # Test start tracking
    tracking_id = test_start_tracking(session)
    if not tracking_id:
        print("❌ Cannot test tracking without valid tracking ID")
        return
    
    # Wait a bit for tracking to start
    print("\n⏳ Waiting 10 seconds for tracking to initialize...")
    time.sleep(10)
    
    # Test tracking status (multiple times to see updates)
    for i in range(3):
        print(f"\n📊 Status Check #{i+1}")
        test_tracking_status(session, tracking_id)
        if i < 2:
            time.sleep(5)
    
    # Test tracked numbers list
    test_tracked_numbers(session)
    
    # Test stop tracking
    test_stop_tracking(session, tracking_id)
    
    print("\n" + "=" * 50)
    print("🎯 Phone Tracking System Test Complete!")
    print("\n📝 Test Summary:")
    print("   ✅ Phone Information Analysis")
    print("   ✅ Real-Time Tracking Start")
    print("   ✅ Tracking Status Monitoring")
    print("   ✅ Tracked Numbers Management")
    print("   ✅ Tracking Stop Control")
    print("\n🔒 Remember: Use only for authorized cybersecurity testing!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
