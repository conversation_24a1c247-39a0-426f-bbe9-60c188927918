@echo off
echo ========================================
echo Augie-Pentest H1 v2.0 - Windows Setup
echo Powered by Augment AI
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo.
echo Installing Windows-compatible packages...
echo This may take a few minutes...
echo.

REM Install core packages first
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install Flask-Login==0.6.3
pip install requests==2.31.0
pip install cryptography==41.0.4

REM Install Windows-specific packages
pip install pywin32==306
pip install psutil==5.9.5
pip install python-dotenv==1.0.0

REM Install remaining packages
pip install -r requirements_windows.txt

echo.
echo ========================================
echo Installation Complete!
echo ========================================
echo.
echo To start Augie-Pentest H1 v2.0:
echo 1. Open Command Prompt as Administrator
echo 2. Navigate to this directory
echo 3. Run: python app.py
echo.
echo The system will be available at:
echo http://localhost:5000
echo.
echo Access Code: SHYJAYOBIBI
echo Default Login: admin / admin123
echo.
pause
