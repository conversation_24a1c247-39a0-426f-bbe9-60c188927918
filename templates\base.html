<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AMADIOHA-M257 v1.0.0{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Enhanced AMADIOHA Preloader Styles */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: all 0.8s ease-out;
            overflow: hidden;
        }

        .preloader.fade-out {
            opacity: 0;
            transform: scale(1.1);
            pointer-events: none;
        }

        .preloader-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .preloader-logo {
            max-width: 350px;
            width: 90%;
            height: auto;
            filter: drop-shadow(0 0 20px #00ff00) drop-shadow(0 0 40px #00ff0050);
            animation: logoGlow 3s ease-in-out infinite;
            margin-bottom: 40px;
        }

        @keyframes logoGlow {
            0% {
                transform: scale(1) rotate(0deg);
                filter: drop-shadow(0 0 20px #00ff00) drop-shadow(0 0 40px #00ff0050);
            }
            25% {
                transform: scale(1.02) rotate(0.5deg);
                filter: drop-shadow(0 0 25px #00ff00) drop-shadow(0 0 50px #00ff0070);
            }
            50% {
                transform: scale(1.05) rotate(0deg);
                filter: drop-shadow(0 0 30px #00ff00) drop-shadow(0 0 60px #00ff0090);
            }
            75% {
                transform: scale(1.02) rotate(-0.5deg);
                filter: drop-shadow(0 0 25px #00ff00) drop-shadow(0 0 50px #00ff0070);
            }
            100% {
                transform: scale(1) rotate(0deg);
                filter: drop-shadow(0 0 20px #00ff00) drop-shadow(0 0 40px #00ff0050);
            }
        }

        .speed-animation {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
            height: 40px;
            overflow: hidden;
            position: relative;
            width: 400px;
        }

        .speed-lines {
            font-family: 'Roboto Mono', monospace;
            font-size: 1.5rem;
            color: #00ff00;
            white-space: nowrap;
            animation: speedRush 1.2s linear infinite;
            text-shadow: 0 0 10px #00ff00, 0 0 20px #00ff0050;
        }

        @keyframes speedRush {
            0% {
                transform: translateX(-100%);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .preloader-text {
            color: #00ff00;
            font-family: 'Roboto Mono', monospace;
            font-size: 1.1rem;
            margin-top: 30px;
            text-align: center;
            animation: textPulse 2s ease-in-out infinite;
            text-shadow: 0 0 10px #00ff00;
            letter-spacing: 2px;
        }

        @keyframes textPulse {
            0%, 100% {
                opacity: 0.7;
                transform: translateY(0px);
            }
            50% {
                opacity: 1;
                transform: translateY(-2px);
            }
        }

        .loading-progress {
            width: 300px;
            height: 4px;
            background: #333;
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
            position: relative;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ff00, #00cc00, #00ff00);
            border-radius: 2px;
            animation: loadingProgress 3s ease-in-out infinite;
            box-shadow: 0 0 10px #00ff00;
        }

        @keyframes loadingProgress {
            0% {
                width: 0%;
                transform: translateX(-100%);
            }
            50% {
                width: 100%;
                transform: translateX(0%);
            }
            100% {
                width: 100%;
                transform: translateX(100%);
            }
        }

        /* Particle effect background */
        .preloader::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #00ff0020, transparent),
                radial-gradient(2px 2px at 40px 70px, #00ff0030, transparent),
                radial-gradient(1px 1px at 90px 40px, #00ff0020, transparent),
                radial-gradient(1px 1px at 130px 80px, #00ff0030, transparent),
                radial-gradient(2px 2px at 160px 30px, #00ff0020, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            animation: particleFloat 20s linear infinite;
            opacity: 0.3;
        }

        @keyframes particleFloat {
            0% { transform: translateY(0px) translateX(0px); }
            100% { transform: translateY(-100px) translateX(50px); }
        }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        :root {
            --primary-color: #ffffff;
            --secondary-color: #000000;
            --accent-color: #ffffff;
            --danger-color: #ff0000;
            --success-color: #ffffff;
            --warning-color: #ffff00;
            --dark-bg: #000000;
            --card-bg: rgba(0, 0, 0, 0.95);
            --sidebar-bg: #000000;
            --header-bg: #000000;
            --text-primary: #ffffff;
            --text-secondary: #cccccc;
            --border-radius: 4px;
            --border-width: 1px;
            --border-color: #ffffff;
            --shadow: 0 0 5px rgba(255, 255, 255, 0.05);
            --glass-bg: rgba(0, 0, 0, 0.9);
            --glass-border: rgba(255, 255, 255, 0.2);
            --terminal-green: #00ff00;
            --terminal-bg: #000000;
        }
        body {
            font-family: 'Roboto Mono', 'Courier New', monospace;
            background: var(--dark-bg);
            color: var(--text-primary);
            min-height: 100vh;
        }
        .sidebar {
            position: fixed;
            top: 0; left: 0; bottom: 0;
            width: 260px;
            background: var(--dark-bg);
            border-right: 2px solid var(--border-color);
            box-shadow: var(--shadow);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            padding: 1.5rem 0;
            transition: all 0.3s ease;
            font-family: 'Roboto Mono', monospace;
        }
        .sidebar .nav-link {
            color: var(--text-primary);
            border-radius: var(--border-radius);
            margin: 0.25rem 0.5rem;
            padding: 0.75rem 1.25rem;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            transition: all 0.2s;
            border: 1px solid transparent;
            font-family: 'Roboto Mono', monospace;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-decoration: none;
        }
        .sidebar .nav-link.active, .sidebar .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border-color: var(--border-color);
            box-shadow: var(--shadow);
            transform: translateX(4px);
        }
        .sidebar .nav-link i {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        /* Dropdown Styles */
        .dropdown-menu {
            background: var(--dark-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin: 0.25rem 0.5rem;
            padding: 0.5rem 0;
        }
        .dropdown-item {
            color: var(--text-primary);
            padding: 0.5rem 1.25rem;
            font-size: 0.85rem;
            font-family: 'Roboto Mono', monospace;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: none;
            background: none;
            transition: all 0.3s ease;
        }
        .dropdown-item:hover, .dropdown-item:focus {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            transform: translateX(4px);
        }
        .dropdown-item i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }
        .dropdown-toggle::after {
            margin-left: auto;
            font-family: "Font Awesome 5 Free";
            content: "\f107";
            border: none;
            font-weight: 900;
            transition: transform 0.3s ease;
        }
        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }
        .nav-dropdown {
            position: relative;
        }
        .nav-dropdown .dropdown-menu {
            position: static;
            display: none;
            width: 100%;
            box-shadow: none;
            border: none;
            background: rgba(255, 255, 255, 0.05);
            margin: 0;
            padding-left: 1rem;
        }
        .nav-dropdown.show .dropdown-menu {
            display: block;
        }

        .sidebar .sidebar-footer {
            margin-top: auto;
            padding: 1rem;
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.95rem;
        }
        .header {
            position: fixed;
            left: 240px; right: 0; top: 0;
            height: 64px;
            background: var(--header-bg);
            border-bottom: var(--border-width) solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            z-index: 900;
            box-shadow: 0 2px 16px rgba(0,0,0,0.08);
        }
        .header .brand {
            font-family: 'Roboto Mono', monospace;
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.35rem;
            display: flex;
            align-items: center;
        }
        .header .brand i {
            margin-right: 0.5rem;
        }
        .header .profile {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .header .profile .avatar {
            width: 40px; height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 1.3rem;
            border: var(--border-width) solid var(--border-color);
            box-shadow: 0 2px 8px rgba(124,58,237,0.12);
            cursor: pointer;
            transition: box-shadow 0.2s;
        }
        .header .profile .avatar:hover {
            box-shadow: 0 4px 16px rgba(124,58,237,0.18);
        }
        .header .profile .dropdown-menu {
            background: var(--card-bg);
            border-radius: var(--border-radius);
            border: var(--border-width) solid var(--border-color);
            color: var(--text-primary);
            min-width: 180px;
        }
        .main-content {
            margin-left: 260px;
            margin-top: 64px;
            padding: 2rem;
            min-height: calc(100vh - 64px);
            background: transparent;
            transition: margin-left 0.3s ease;
        }
        .card {
            background: var(--dark-bg);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            font-family: 'Roboto Mono', monospace;
            transition: all 0.2s;
        }
        .card:hover {
            box-shadow: 0 0 8px rgba(255, 255, 255, 0.1);
            transform: scale(1.01);
        }
        .card-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .card-body {
            color: var(--text-primary);
        }
        .btn, .btn-cyber, .btn-primary, .btn-success, .btn-warning, .btn-danger, .btn-info {
            background: var(--dark-bg) !important;
            border: 2px solid var(--border-color) !important;
            border-radius: var(--border-radius) !important;
            color: var(--text-primary) !important;
            font-family: 'Roboto Mono', monospace !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
            font-weight: bold !important;
            transition: all 0.2s !important;
        }
        .btn:hover, .btn-cyber:hover, .btn-primary:hover, .btn-success:hover, .btn-warning:hover, .btn-danger:hover, .btn-info:hover {
            background: var(--text-primary) !important;
            color: var(--dark-bg) !important;
            box-shadow: 0 0 3px rgba(255, 255, 255, 0.3) !important;
            transform: translateY(-1px) !important;
        }
        .btn-danger {
            border-color: var(--danger-color) !important;
            color: var(--danger-color) !important;
        }
        .btn-danger:hover {
            background: var(--danger-color) !important;
            color: var(--terminal-bg) !important;
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.5) !important;
        }
        .btn-warning {
            border-color: var(--warning-color) !important;
            color: var(--warning-color) !important;
        }
        .btn-warning:hover {
            background: var(--warning-color) !important;
            color: var(--terminal-bg) !important;
            box-shadow: 0 0 15px rgba(255, 255, 0, 0.5) !important;
        }
        /* Mobile-first responsive design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 70px;
                padding: 1rem 0;
                overflow: visible;
                transition: width 0.3s ease;
            }
            .sidebar:hover {
                width: 260px;
                overflow-y: auto;
            }
            .sidebar .nav-link span {
                display: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            }
            .sidebar:hover .nav-link span {
                display: inline;
                opacity: 1;
            }
            .sidebar .brand span { display: none; }
            .sidebar:hover .brand span { display: inline; }
            .sidebar .dropdown-toggle::after { display: none; }
            .sidebar:hover .dropdown-toggle::after { display: inline; }
            .sidebar .nav-dropdown .dropdown-menu {
                display: none !important;
            }
            .sidebar:hover .nav-dropdown .dropdown-menu {
                display: block !important;
                position: static;
            }
            .header { left: 70px; padding: 0 1rem; }
            .main-content { margin-left: 70px; padding: 1rem; }
        }
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px;
                overflow-y: auto;
                max-height: 100vh;
            }
            .sidebar.show { transform: translateX(0); }
            .sidebar .nav-link span { display: inline; }
            .sidebar .dropdown-toggle::after { display: inline; }
            .sidebar .nav-dropdown .dropdown-menu { display: block; }
            .header { left: 0; }
            .main-content { margin-left: 0; padding: 0.5rem; }
            .mobile-menu-btn {
                display: block !important;
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 1001;
                background: var(--glass-bg);
                border: var(--border-width) solid var(--glass-border);
                border-radius: var(--border-radius);
                padding: 0.5rem;
                color: var(--text-primary);
                backdrop-filter: blur(10px);
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
        @media (max-width: 480px) {
            .sidebar {
                width: 100vw;
            }
            .main-content { padding: 0.25rem; }
        }
        .mobile-menu-btn { display: none; }
        .nav-section-header {
            padding: 0 1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Form controls */
        .form-control, .form-select, textarea {
            background: var(--dark-bg) !important;
            border: 1px solid var(--border-color) !important;
            color: var(--text-primary) !important;
            font-family: 'Roboto Mono', monospace !important;
            border-radius: var(--border-radius) !important;
        }
        .form-control:focus, .form-select:focus, textarea:focus {
            border-color: var(--text-primary) !important;
            box-shadow: 0 0 3px rgba(255, 255, 255, 0.2) !important;
            background: var(--dark-bg) !important;
            color: var(--text-primary) !important;
        }
        .form-control::placeholder {
            color: var(--text-secondary) !important;
            opacity: 0.7 !important;
        }

        /* Alert styles */
        .alert {
            background: var(--terminal-bg) !important;
            border: 1px solid var(--terminal-green) !important;
            color: var(--text-primary) !important;
            font-family: 'Roboto Mono', monospace !important;
        }
        .alert-warning {
            border-color: var(--warning-color) !important;
            color: var(--warning-color) !important;
        }
        .alert-danger {
            border-color: var(--danger-color) !important;
            color: var(--danger-color) !important;
        }
        .alert-info {
            border-color: var(--border-color) !important;
            color: var(--text-primary) !important;
        }

        /* Terminal interface specific styles - keep green for terminal pages */
        .terminal-interface .card {
            border-color: var(--terminal-green) !important;
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.3) !important;
        }
        .terminal-interface .card:hover {
            box-shadow: 0 0 25px rgba(0, 255, 0, 0.5) !important;
        }
        .terminal-interface .card-header {
            background: rgba(0, 255, 0, 0.1) !important;
            border-bottom-color: var(--terminal-green) !important;
        }
        .terminal-interface .btn {
            border-color: var(--terminal-green) !important;
            color: var(--terminal-green) !important;
        }
        .terminal-interface .btn:hover {
            background: var(--terminal-green) !important;
            color: var(--terminal-bg) !important;
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.5) !important;
        }
        .terminal-interface .form-control {
            border-color: var(--terminal-green) !important;
        }
        .terminal-interface .form-control:focus {
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3) !important;
        }
        .terminal-interface .nav-link {
            color: var(--terminal-green) !important;
        }
        .terminal-interface .nav-link:hover {
            background: rgba(0, 255, 0, 0.1) !important;
            border-color: var(--terminal-green) !important;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3) !important;
        }
    </style>
</head>
<body>
    <!-- Enhanced AMADIOHA Preloader -->
    <div class="preloader" id="preloader">
        <div class="preloader-container">
            <!-- AMADIOHA Logo -->
            <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}"
                 alt="AMADIOHA-M257"
                 class="preloader-logo">

            <!-- Speed Animation Effect -->
            <div class="speed-animation">
                <div class="speed-lines">....../\/\/\/\/\/\/\/\/\.....</div>
            </div>

            <!-- Loading Progress Bar -->
            <div class="loading-progress">
                <div class="loading-bar"></div>
            </div>

            <!-- Loading Text -->
            <div class="preloader-text">INITIALIZING AMADIOHA-M257</div>
        </div>
    </div>

    <!-- Mobile Menu Button -->
    <button class="mobile-menu-btn" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>
 <div class="sidebar" id="sidebar">
        <a class="navbar-brand brand mb-4" href="{{ url_for('index') }}" <div class="header">
        <div class="brand">
            <div class="text-center"></div>
            <img src="{{ url_for('static', filename='images/amadioha-logo.svg') }}" alt="AMADIOHA-M257" style="height: 40px; width: auto;">
        </div>
        </a>
        {% if current_user.is_authenticated %}
            <!-- Main Dashboard -->
            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                <i class="fas fa-tachometer-alt"></i> <span>Dashboard</span>
            </a>

            <!-- Campaign Management Dropdown -->
            <div class="nav-dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" onclick="toggleDropdown(this)">
                    <i class="fas fa-bullhorn"></i> <span>Campaign Mgt</span>
                </a>
                <div class="dropdown-menu">
                    <a class="dropdown-item {% if request.endpoint == 'mass_campaign' %}active{% endif %}" href="/mass_campaign">
                        <i class="fas fa-rocket"></i> Mass-Email Engine
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'campaigns' %}active{% endif %}" href="{{ url_for('campaigns') }}">
                        <i class="fas fa-bullhorn"></i> Campaigns
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'templates' %}active{% endif %}" href="{{ url_for('templates') }}">
                        <i class="fas fa-envelope"></i> Templates
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'results' %}active{% endif %}" href="{{ url_for('results') }}">
                        <i class="fas fa-chart-bar"></i> Results
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'analytics' %}active{% endif %}" href="{{ url_for('analytics') }}">
                        <i class="fas fa-chart-line"></i> Analytics
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'cves' %}active{% endif %}" href="{{ url_for('cves') }}">
                        <i class="fas fa-bug"></i> CVEs
                    </a>
                </div>
            </div>

            <!-- Malware Operations Dropdown -->
            <div class="nav-dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" onclick="toggleDropdown(this)">
                    <i class="fas fa-virus"></i> <span>Malware Operations</span>
                </a>
                <div class="dropdown-menu">
                    <a class="dropdown-item {% if request.endpoint == 'malware_operations' %}active{% endif %}" href="{{ url_for('malware_operations') }}">
                        <i class="fas fa-virus"></i> Malware Operations
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'real_malware_deployment' %}active{% endif %}" href="{{ url_for('real_malware_deployment') }}">
                        <i class="fas fa-skull-crossbones"></i> Real Malware Deployment
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'payload_generator' %}active{% endif %}" href="{{ url_for('payload_generator') }}">
                        <i class="fas fa-bomb"></i> Payload Generator
                    </a>
                </div>
            </div>

            <!-- Phishing Operations Dropdown -->
            <div class="nav-dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false" onclick="toggleDropdown(this)">
                    <i class="fas fa-fish"></i> <span>Phishing Operations</span>
                </a>
                <div class="dropdown-menu">
                    <a class="dropdown-item {% if request.endpoint == 'phishing_operations' %}active{% endif %}" href="{{ url_for('phishing_operations') }}">
                        <i class="fas fa-fish"></i> Phishing Operations
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'social_engineering' %}active{% endif %}" href="{{ url_for('social_engineering') }}">
                        <i class="fas fa-user-secret"></i> Social Engineering
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'enhanced_social_engineering' %}active{% endif %}" href="{{ url_for('enhanced_social_engineering') }}">
                        <i class="fas fa-envelope-open-text"></i> Real SMTP Phishing
                    </a>
                    <a class="dropdown-item {% if request.endpoint == 'pdf_generator' %}active{% endif %}" href="{{ url_for('pdf_generator') }}">
                        <i class="fas fa-file-pdf"></i> PDF Attachment Generator
                    </a>
                </div>
            </div>

            <!-- Phone Operations -->
            <a class="nav-link {% if request.endpoint == 'phone_operations' %}active{% endif %}" href="{{ url_for('phone_operations') }}">
                <i class="fas fa-mobile-alt"></i> <span>Phone Operations</span>
            </a>

            <!-- Network Recon -->
            <a class="nav-link {% if request.endpoint == 'network_recon' %}active{% endif %}" href="{{ url_for('network_recon') }}">
                <i class="fas fa-search"></i> <span>Network Recon</span>
            </a>

            <!-- Terminal -->
            <a class="nav-link {% if request.endpoint == 'terminal' %}active{% endif %}" href="{{ url_for('terminal') }}">
                <i class="fas fa-terminal"></i> <span>Terminal</span>
            </a>

            <!-- AI Chat -->
            <a class="nav-link {% if request.endpoint == 'chat' %}active{% endif %}" href="{{ url_for('chat') }}">
                <i class="fas fa-robot"></i> <span>AI Chat</span>
            </a>

            <!-- Logout -->
            <a class="nav-link mt-4" href="{{ url_for('logout') }}">
                <i class="fas fa-sign-out-alt"></i> <span>Logout</span>
            </a>
        {% else %}
            <a class="nav-link" href="{{ url_for('login') }}"><i class="fas fa-sign-in-alt"></i> <span>Login</span></a>
        {% endif %}
    </div>
   
        <div class="profile dropdown">
            <div class="avatar dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-user"></i>
            </div>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item" href="#">Profile</a></li>
                <li><a class="dropdown-item" href="#">Settings</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a></li>
            </ul>
        </div>
    </div>
    <div class="main-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show mt-3" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </div>

    <!-- Legal Footer -->
    <footer class="mt-5 py-4" style="background: var(--dark-bg); border-top: 1px solid var(--border-color); color: var(--text-secondary); font-size: 0.85rem;">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong style="color: var(--terminal-green);">AMADIOHA-M257 v1.0.0</strong>
                    </div>
                    <div class="mb-2">
                        Licensed under <strong>BMD CYBER OPS LEGAL RIGHT</strong>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-md-end">
                        <div class="mb-2" style="color: #ff6b6b;">
                            <i class="fas fa-exclamation-triangle"></i> <strong>DISCLAIMER & WARNING</strong>
                        </div>
                        <div style="font-size: 0.75rem; line-height: 1.3;">
                            This system is for authorized cybersecurity training and testing purposes only.<br>
                            Unauthorized use is strictly prohibited and may violate applicable laws.<br>
                            Users acknowledge full responsibility for compliance with all legal requirements.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <div style="color: var(--text-secondary); font-size: 0.7rem;">
                        By using this system, you acknowledge and agree to the terms of use and legal disclaimers.
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Toggle dropdown functionality
        function toggleDropdown(element) {
            event.preventDefault();
            const dropdown = element.parentElement;
            const isOpen = dropdown.classList.contains('show');

            // Close all other dropdowns
            document.querySelectorAll('.nav-dropdown.show').forEach(d => {
                if (d !== dropdown) {
                    d.classList.remove('show');
                    d.querySelector('.dropdown-toggle').setAttribute('aria-expanded', 'false');
                }
            });

            // Toggle current dropdown
            if (isOpen) {
                dropdown.classList.remove('show');
                element.setAttribute('aria-expanded', 'false');
            } else {
                dropdown.classList.add('show');
                element.setAttribute('aria-expanded', 'true');
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !menuBtn.contains(event.target)) {
                sidebar.classList.remove('show');
            }

            // Close dropdowns when clicking outside
            if (!event.target.closest('.nav-dropdown')) {
                document.querySelectorAll('.nav-dropdown.show').forEach(dropdown => {
                    dropdown.classList.remove('show');
                    dropdown.querySelector('.dropdown-toggle').setAttribute('aria-expanded', 'false');
                });
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });

        // Enhanced AMADIOHA Preloader functionality
        window.addEventListener('load', function() {
            const preloader = document.getElementById('preloader');
            const preloaderText = document.querySelector('.preloader-text');

            // Loading sequence messages
            const loadingMessages = [
                'INITIALIZING AMADIOHA-M257',
                'LOADING SECURITY MODULES',
                'ACTIVATING THREAT INTELLIGENCE',
                'PREPARING ATTACK VECTORS',
                'SYSTEM READY'
            ];

            let messageIndex = 0;

            // Update loading messages
            const messageInterval = setInterval(function() {
                if (messageIndex < loadingMessages.length) {
                    preloaderText.textContent = loadingMessages[messageIndex];
                    messageIndex++;
                } else {
                    clearInterval(messageInterval);
                }
            }, 800);

            // Hide preloader after loading sequence
            setTimeout(function() {
                preloader.classList.add('fade-out');
                setTimeout(function() {
                    preloader.style.display = 'none';
                }, 800);
            }, 4500); // Show for 4.5 seconds total
        });

        // Add additional speed lines for enhanced effect
        document.addEventListener('DOMContentLoaded', function() {
            const speedAnimation = document.querySelector('.speed-animation');

            // Create multiple speed line elements for layered effect
            for (let i = 0; i < 3; i++) {
                const speedLine = document.createElement('div');
                speedLine.className = 'speed-lines';
                speedLine.textContent = '....../\/\/\/\/\/\/\/\/\\.....';
                speedLine.style.animationDelay = `${i * 0.4}s`;
                speedLine.style.position = 'absolute';
                speedLine.style.opacity = `${1 - (i * 0.3)}`;
                speedAnimation.appendChild(speedLine);
            }
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
