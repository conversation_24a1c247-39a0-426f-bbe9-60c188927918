#!/usr/bin/env python3
"""
CYBER-OPS Platform System Status Check
Comprehensive verification of all platform capabilities
"""

import requests
import json
import time
import sys
import os
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TIMEOUT = 10

def print_header():
    """Print system check header"""
    print("🎯 CYBER-OPS PLATFORM SYSTEM STATUS CHECK")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Base URL: {BASE_URL}")
    print("=" * 60)

def check_platform_status():
    """Check if platform is running"""
    print("\n🚀 PLATFORM STATUS CHECK")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/login", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Platform is ONLINE and accessible")
            print(f"   Response time: {response.elapsed.total_seconds():.3f}s")
            return True
        else:
            print(f"❌ Platform returned status code: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Platform is OFFLINE - Connection refused")
        return False
    except Exception as e:
        print(f"❌ Platform check failed: {e}")
        return False

def check_malware_deployment():
    """Check malware deployment capabilities"""
    print("\n🦠 MALWARE DEPLOYMENT CHECK")
    print("-" * 30)
    
    # Check keylogger deployment endpoint
    try:
        response = requests.get(f"{BASE_URL}/real_malware_deployment", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Malware deployment interface accessible")
        else:
            print(f"❌ Malware deployment interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ Malware deployment check failed: {e}")

def check_phishing_capabilities():
    """Check phishing system capabilities"""
    print("\n🎣 PHISHING SYSTEM CHECK")
    print("-" * 30)
    
    # Check enhanced social engineering
    try:
        response = requests.get(f"{BASE_URL}/enhanced_social_engineering", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Enhanced social engineering interface accessible")
        else:
            print(f"❌ Social engineering interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ Phishing system check failed: {e}")
    
    # Check PDF generator
    try:
        response = requests.get(f"{BASE_URL}/pdf_generator", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ PDF attachment generator accessible")
        else:
            print(f"❌ PDF generator error: {response.status_code}")
    except Exception as e:
        print(f"❌ PDF generator check failed: {e}")

def check_perfect_replicas():
    """Check perfect login replica generation"""
    print("\n🎭 PERFECT REPLICA CHECK")
    print("-" * 30)
    
    # Test Gmail replica
    try:
        response = requests.get(f"{BASE_URL}/perfect_replica/gmail?target=<EMAIL>", timeout=TIMEOUT)
        if response.status_code == 200 and "gmail" in response.text.lower():
            print("✅ Gmail perfect replica generation working")
        else:
            print(f"❌ Gmail replica error: {response.status_code}")
    except Exception as e:
        print(f"❌ Gmail replica check failed: {e}")
    
    # Test Outlook replica
    try:
        response = requests.get(f"{BASE_URL}/perfect_replica/outlook?target=<EMAIL>", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Outlook perfect replica generation working")
        else:
            print(f"❌ Outlook replica error: {response.status_code}")
    except Exception as e:
        print(f"❌ Outlook replica check failed: {e}")

def check_network_recon():
    """Check network reconnaissance capabilities"""
    print("\n🔍 NETWORK RECONNAISSANCE CHECK")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/network_recon", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Network reconnaissance interface accessible")
        else:
            print(f"❌ Network recon interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ Network reconnaissance check failed: {e}")

def check_cve_database():
    """Check CVE database capabilities"""
    print("\n🚨 CVE DATABASE CHECK")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/cve_database", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ CVE database interface accessible")
        else:
            print(f"❌ CVE database interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ CVE database check failed: {e}")

def check_advanced_features():
    """Check advanced platform features"""
    print("\n🔧 ADVANCED FEATURES CHECK")
    print("-" * 30)
    
    # Check terminal interface
    try:
        response = requests.get(f"{BASE_URL}/terminal", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Terminal interface accessible")
        else:
            print(f"❌ Terminal interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ Terminal interface check failed: {e}")
    
    # Check dashboard
    try:
        response = requests.get(f"{BASE_URL}/dashboard", timeout=TIMEOUT)
        if response.status_code in [200, 302]:  # 302 for redirect to login
            print("✅ Dashboard interface accessible")
        else:
            print(f"❌ Dashboard interface error: {response.status_code}")
    except Exception as e:
        print(f"❌ Dashboard check failed: {e}")

def check_file_structure():
    """Check critical file structure"""
    print("\n📁 FILE STRUCTURE CHECK")
    print("-" * 30)
    
    critical_files = [
        "app.py",
        "requirements.txt",
        "README.md",
        "DEPLOYMENT_GUIDE.md",
        "OPERATIONAL_MANUAL.md",
        "SECURITY_COMPLIANCE_GUIDE.md",
        "QUICK_START_GUIDE.md"
    ]
    
    for file in critical_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")

def check_documentation():
    """Check documentation completeness"""
    print("\n📚 DOCUMENTATION CHECK")
    print("-" * 30)
    
    docs = {
        "README.md": "Main documentation",
        "DEPLOYMENT_GUIDE.md": "Deployment instructions",
        "OPERATIONAL_MANUAL.md": "Operation procedures",
        "SECURITY_COMPLIANCE_GUIDE.md": "Security guidelines",
        "QUICK_START_GUIDE.md": "Quick start instructions"
    }
    
    for doc, description in docs.items():
        if os.path.exists(doc):
            size = os.path.getsize(doc)
            print(f"✅ {doc} ({description}) - {size:,} bytes")
        else:
            print(f"❌ {doc} missing")

def generate_system_report():
    """Generate comprehensive system report"""
    print("\n📊 SYSTEM CAPABILITIES SUMMARY")
    print("=" * 60)
    
    capabilities = {
        "🦠 Real Malware Deployment": [
            "Windows keylogger with persistence",
            "Browser credential stealer (Chrome/Firefox/Edge)",
            "Anti-VM and sandbox detection",
            "AES-256 encrypted C2 communication"
        ],
        "🎣 Advanced Phishing Systems": [
            "Real SMTP email delivery",
            "Perfect Gmail/Outlook login replicas",
            "Malicious PDF attachments with JavaScript",
            "Real-time credential harvesting"
        ],
        "🔍 Network Reconnaissance": [
            "Subdomain enumeration (DNS/CT/Search)",
            "Advanced port scanning (SYN/Connect/UDP)",
            "Service detection and banner grabbing",
            "Vulnerability mapping and reporting"
        ],
        "🚨 2025 CVE Exploitation": [
            "10 critical 2025 vulnerabilities",
            "Working exploit code generation",
            "Target-specific payload creation",
            "Vulnerability assessment tools"
        ],
        "🛡️ Security Features": [
            "AES-256-GCM encryption",
            "Comprehensive audit logging",
            "Real-time Telegram notifications",
            "Role-based access control"
        ],
        "🔧 Advanced Integrations": [
            "Ngrok tunneling for public access",
            "Telegram bot for notifications",
            "Perfect login page replication",
            "Encrypted credential storage"
        ]
    }
    
    for category, features in capabilities.items():
        print(f"\n{category}:")
        for feature in features:
            print(f"  ✅ {feature}")

def main():
    """Main system check function"""
    print_header()
    
    # Core platform checks
    platform_online = check_platform_status()
    
    if platform_online:
        check_malware_deployment()
        check_phishing_capabilities()
        check_perfect_replicas()
        check_network_recon()
        check_cve_database()
        check_advanced_features()
    else:
        print("\n⚠️  Platform is offline - skipping functionality checks")
        print("   Start the platform with: python app.py")
    
    # File system checks
    check_file_structure()
    check_documentation()
    
    # Generate report
    generate_system_report()
    
    print("\n" + "=" * 60)
    print("🎯 CYBER-OPS PLATFORM STATUS CHECK COMPLETE")
    print("=" * 60)
    
    if platform_online:
        print("✅ PLATFORM IS FULLY OPERATIONAL")
        print("\n🚀 READY FOR AUTHORIZED PENETRATION TESTING!")
        print("\n📖 Next Steps:")
        print("   1. Review DEPLOYMENT_GUIDE.md for production setup")
        print("   2. Read SECURITY_COMPLIANCE_GUIDE.md for legal requirements")
        print("   3. Study OPERATIONAL_MANUAL.md for daily operations")
        print("   4. Ensure proper authorization before testing")
    else:
        print("❌ PLATFORM REQUIRES ATTENTION")
        print("\n🔧 Troubleshooting:")
        print("   1. Start platform: python app.py")
        print("   2. Check logs for errors")
        print("   3. Verify dependencies: pip install -r requirements.txt")
        print("   4. Review QUICK_START_GUIDE.md")
    
    print("\n⚠️  REMEMBER: FOR AUTHORIZED USE ONLY")
    print("   Always ensure proper legal authorization before deployment")

if __name__ == "__main__":
    main()
