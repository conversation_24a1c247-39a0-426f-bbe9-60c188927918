#!/usr/bin/env python3
"""
AMADIOHA-M257 Environment Setup Script
Interactive configuration setup for the cybersecurity platform
"""

import os
import secrets
import string
import getpass
from pathlib import Path

def generate_secure_key(length=64):
    """Generate a cryptographically secure random key"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*()-_=+[]{}|;:,.<>?"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def get_user_input(prompt, default="", required=True, password=False):
    """Get user input with validation"""
    while True:
        if password:
            value = getpass.getpass(f"{prompt} [{default}]: " if default else f"{prompt}: ")
        else:
            value = input(f"{prompt} [{default}]: " if default else f"{prompt}: ").strip()
        
        if value:
            return value
        elif default:
            return default
        elif not required:
            return ""
        else:
            print("This field is required. Please enter a value.")

def setup_core_config():
    """Setup core Flask configuration"""
    print("\n🔧 Core Configuration")
    print("-" * 30)
    
    config = {}
    
    # Generate secure keys
    print("Generating secure keys...")
    config['SECRET_KEY'] = generate_secure_key(64)
    config['SECURITY_KEY'] = generate_secure_key(32)
    
    # Environment
    env = get_user_input("Environment (development/production)", "development", required=False)
    config['FLASK_ENV'] = env
    config['FLASK_DEBUG'] = 'True' if env == 'development' else 'False'
    config['DEBUG'] = 'True' if env == 'development' else 'False'
    
    return config

def setup_database_config():
    """Setup database configuration"""
    print("\n🗄️ Database Configuration")
    print("-" * 30)
    
    config = {}
    
    db_type = get_user_input("Database type (sqlite/postgresql/mysql)", "sqlite", required=False)
    
    if db_type == 'sqlite':
        db_name = get_user_input("Database filename", "cybersecurity_platform.db", required=False)
        config['DATABASE_URL'] = f"sqlite:///{db_name}"
        config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{db_name}"
    
    elif db_type == 'postgresql':
        host = get_user_input("PostgreSQL host", "localhost", required=False)
        port = get_user_input("PostgreSQL port", "5432", required=False)
        username = get_user_input("PostgreSQL username", required=True)
        password = get_user_input("PostgreSQL password", required=True, password=True)
        database = get_user_input("PostgreSQL database name", "amadioha_m257", required=False)
        
        db_url = f"postgresql://{username}:{password}@{host}:{port}/{database}"
        config['DATABASE_URL'] = db_url
        config['SQLALCHEMY_DATABASE_URI'] = db_url
    
    elif db_type == 'mysql':
        host = get_user_input("MySQL host", "localhost", required=False)
        port = get_user_input("MySQL port", "3306", required=False)
        username = get_user_input("MySQL username", required=True)
        password = get_user_input("MySQL password", required=True, password=True)
        database = get_user_input("MySQL database name", "amadioha_m257", required=False)
        
        db_url = f"mysql://{username}:{password}@{host}:{port}/{database}"
        config['DATABASE_URL'] = db_url
        config['SQLALCHEMY_DATABASE_URI'] = db_url
    
    return config

def setup_api_keys():
    """Setup API keys"""
    print("\n🔑 API Keys Configuration")
    print("-" * 30)
    
    config = {}
    
    print("\nCybersecurity APIs (optional but recommended):")
    
    # VirusTotal
    vt_key = get_user_input("VirusTotal API Key", required=False)
    if vt_key:
        config['VIRUSTOTAL_API_KEY'] = vt_key
    
    # Shodan
    shodan_key = get_user_input("Shodan API Key", required=False)
    if shodan_key:
        config['SHODAN_API_KEY'] = shodan_key
    
    # Censys
    censys_id = get_user_input("Censys API ID", required=False)
    if censys_id:
        config['CENSYS_API_ID'] = censys_id
        censys_secret = get_user_input("Censys API Secret", required=False)
        if censys_secret:
            config['CENSYS_API_SECRET'] = censys_secret
    
    print("\nPhone Tracking APIs (optional):")
    
    # NumVerify
    numverify_key = get_user_input("NumVerify API Key", required=False)
    if numverify_key:
        config['NUMVERIFY_API_KEY'] = numverify_key
    
    # OpenCellID
    opencellid_key = get_user_input("OpenCellID API Key", required=False)
    if opencellid_key:
        config['OPENCELLID_API_KEY'] = opencellid_key
    
    # IPGeolocation
    ipgeo_key = get_user_input("IPGeolocation API Key", required=False)
    if ipgeo_key:
        config['IPGEOLOCATION_API_KEY'] = ipgeo_key
    
    return config

def setup_email_config():
    """Setup email configuration"""
    print("\n📧 Email Configuration")
    print("-" * 30)
    
    config = {}
    
    setup_email = get_user_input("Setup email notifications? (y/n)", "n", required=False).lower()
    
    if setup_email == 'y':
        smtp_server = get_user_input("SMTP Server", "smtp.gmail.com", required=False)
        smtp_port = get_user_input("SMTP Port", "587", required=False)
        smtp_username = get_user_input("SMTP Username/Email", required=True)
        smtp_password = get_user_input("SMTP Password", required=True, password=True)
        email_from = get_user_input("From Email", smtp_username, required=False)
        
        config.update({
            'SMTP_SERVER': smtp_server,
            'SMTP_PORT': smtp_port,
            'SMTP_USE_TLS': 'True',
            'SMTP_USERNAME': smtp_username,
            'SMTP_PASSWORD': smtp_password,
            'EMAIL_FROM': email_from
        })
    
    return config

def setup_telegram_config():
    """Setup Telegram bot configuration"""
    print("\n🤖 Telegram Bot Configuration")
    print("-" * 30)
    
    config = {}
    
    setup_telegram = get_user_input("Setup Telegram bot? (y/n)", "n", required=False).lower()
    
    if setup_telegram == 'y':
        print("\nTo setup Telegram bot:")
        print("1. Message @BotFather on Telegram")
        print("2. Use /newbot command to create a bot")
        print("3. Get the bot token")
        print("4. Add bot to your group/channel")
        print("5. Get chat ID from bot messages")
        
        bot_token = get_user_input("Telegram Bot Token", required=True)
        chat_id = get_user_input("Telegram Chat ID", required=True)
        
        config.update({
            'TELEGRAM_BOT_TOKEN': bot_token,
            'TELEGRAM_CHAT_ID': chat_id,
            'TELEGRAM_ENABLE_LOGGING': 'True'
        })
    
    return config

def setup_security_config():
    """Setup security configuration"""
    print("\n🔒 Security Configuration")
    print("-" * 30)
    
    config = {}
    
    # Session timeout
    session_timeout = get_user_input("Session timeout (seconds)", "3600", required=False)
    config['SESSION_TIMEOUT'] = session_timeout
    
    # Login attempts
    max_attempts = get_user_input("Max login attempts", "5", required=False)
    config['MAX_LOGIN_ATTEMPTS'] = max_attempts
    
    # Lockout duration
    lockout_duration = get_user_input("Login lockout duration (seconds)", "900", required=False)
    config['LOGIN_LOCKOUT_DURATION'] = lockout_duration
    
    return config

def create_env_file(config):
    """Create .env file with configuration"""
    print("\n📝 Creating .env file...")
    
    # Backup existing .env if it exists
    if os.path.exists('.env'):
        backup_path = '.env.backup'
        counter = 1
        while os.path.exists(backup_path):
            backup_path = f'.env.backup.{counter}'
            counter += 1
        
        os.rename('.env', backup_path)
        print(f"Existing .env backed up to {backup_path}")
    
    # Read the comprehensive template
    template_content = """# ==========================================
# AMADIOHA-M257 v1.0.0 - BMD CYBER OPS
# Comprehensive Environment Configuration
# ==========================================

# ==========================================
# CORE FLASK CONFIGURATION
# ==========================================
SECRET_KEY={SECRET_KEY}
FLASK_ENV={FLASK_ENV}
FLASK_DEBUG={FLASK_DEBUG}
FLASK_APP=app.py

# ==========================================
# DATABASE CONFIGURATION
# ==========================================
DATABASE_URL={DATABASE_URL}
SQLALCHEMY_DATABASE_URI={SQLALCHEMY_DATABASE_URI}
SQLALCHEMY_TRACK_MODIFICATIONS=False

# ==========================================
# SECURITY CONFIGURATION
# ==========================================
SECURITY_KEY={SECURITY_KEY}
SESSION_TIMEOUT={SESSION_TIMEOUT}
MAX_LOGIN_ATTEMPTS={MAX_LOGIN_ATTEMPTS}
LOGIN_LOCKOUT_DURATION={LOGIN_LOCKOUT_DURATION}

# ==========================================
# CYBERSECURITY API KEYS
# ==========================================
VIRUSTOTAL_API_KEY={VIRUSTOTAL_API_KEY}
SHODAN_API_KEY={SHODAN_API_KEY}
CENSYS_API_ID={CENSYS_API_ID}
CENSYS_API_SECRET={CENSYS_API_SECRET}

# ==========================================
# PHONE TRACKING API KEYS
# ==========================================
NUMVERIFY_API_KEY={NUMVERIFY_API_KEY}
OPENCELLID_API_KEY={OPENCELLID_API_KEY}
IPGEOLOCATION_API_KEY={IPGEOLOCATION_API_KEY}
TRUECALLER_API_KEY={TRUECALLER_API_KEY}

# ==========================================
# EMAIL & SMTP CONFIGURATION
# ==========================================
SMTP_SERVER={SMTP_SERVER}
SMTP_PORT={SMTP_PORT}
SMTP_USE_TLS={SMTP_USE_TLS}
SMTP_USERNAME={SMTP_USERNAME}
SMTP_PASSWORD={SMTP_PASSWORD}
EMAIL_FROM={EMAIL_FROM}

# ==========================================
# TELEGRAM BOT CONFIGURATION
# ==========================================
TELEGRAM_BOT_TOKEN={TELEGRAM_BOT_TOKEN}
TELEGRAM_CHAT_ID={TELEGRAM_CHAT_ID}
TELEGRAM_ENABLE_LOGGING={TELEGRAM_ENABLE_LOGGING}

# ==========================================
# DEVELOPMENT SETTINGS
# ==========================================
DEBUG={DEBUG}
TESTING=False
DEVELOPMENT_MODE=True

# ==========================================
# FILE UPLOAD & STORAGE
# ==========================================
MAX_CONTENT_LENGTH=104857600
UPLOAD_FOLDER=uploads
TEMP_FOLDER=temp
ALLOWED_EXTENSIONS=pdf,xlsx,docx,txt,exe,dll,zip,rar

# ==========================================
# LOGGING CONFIGURATION
# ==========================================
LOG_LEVEL=INFO
LOG_FILE=logs/amadioha_m257.log
AUDIT_LOG_ENABLED=True
AUDIT_LOG_FILE=logs/audit.log

# ==========================================
# FEATURE FLAGS
# ==========================================
ENABLE_PHONE_TRACKING=True
ENABLE_REAL_MALWARE=True
ENABLE_CREDENTIAL_HARVESTING=True
ENABLE_PDF_EXPLOITS=True
ENABLE_SOCIAL_ENGINEERING=True
ENABLE_NETWORK_PENETRATION=True
"""
    
    # Set default values for missing keys
    defaults = {
        'SECRET_KEY': generate_secure_key(64),
        'SECURITY_KEY': generate_secure_key(32),
        'FLASK_ENV': 'development',
        'FLASK_DEBUG': 'True',
        'DEBUG': 'True',
        'DATABASE_URL': 'sqlite:///cybersecurity_platform.db',
        'SQLALCHEMY_DATABASE_URI': 'sqlite:///cybersecurity_platform.db',
        'SESSION_TIMEOUT': '3600',
        'MAX_LOGIN_ATTEMPTS': '5',
        'LOGIN_LOCKOUT_DURATION': '900',
        'VIRUSTOTAL_API_KEY': '',
        'SHODAN_API_KEY': '',
        'CENSYS_API_ID': '',
        'CENSYS_API_SECRET': '',
        'NUMVERIFY_API_KEY': '',
        'OPENCELLID_API_KEY': '',
        'IPGEOLOCATION_API_KEY': '',
        'TRUECALLER_API_KEY': '',
        'SMTP_SERVER': '',
        'SMTP_PORT': '',
        'SMTP_USE_TLS': '',
        'SMTP_USERNAME': '',
        'SMTP_PASSWORD': '',
        'EMAIL_FROM': '',
        'TELEGRAM_BOT_TOKEN': '',
        'TELEGRAM_CHAT_ID': '',
        'TELEGRAM_ENABLE_LOGGING': ''
    }
    
    # Merge config with defaults
    final_config = {**defaults, **config}
    
    # Format template with config values
    env_content = template_content.format(**final_config)
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("✅ .env file created successfully!")

def main():
    """Main setup function"""
    print("🚀 AMADIOHA-M257 Environment Setup")
    print("=" * 50)
    print("This script will help you configure your cybersecurity platform.")
    print("You can skip optional configurations and add them later.\n")
    
    # Collect all configuration
    config = {}
    
    config.update(setup_core_config())
    config.update(setup_database_config())
    config.update(setup_api_keys())
    config.update(setup_email_config())
    config.update(setup_telegram_config())
    config.update(setup_security_config())
    
    # Create .env file
    create_env_file(config)
    
    # Create necessary directories
    print("\n📁 Creating directories...")
    directories = ['uploads', 'temp', 'logs', 'backups']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Review and edit .env file if needed")
    print("2. Run: python validate_config.py")
    print("3. Run: python run.py")
    print("4. Access at: http://localhost:5000")
    print("\n🔒 Default credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("\n⚠️  IMPORTANT:")
    print("- Change default credentials after first login")
    print("- Add API keys for full functionality")
    print("- Use only for authorized cybersecurity testing")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Setup interrupted by user")
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        import traceback
        traceback.print_exc()
