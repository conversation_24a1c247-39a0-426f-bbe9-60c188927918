@echo off
echo 🚀 AMADIOHA-M257 CYBER OPS SERVER STARTUP
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Please install Python first.
    echo.
    echo 📦 Install from Microsoft Store: ms-windows-store://pdp/?productid=9NRWMJP3717K
    echo    OR from: https://www.python.org/downloads/
    echo.
    echo After installation, run this script again.
    pause
    exit /b 1
)

echo ✅ Python found
python --version
echo.

REM Install/upgrade pip
echo 📦 Upgrading pip...
python -m pip install --upgrade pip

REM Install minimal requirements first
echo 📦 Installing core requirements...
pip install -r requirements_minimal.txt

REM Try to install additional requirements (ignore failures)
echo 📦 Installing additional requirements (optional)...
pip install -r requirements.txt --ignore-installed --no-deps 2>nul

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️ .env file not found. Please ensure .env is configured.
    pause
    exit /b 1
)

echo.
echo 🎯 AMADIOHA-M257 CYBER OPS PLATFORM
echo ====================================
echo 📱 Phone Operations: Location tracking, SMS campaigns, Voice calls
echo 🦠 Malware Operations: FUD malware, keyloggers, document weaponization
echo 🎣 Phishing Operations: Website cloning, credential harvesting
echo 🌐 Web Scraping: Automatic logo/asset extraction
echo.

REM Create instance directory if it doesn't exist
if not exist "instance" mkdir instance

REM Start the server
echo 🚀 Starting AMADIOHA-M257 server...
echo 🌐 Access at: http://localhost:5000
echo.
python run.py

pause
