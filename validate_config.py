#!/usr/bin/env python3
"""
AMADIOHA-M257 Configuration Validation Script
Validates environment configuration and API connectivity
"""

import os
import sys
import requests
import sqlite3
from dotenv import load_dotenv
import phonenumbers
from phonenumbers import geocoder, carrier
import smtplib
from email.mime.text import MIMEText
import json

# Load environment variables
load_dotenv()

class ConfigValidator:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.success = []
        
    def log_error(self, message):
        self.errors.append(f"❌ {message}")
        print(f"❌ {message}")
    
    def log_warning(self, message):
        self.warnings.append(f"⚠️  {message}")
        print(f"⚠️  {message}")
    
    def log_success(self, message):
        self.success.append(f"✅ {message}")
        print(f"✅ {message}")
    
    def validate_core_config(self):
        """Validate core Flask configuration"""
        print("\n🔧 Validating Core Configuration...")
        
        # Check SECRET_KEY
        secret_key = os.getenv('SECRET_KEY')
        if not secret_key or secret_key == 'your-secret-key-here':
            self.log_error("SECRET_KEY not configured or using default value")
        elif len(secret_key) < 32:
            self.log_warning("SECRET_KEY should be at least 32 characters long")
        else:
            self.log_success("SECRET_KEY configured properly")
        
        # Check SECURITY_KEY
        security_key = os.getenv('SECURITY_KEY')
        if not security_key:
            self.log_error("SECURITY_KEY not configured")
        elif len(security_key) < 16:
            self.log_warning("SECURITY_KEY should be at least 16 characters long")
        else:
            self.log_success("SECURITY_KEY configured properly")
        
        # Check Flask environment
        flask_env = os.getenv('FLASK_ENV', 'production')
        if flask_env == 'development':
            self.log_warning("Running in development mode - change to 'production' for live deployment")
        else:
            self.log_success(f"Flask environment: {flask_env}")
    
    def validate_database(self):
        """Validate database configuration"""
        print("\n🗄️ Validating Database Configuration...")
        
        db_url = os.getenv('DATABASE_URL', os.getenv('SQLALCHEMY_DATABASE_URI'))
        if not db_url:
            self.log_error("Database URL not configured")
            return
        
        if db_url.startswith('sqlite:'):
            # Test SQLite connection
            try:
                db_path = db_url.replace('sqlite:///', '')
                # Try to create/connect to database
                conn = sqlite3.connect(db_path)
                conn.execute('SELECT 1')
                conn.close()
                self.log_success("SQLite database connection successful")
            except Exception as e:
                self.log_error(f"SQLite database connection failed: {e}")
        
        elif db_url.startswith('postgresql:'):
            self.log_warning("PostgreSQL configured - ensure server is running and accessible")
        
        elif db_url.startswith('mysql:'):
            self.log_warning("MySQL configured - ensure server is running and accessible")
        
        else:
            self.log_warning(f"Unknown database type: {db_url}")
    
    def validate_api_keys(self):
        """Validate external API keys"""
        print("\n🔑 Validating API Keys...")
        
        # VirusTotal
        vt_key = os.getenv('VIRUSTOTAL_API_KEY')
        if not vt_key or vt_key == 'your-virustotal-api-key-here':
            self.log_warning("VirusTotal API key not configured")
        else:
            try:
                response = requests.get(
                    f"https://www.virustotal.com/vtapi/v2/file/report",
                    params={'apikey': vt_key, 'resource': 'test'},
                    timeout=10
                )
                if response.status_code == 200:
                    self.log_success("VirusTotal API key valid")
                else:
                    self.log_error(f"VirusTotal API key invalid (Status: {response.status_code})")
            except Exception as e:
                self.log_warning(f"VirusTotal API test failed: {e}")
        
        # Shodan
        shodan_key = os.getenv('SHODAN_API_KEY')
        if not shodan_key or shodan_key == 'your-shodan-api-key-here':
            self.log_warning("Shodan API key not configured")
        else:
            try:
                response = requests.get(
                    f"https://api.shodan.io/api-info?key={shodan_key}",
                    timeout=10
                )
                if response.status_code == 200:
                    self.log_success("Shodan API key valid")
                else:
                    self.log_error(f"Shodan API key invalid (Status: {response.status_code})")
            except Exception as e:
                self.log_warning(f"Shodan API test failed: {e}")
        
        # Phone Tracking APIs
        self.validate_phone_apis()
    
    def validate_phone_apis(self):
        """Validate phone tracking API keys"""
        print("\n📱 Validating Phone Tracking APIs...")
        
        # NumVerify
        numverify_key = os.getenv('NUMVERIFY_API_KEY')
        if not numverify_key or numverify_key == 'your-numverify-api-key-here':
            self.log_warning("NumVerify API key not configured")
        else:
            try:
                response = requests.get(
                    f"http://apilayer.net/api/validate",
                    params={'access_key': numverify_key, 'number': '+1234567890'},
                    timeout=10
                )
                if response.status_code == 200:
                    self.log_success("NumVerify API key valid")
                else:
                    self.log_error(f"NumVerify API key invalid (Status: {response.status_code})")
            except Exception as e:
                self.log_warning(f"NumVerify API test failed: {e}")
        
        # Test phonenumbers library
        try:
            test_number = phonenumbers.parse("+1234567890", None)
            country = geocoder.description_for_number(test_number, 'en')
            carrier_name = carrier.name_for_number(test_number, 'en')
            self.log_success("Phone number parsing library working")
        except Exception as e:
            self.log_error(f"Phone number parsing failed: {e}")
    
    def validate_email_config(self):
        """Validate email/SMTP configuration"""
        print("\n📧 Validating Email Configuration...")
        
        smtp_server = os.getenv('SMTP_SERVER')
        smtp_port = os.getenv('SMTP_PORT', '587')
        smtp_username = os.getenv('SMTP_USERNAME')
        smtp_password = os.getenv('SMTP_PASSWORD')
        
        if not all([smtp_server, smtp_username, smtp_password]):
            self.log_warning("SMTP configuration incomplete - email features may not work")
            return
        
        try:
            # Test SMTP connection
            server = smtplib.SMTP(smtp_server, int(smtp_port))
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.quit()
            self.log_success("SMTP configuration valid")
        except Exception as e:
            self.log_error(f"SMTP connection failed: {e}")
    
    def validate_telegram_config(self):
        """Validate Telegram bot configuration"""
        print("\n🤖 Validating Telegram Configuration...")
        
        bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        chat_id = os.getenv('TELEGRAM_CHAT_ID')
        
        if not bot_token or bot_token == 'your-telegram-bot-token-here':
            self.log_warning("Telegram bot token not configured")
            return
        
        if not chat_id or chat_id == 'your-telegram-chat-id-here':
            self.log_warning("Telegram chat ID not configured")
            return
        
        try:
            # Test bot API
            response = requests.get(
                f"https://api.telegram.org/bot{bot_token}/getMe",
                timeout=10
            )
            if response.status_code == 200:
                bot_info = response.json()
                if bot_info.get('ok'):
                    self.log_success(f"Telegram bot valid: {bot_info['result']['username']}")
                else:
                    self.log_error("Telegram bot token invalid")
            else:
                self.log_error(f"Telegram API error (Status: {response.status_code})")
        except Exception as e:
            self.log_warning(f"Telegram API test failed: {e}")
    
    def validate_file_paths(self):
        """Validate file paths and directories"""
        print("\n📁 Validating File Paths...")
        
        # Check upload directory
        upload_folder = os.getenv('UPLOAD_FOLDER', 'uploads')
        if not os.path.exists(upload_folder):
            try:
                os.makedirs(upload_folder)
                self.log_success(f"Created upload directory: {upload_folder}")
            except Exception as e:
                self.log_error(f"Cannot create upload directory: {e}")
        else:
            self.log_success(f"Upload directory exists: {upload_folder}")
        
        # Check temp directory
        temp_folder = os.getenv('TEMP_FOLDER', 'temp')
        if not os.path.exists(temp_folder):
            try:
                os.makedirs(temp_folder)
                self.log_success(f"Created temp directory: {temp_folder}")
            except Exception as e:
                self.log_error(f"Cannot create temp directory: {e}")
        else:
            self.log_success(f"Temp directory exists: {temp_folder}")
        
        # Check logs directory
        log_file = os.getenv('LOG_FILE', 'logs/amadioha_m257.log')
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir)
                self.log_success(f"Created logs directory: {log_dir}")
            except Exception as e:
                self.log_error(f"Cannot create logs directory: {e}")
    
    def validate_security_settings(self):
        """Validate security settings"""
        print("\n🔒 Validating Security Settings...")
        
        # Check if using default keys
        secret_key = os.getenv('SECRET_KEY', '')
        if 'your-secret-key' in secret_key.lower() or 'change-this' in secret_key.lower():
            self.log_error("Using default SECRET_KEY - change immediately!")
        
        security_key = os.getenv('SECURITY_KEY', '')
        if 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' in security_key:
            self.log_error("Using default SECURITY_KEY - change immediately!")
        
        # Check session timeout
        session_timeout = os.getenv('SESSION_TIMEOUT', '3600')
        try:
            timeout = int(session_timeout)
            if timeout < 300:
                self.log_warning("Session timeout very short (< 5 minutes)")
            elif timeout > 86400:
                self.log_warning("Session timeout very long (> 24 hours)")
            else:
                self.log_success(f"Session timeout: {timeout} seconds")
        except ValueError:
            self.log_error("Invalid session timeout value")
        
        # Check debug mode
        debug = os.getenv('DEBUG', 'False').lower() == 'true'
        flask_debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        if debug or flask_debug:
            self.log_warning("Debug mode enabled - disable in production!")
        else:
            self.log_success("Debug mode disabled")
    
    def generate_report(self):
        """Generate validation report"""
        print("\n" + "="*60)
        print("📊 CONFIGURATION VALIDATION REPORT")
        print("="*60)
        
        print(f"\n✅ Successful Checks: {len(self.success)}")
        for item in self.success:
            print(f"  {item}")
        
        print(f"\n⚠️  Warnings: {len(self.warnings)}")
        for item in self.warnings:
            print(f"  {item}")
        
        print(f"\n❌ Errors: {len(self.errors)}")
        for item in self.errors:
            print(f"  {item}")
        
        print("\n" + "="*60)
        
        if self.errors:
            print("🚨 CRITICAL: Fix errors before running the platform!")
            return False
        elif self.warnings:
            print("⚠️  WARNING: Review warnings for optimal configuration")
            return True
        else:
            print("🎉 SUCCESS: Configuration looks good!")
            return True

def main():
    """Main validation function"""
    print("🔍 AMADIOHA-M257 Configuration Validator")
    print("="*50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Create .env file from the template and configure your settings")
        sys.exit(1)
    
    validator = ConfigValidator()
    
    # Run all validations
    validator.validate_core_config()
    validator.validate_database()
    validator.validate_api_keys()
    validator.validate_email_config()
    validator.validate_telegram_config()
    validator.validate_file_paths()
    validator.validate_security_settings()
    
    # Generate report
    success = validator.generate_report()
    
    if not success:
        print("\n🔧 Configuration Help:")
        print("1. Review CONFIG_GUIDE.md for detailed setup instructions")
        print("2. Check API key documentation for each service")
        print("3. Verify network connectivity and firewall settings")
        print("4. Test individual components before full deployment")
        sys.exit(1)
    else:
        print("\n🚀 Ready to launch AMADIOHA-M257!")
        print("Run: python run.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Validation interrupted by user")
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
