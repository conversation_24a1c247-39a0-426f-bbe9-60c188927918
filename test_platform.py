#!/usr/bin/env python3
"""
Test script for Phishing Awareness Platform
"""

import requests
import json
from utils.attachment_generator import AttachmentGenerator
from utils.website_cloner import WebsiteCloner

def test_attachment_generator():
    """Test attachment generation functionality"""
    print("🧪 Testing Attachment Generator...")
    
    try:
        generator = AttachmentGenerator()
        
        # Test PDF generation
        pdf_result = generator.generate_attachment("PDF", "invoice")
        print(f"✅ PDF generated: {pdf_result['hash'][:16]}...")
        
        # Test XLSX generation
        xlsx_result = generator.generate_attachment("XLSX", "budget")
        print(f"✅ XLSX generated: {xlsx_result['hash'][:16]}...")
        
        # Test DOCX generation
        docx_result = generator.generate_attachment("DOCX", "policy")
        print(f"✅ DOCX generated: {docx_result['hash'][:16]}...")
        
        return True
    except Exception as e:
        print(f"❌ Attachment generator test failed: {e}")
        return False

def test_website_cloner():
    """Test website cloning functionality"""
    print("🧪 Testing Website Cloner...")
    
    try:
        cloner = WebsiteCloner()
        
        # Test with a simple, safe website
        test_url = "https://httpbin.org/html"
        result = cloner.get_consent_status(test_url)
        
        print(f"✅ Consent check: {result[1]}")
        
        if result[0]:
            clone_result = cloner.clone_website(test_url)
            if clone_result['success']:
                print(f"✅ Website cloned successfully")
                return True
            else:
                print(f"❌ Website cloning failed: {clone_result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"⚠️  Consent check failed: {result[1]}")
            return False
            
    except Exception as e:
        print(f"❌ Website cloner test failed: {e}")
        return False

def test_flask_app():
    """Test Flask application"""
    print("🧪 Testing Flask Application...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test home page
            response = client.get('/')
            if response.status_code == 200:
                print("✅ Home page accessible")
            else:
                print(f"❌ Home page failed: {response.status_code}")
                return False
            
            # Test login page
            response = client.get('/login')
            if response.status_code == 200:
                print("✅ Login page accessible")
            else:
                print(f"❌ Login page failed: {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ Flask app test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Phishing Awareness Platform Tests...")
    print("=" * 50)
    
    tests = [
        ("Flask Application", test_flask_app),
        ("Attachment Generator", test_attachment_generator),
        ("Website Cloner", test_website_cloner)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} test passed")
        else:
            print(f"❌ {test_name} test failed")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Platform is ready to use.")
        print("\n📝 To start the platform:")
        print("   python run.py")
        print("\n🌐 Access at: http://localhost:5000")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main() 