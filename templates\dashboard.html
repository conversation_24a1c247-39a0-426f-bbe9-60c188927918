{% extends 'base.html' %}
{% block title %}Dashboard | AMADIOHA-M257{% endblock %}
{% block content %}
<style>
    .stats-card {
        background: #000000;
        color: #ffffff;
        border-radius: 15px;
        border: none;
        box-shadow:
            10px 10px 20px rgba(0,0,0,0.5),
            -10px -10px 20px rgba(50,50,50,0.1),
            inset 0 0 0 2px #28a745;
        padding: 1.5rem;
        margin: 7px;
        transition: all 0.3s ease;
        position: relative;
    }
    .stats-card:hover {
        box-shadow:
            18px 18px 36px #1a1c22,
            -18px -18px 36px #2e3240,
            inset 0 0 0 2px #28a745;
        transform: translateY(-3px);
    }
    .stats-card .circle-arrow {
        position: absolute;
        top: 1.2rem;
        right: 1.2rem;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 1.2rem;
        box-shadow: 0 2px 8px rgba(124,58,237,0.12);
        transition: box-shadow 0.2s, background 0.2s;
    }
    .stats-card .circle-arrow:hover {
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        box-shadow: 0 4px 16px rgba(124,58,237,0.18);
    }
    .card, .tool-card {
        background: #000000;
        border: none;
        border-radius: 15px;
        box-shadow:
            8px 8px 16px rgba(0,0,0,0.5),
            -8px -8px 16px rgba(50,50,50,0.1),
            inset 0 0 0 1.8px #dc3545;
        transition: all 0.3s ease;
        margin: 7px;
        color: #ffffff;
    }
    .card:hover, .tool-card:hover {
        box-shadow:
            15px 15px 30px #1a1c22,
            -15px -15px 30px #2e3240,
            inset 0 0 0 1.8px #dc3545;
        transform: translateY(-2px);
    }

    /* Enhanced neumorphic button effects */
    .btn-neumorphic {
        background: linear-gradient(145deg, #2a2d3a, #1e2028);
        border: none;
        border-radius: 10px;
        box-shadow:
            8px 8px 16px #1a1c22,
            -8px -8px 16px #2e3240;
        transition: all 0.2s ease;
        color: var(--text-primary);
    }

    .btn-neumorphic:hover {
        box-shadow:
            6px 6px 12px #1a1c22,
            -6px -6px 12px #2e3240;
        transform: translateY(-1px);
        color: var(--text-primary);
    }

    .btn-neumorphic:active {
        box-shadow:
            inset 4px 4px 8px #1a1c22,
            inset -4px -4px 8px #2e3240;
        transform: translateY(1px);
    }
    .tool-card {
        cursor: pointer;
        border: var(--border-width) solid transparent;
        position: relative;
    }
    .tool-card .circle-arrow {
        position: absolute;
        top: 1.2rem;
        right: 1.2rem;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 1.2rem;
        box-shadow: 0 2px 8px rgba(124,58,237,0.12);
        transition: box-shadow 0.2s, background 0.2s;
    }
    .tool-card .circle-arrow:hover {
        background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
        box-shadow: 0 4px 16px rgba(124,58,237,0.18);
    }
    .btn-cyber {
        background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
        border: var(--border-width) solid var(--border-color);
        color: #fff;
        border-radius: var(--border-radius);
        font-weight: 600;
        transition: background 0.2s, box-shadow 0.2s;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    .btn-cyber .fa-arrow-right, .btn-cyber .fa-arrow-circle-right {
        font-size: 1.1rem;
        margin-left: 0.2rem;
    }
    .btn-cyber:hover {
        background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
        box-shadow: 0 4px 16px rgba(124,58,237,0.18);
    }
    .alert-cyber {
        background: linear-gradient(45deg, var(--danger-color), #ee5a24);
        color: white;
        border: none;
        border-radius: var(--border-radius);
    }
    .file-upload-area {
        border: var(--border-width) dashed var(--primary-color);
        border-radius: var(--border-radius);
        padding: 40px;
        text-align: center;
        background: rgba(124,58,237,0.08);
        transition: all 0.3s ease;
    }
    .file-upload-area:hover {
        background: rgba(124,58,237,0.15);
        border-color: var(--accent-color);
    }
    .scan-result {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        border-left: 4px solid var(--primary-color);
        color: var(--text-primary);
    }
    .risk-high {
        border-left-color: var(--danger-color);
    }
    .risk-medium {
        border-left-color: var(--warning-color);
    }
    .risk-low {
        border-left-color: var(--success-color);
    }

    /* Ensure all text is visible */
    .card-title, .card-text, .stats-card h3, .stats-card p,
    .table, .table td, .table th, .list-group-item,
    .badge, .btn, .form-control, .form-label {
        color: #ffffff !important;
    }

    .table {
        background: #000000 !important;
    }

    .table td, .table th {
        border-color: #333333 !important;
        background: #000000 !important;
    }

    .list-group-item {
        background: #000000 !important;
        border-color: #333333 !important;
    }

    .badge {
        background: #dc3545 !important;
    }
</style>
<div class="alert alert-cyber mb-4">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>CYBERSECURITY OPERATIONS CENTER:</strong>
    Professional security tools for threat detection, analysis, and response.
    All operations are logged and monitored for security compliance.
</div>
<div class="row">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-search fa-3x mb-3 text-primary"></i>
            <h3 id="total-scans">{{ total_scans or 0 }}</h3>
            <p>Security Scans</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
            <h3 id="open-incidents">{{ open_incidents or 0 }}</h3>
            <p>Active Incidents</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-shield-alt fa-3x mb-3 text-danger"></i>
            <h3 id="high-threats">{{ high_severity_threats or 0 }}</h3>
            <p>Critical Threats</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-satellite-dish fa-3x mb-3 text-success"></i>
            <h3 id="active-campaigns">0</h3>
            <p>Live Campaigns</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
</div>

<!-- Additional Augie-Pentest H1 Metrics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fab fa-bitcoin fa-3x mb-3 text-warning"></i>
            <h3 id="bitcoin-analyses">0</h3>
            <p>Bitcoin Analyses</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-coins fa-3x mb-3 text-info"></i>
            <h3 id="blockchain-scans">0</h3>
            <p>Blockchain Scans</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-mobile-alt fa-3x mb-3 text-primary"></i>
            <h3 id="phone-tracking">0</h3>
            <p>Phone Tracking</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-key fa-3x mb-3 text-danger"></i>
            <h3 id="harvested-creds">0</h3>
            <p>Harvested Creds</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-bug fa-3x mb-3 text-warning"></i>
            <h3 id="cve-count">0</h3>
            <p>CVE Database</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-network-wired fa-3x mb-3 text-success"></i>
            <h3 id="network-scans">0</h3>
            <p>Network Scans</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-virus fa-3x mb-3 text-danger"></i>
            <h3 id="malware-deployments">0</h3>
            <p>Malware Active</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <i class="fas fa-users fa-3x mb-3 text-info"></i>
            <h3 id="users-count">0</h3>
            <p>Active Users</p>
            <div class="circle-arrow"><i class="fas fa-arrow-circle-right"></i></div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Security Metrics</h5>
            </div>
            <div class="card-body">
                <canvas id="securityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Recent Activity</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for scan in recent_scans[:5] %}
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between">
                            <small>{{ scan.scan_type|title }}</small>
                            <span class="badge bg-{{ 'danger' if scan.risk_level == 'high' else 'warning' if scan.risk_level == 'medium' else 'success' }}">
                                {{ scan.risk_level|upper }}
                            </span>
                        </div>
                        <small class="text-muted">{{ scan.target }}</small>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> Cybersecurity Tools</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- File Encryption/Decryption -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-lock text-primary"></i> File Encryption/Decryption</h6>
                            <p class="text-muted">AES-256 encryption for secure file protection</p>

                            <div class="file-upload-area" id="encrypt-upload">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h6>Encrypt File</h6>
                                <input type="file" id="encrypt-file" class="form-control mb-3" style="display: none;">
                                <input type="password" id="encrypt-password" class="form-control mb-3" placeholder="Enter encryption password">
                                <button class="btn btn-cyber" onclick="encryptFile()">
                                    <i class="fas fa-lock"></i> Encrypt File
                                </button>
                            </div>

                            <div class="file-upload-area mt-3" id="decrypt-upload">
                                <i class="fas fa-cloud-download-alt fa-3x text-success mb-3"></i>
                                <h6>Decrypt File</h6>
                                <input type="file" id="decrypt-file" class="form-control mb-3" style="display: none;">
                                <input type="password" id="decrypt-password" class="form-control mb-3" placeholder="Enter decryption password">
                                <button class="btn btn-cyber" onclick="decryptFile()">
                                    <i class="fas fa-unlock"></i> Decrypt File
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Port Scanning -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-network-wired text-warning"></i> Port Scanner</h6>
                            <p class="text-muted">Network reconnaissance and service enumeration</p>

                            <div class="mb-3">
                                <label class="form-label">Target IP/Hostname</label>
                                <input type="text" id="port-scan-target" class="form-control" placeholder="***********">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Port Range</label>
                                <input type="text" id="port-scan-ports" class="form-control" placeholder="1-1000" value="1-1000">
                            </div>
                            <button class="btn btn-cyber" onclick="portScan()">
                                <i class="fas fa-search"></i> Start Port Scan
                            </button>

                            <div id="port-scan-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Web Security Scanner -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-globe text-info"></i> Web Security Scanner</h6>
                            <p class="text-muted">Comprehensive web application security assessment</p>

                            <div class="mb-3">
                                <label class="form-label">Target URL</label>
                                <input type="url" id="web-scan-url" class="form-control" placeholder="https://example.com">
                            </div>
                            <button class="btn btn-cyber" onclick="webScan()">
                                <i class="fas fa-shield-alt"></i> Scan Website
                            </button>

                            <div id="web-scan-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- File Analysis -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-file-alt text-danger"></i> File Analysis</h6>
                            <p class="text-muted">Malware detection and file integrity analysis</p>

                            <div class="file-upload-area" id="file-analysis-upload">
                                <i class="fas fa-file-upload fa-3x text-danger mb-3"></i>
                                <h6>Upload File for Analysis</h6>
                                <input type="file" id="analysis-file" class="form-control mb-3" style="display: none;">
                                <button class="btn btn-cyber" onclick="analyzeFile()">
                                    <i class="fas fa-search"></i> Analyze File
                                </button>
                            </div>

                            <div id="file-analysis-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Network Reconnaissance -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-sitemap text-success"></i> Network Reconnaissance</h6>
                            <p class="text-muted">Network topology and host discovery</p>

                            <div class="mb-3">
                                <label class="form-label">Network Range</label>
                                <input type="text" id="network-target" class="form-control" placeholder="***********/24">
                            </div>
                            <button class="btn btn-cyber" onclick="networkRecon()">
                                <i class="fas fa-network-wired"></i> Start Reconnaissance
                            </button>

                            <div id="network-recon-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- CVE Search -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-bug text-warning"></i> CVE Database Search</h6>
                            <p class="text-muted">Vulnerability intelligence and threat research</p>

                            <div class="mb-3">
                                <label class="form-label">Search Query</label>
                                <input type="text" id="cve-query" class="form-control" placeholder="Apache, Windows, etc.">
                            </div>
                            <button class="btn btn-cyber" onclick="searchCVE()">
                                <i class="fas fa-search"></i> Search CVEs
                            </button>

                            <div id="cve-search-results" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- ==================== PENETRATION TESTING TOOLS ==================== -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-crosshairs text-danger"></i> Penetration Testing Tools</h5>
                <small class="text-muted">Advanced security testing for networks, servers, devices, websites, databases, and systems</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Network Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-network-wired text-danger"></i> Network Penetration Testing</h6>
                            <p class="text-muted">Comprehensive network vulnerability assessment and exploitation</p>

                            <div class="mb-3">
                                <label class="form-label">Target Network</label>
                                <input type="text" id="network-pen-target" class="form-control" placeholder="***********/24">
                            </div>
                            <button class="btn btn-cyber" onclick="networkPenetration()">
                                <i class="fas fa-crosshairs"></i> Start Network Penetration
                            </button>

                            <div id="network-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Server Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-server text-warning"></i> Server Penetration Testing</h6>
                            <p class="text-muted">SSH, FTP, Telnet, and web server vulnerability assessment</p>

                            <div class="mb-3">
                                <label class="form-label">Target Server</label>
                                <input type="text" id="server-pen-target" class="form-control" placeholder="***********00">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Credentials (Optional)</label>
                                <input type="text" id="server-username" class="form-control mb-2" placeholder="Username">
                                <input type="password" id="server-password" class="form-control" placeholder="Password">
                            </div>
                            <button class="btn btn-cyber" onclick="serverPenetration()">
                                <i class="fas fa-server"></i> Test Server Security
                            </button>

                            <div id="server-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Device Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-mobile-alt text-info"></i> Device Penetration Testing</h6>
                            <p class="text-muted">IoT devices, embedded systems, and network appliances</p>

                            <div class="mb-3">
                                <label class="form-label">Target Device</label>
                                <input type="text" id="device-pen-target" class="form-control" placeholder="192.168.1.50">
                            </div>
                            <button class="btn btn-cyber" onclick="devicePenetration()">
                                <i class="fas fa-mobile-alt"></i> Test Device Security
                            </button>

                            <div id="device-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Website Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-globe text-primary"></i> Website Penetration Testing</h6>
                            <p class="text-muted">SQL injection, XSS, CSRF, and web application vulnerabilities</p>

                            <div class="mb-3">
                                <label class="form-label">Target Website</label>
                                <input type="url" id="website-pen-url" class="form-control" placeholder="https://example.com">
                            </div>
                            <button class="btn btn-cyber" onclick="websitePenetration()">
                                <i class="fas fa-globe"></i> Test Website Security
                            </button>

                            <div id="website-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Database Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-database text-success"></i> Database Penetration Testing</h6>
                            <p class="text-muted">MySQL, PostgreSQL, MongoDB, and SQLite security assessment</p>

                            <div class="mb-3">
                                <label class="form-label">Target Database</label>
                                <input type="text" id="db-pen-target" class="form-control" placeholder="192.168.1.200">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Credentials (Optional)</label>
                                <input type="text" id="db-username" class="form-control mb-2" placeholder="Username">
                                <input type="password" id="db-password" class="form-control" placeholder="Password">
                            </div>
                            <button class="btn btn-cyber" onclick="databasePenetration()">
                                <i class="fas fa-database"></i> Test Database Security
                            </button>

                            <div id="db-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Windows OS Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-desktop text-secondary"></i> Windows OS Penetration Testing</h6>
                            <p class="text-muted">Windows system enumeration, privilege escalation, and security analysis</p>

                            <div class="mb-3">
                                <label class="form-label">Target Windows System</label>
                                <input type="text" id="windows-pen-target" class="form-control" placeholder="***********0">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Credentials (Optional)</label>
                                <input type="text" id="windows-username" class="form-control mb-2" placeholder="Username">
                                <input type="password" id="windows-password" class="form-control" placeholder="Password">
                            </div>
                            <button class="btn btn-cyber" onclick="windowsPenetration()">
                                <i class="fas fa-desktop"></i> Test Windows Security
                            </button>

                            <div id="windows-pen-results" class="mt-3"></div>
                        </div>
                    </div>

                    <!-- Log Analysis Penetration Testing -->
                    <div class="col-md-6 mb-4">
                        <div class="tool-card card p-4">
                            <h6><i class="fas fa-file-alt text-warning"></i> Log Analysis Penetration Testing</h6>
                            <p class="text-muted">Security log analysis, anomaly detection, and forensic investigation</p>

                            <div class="mb-3">
                                <label class="form-label">Target System</label>
                                <input type="text" id="log-pen-target" class="form-control" placeholder="***********00">
                            </div>
                            <button class="btn btn-cyber" onclick="logPenetration()">
                                <i class="fas fa-file-alt"></i> Analyze Security Logs
                            </button>

                            <div id="log-pen-results" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-terminal"></i> Security Terminal</h5>
            </div>
            <div class="card-body">
                <div class="terminal-card" id="terminal">
                    <div id="terminal-output">
                        <span class="text-success">$</span> Welcome to Cybersecurity Operations Center<br>
                        <span class="text-success">$</span> Type 'help' for available commands<br>
                    </div>
                    <div class="input-group mt-3">
                        <input type="text" class="form-control bg-dark text-success" id="terminal-input"
                               placeholder="Enter command..." onkeypress="handleTerminalInput(event)">
                        <button class="btn btn-success" onclick="executeCommand()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle"></i> Active Incidents</h5>
            </div>
            <div class="card-body">
                {% if active_incidents %}
                    {% for incident in active_incidents %}
                    <div class="scan-result risk-{{ 'high' if incident.priority == 'high' else 'medium' if incident.priority == 'medium' else 'low' }}">
                        <h6>{{ incident.incident_type }}</h6>
                        <p class="mb-2">{{ incident.description }}</p>
                        <small class="text-muted">
                            Priority: {{ incident.priority|upper }} |
                            Created: {{ incident.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">No active incidents</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
    // Security Metrics Chart
    const ctx = document.getElementById('securityChart').getContext('2d');
    const securityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
                label: 'Security Scans',
                data: [45, 52, 38, 67, 58, 73],
                borderColor: var(--primary-color),
                backgroundColor: 'rgba(124,58,237,0.1)',
                tension: 0.4
            }, {
                label: 'Threats Detected',
                data: [12, 18, 15, 25, 22, 30],
                borderColor: var(--danger-color),
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Terminal functionality
    function handleTerminalInput(event) {
        if (event.key === 'Enter') {
            executeCommand();
        }
    }

    function executeCommand() {
        const input = document.getElementById('terminal-input');
        const command = input.value.trim();
        const output = document.getElementById('terminal-output');

        if (command) {
            output.innerHTML += `<span class="text-success">$</span> ${command}<br>`;

            // Process commands
            if (command === 'help') {
                output.innerHTML += `Available commands:<br>
                - help: Show this help<br>
                - status: Show system status<br>
                - scans: List recent scans<br>
                - threats: Show active threats<br>
                - clear: Clear terminal<br>`;
            } else if (command === 'status') {
                output.innerHTML += `System Status: Online<br>
                Total Scans: {{ total_scans }}<br>
                Open Incidents: {{ open_incidents }}<br>
                High Severity Threats: {{ high_severity_threats }}<br>`;
            } else if (command === 'scans') {
                output.innerHTML += `Recent Scans:<br>
                {% for scan in recent_scans[:3] %}
                - {{ scan.scan_type }}: {{ scan.target }} ({{ scan.risk_level }})<br>
                {% endfor %}`;
            } else if (command === 'threats') {
                output.innerHTML += `Active Threats:<br>
                {% for threat in recent_threats[:3] %}
                - {{ threat.threat_type }}: {{ threat.indicator }} ({{ threat.severity }})<br>
                {% endfor %}`;
            } else if (command === 'clear') {
                output.innerHTML = '';
            } else {
                output.innerHTML += `Command not found: ${command}<br>`;
            }

            input.value = '';
            output.scrollTop = output.scrollHeight;
        }
    }

    // File Upload Handlers
    document.getElementById('encrypt-upload').addEventListener('click', () => {
        document.getElementById('encrypt-file').click();
    });

    document.getElementById('decrypt-upload').addEventListener('click', () => {
        document.getElementById('decrypt-file').click();
    });

    document.getElementById('file-analysis-upload').addEventListener('click', () => {
        document.getElementById('analysis-file').click();
    });

    // Encryption/Decryption Functions
    function encryptFile() {
        const fileInput = document.getElementById('encrypt-file');
        const password = document.getElementById('encrypt-password').value;

        if (!fileInput.files[0]) {
            alert('Please select a file to encrypt');
            return;
        }

        if (!password) {
            alert('Please enter an encryption password');
            return;
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        formData.append('password', password);

        fetch('/api/encrypt_file', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                return response.json();
            }
        })
        .then(data => {
            if (data instanceof Blob) {
                // Download encrypted file
                const url = window.URL.createObjectURL(data);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileInput.files[0].name + '.encrypted';
                a.click();
                window.URL.revokeObjectURL(url);
                alert('File encrypted successfully!');
            } else {
                alert('Encryption failed: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }

    function decryptFile() {
        const fileInput = document.getElementById('decrypt-file');
        const password = document.getElementById('decrypt-password').value;

        if (!fileInput.files[0]) {
            alert('Please select a file to decrypt');
            return;
        }

        if (!password) {
            alert('Please enter a decryption password');
            return;
        }

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        formData.append('password', password);

        fetch('/api/decrypt_file', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.blob();
            } else {
                return response.json();
            }
        })
        .then(data => {
            if (data instanceof Blob) {
                // Download decrypted file
                const url = window.URL.createObjectURL(data);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileInput.files[0].name.replace('.encrypted', '.decrypted');
                a.click();
                window.URL.revokeObjectURL(url);
                alert('File decrypted successfully!');
            } else {
                alert('Decryption failed: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error: ' + error);
        });
    }

    // Port Scanning
    function portScan() {
        const target = document.getElementById('port-scan-target').value;
        const ports = document.getElementById('port-scan-ports').value;
        const resultsDiv = document.getElementById('port-scan-results');

        if (!target) {
            alert('Please enter a target');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Scanning ports...</div>';

        fetch('/api/port_scan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target,
                ports: ports
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Scan completed!</div>';
                html += '<h6>Open Ports:</h6>';
                data.results.forEach(port => {
                    if (port.state === 'open') {
                        html += `<div class="scan-result risk-high">
                            <strong>Port ${port.port}</strong> (${port.service})
                            ${port.version ? '- ' + port.version : ''}
                        </div>`;
                    }
                });
                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Scan failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Web Security Scanning
    function webScan() {
        const url = document.getElementById('web-scan-url').value;
        const resultsDiv = document.getElementById('web-scan-results');

        if (!url) {
            alert('Please enter a URL');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Scanning website...</div>';

        fetch('/api/web_scan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Scan completed!</div>';

                // SSL Info
                if (data.results.ssl_info && !data.results.ssl_info.error) {
                    html += '<h6>SSL Certificate:</h6>';
                    html += `<div class="scan-result risk-low">
                        <strong>Issuer:</strong> ${Object.values(data.results.ssl_info.issuer).join(', ')}<br>
                        <strong>Valid Until:</strong> ${data.results.ssl_info.not_after}
                    </div>`;
                }

                // Vulnerabilities
                if (data.results.vulnerabilities.length > 0) {
                    html += '<h6>Security Issues:</h6>';
                    data.results.vulnerabilities.forEach(vuln => {
                        html += `<div class="scan-result risk-high">${vuln}</div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Scan failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // File Analysis
    function analyzeFile() {
        const fileInput = document.getElementById('analysis-file');
        const resultsDiv = document.getElementById('file-analysis-results');

        if (!fileInput.files[0]) {
            alert('Please select a file to analyze');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Analyzing file...</div>';

        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        fetch('/api/file_analysis', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Analysis completed!</div>';

                // File Info
                html += '<h6>File Information:</h6>';
                html += `<div class="scan-result risk-low">
                    <strong>Size:</strong> ${data.results.file_info.size} bytes<br>
                    <strong>Type:</strong> ${data.results.file_info.type}<br>
                    <strong>MD5:</strong> ${data.results.file_info.hashes.md5}<br>
                    <strong>SHA256:</strong> ${data.results.file_info.hashes.sha256}
                </div>`;

                // Malware Indicators
                if (data.results.malware_indicators.length > 0) {
                    html += '<h6>Malware Indicators:</h6>';
                    data.results.malware_indicators.forEach(indicator => {
                        html += `<div class="scan-result risk-high">${indicator}</div>`;
                    });
                }

                // YARA Matches
                if (data.results.yara_matches.length > 0) {
                    html += '<h6>YARA Rule Matches:</h6>';
                    data.results.yara_matches.forEach(match => {
                        html += `<div class="scan-result risk-medium">${match}</div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Analysis failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Network Reconnaissance
    function networkRecon() {
        const target = document.getElementById('network-target').value;
        const resultsDiv = document.getElementById('network-recon-results');

        if (!target) {
            alert('Please enter a network range');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing reconnaissance...</div>';

        fetch('/api/network_recon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Reconnaissance completed!</div>';

                // Host Discovery
                html += '<h6>Discovered Hosts:</h6>';
                data.results.host_discovery.forEach(host => {
                    html += `<div class="scan-result risk-low">
                        <strong>IP:</strong> ${host.ip}<br>
                        <strong>MAC:</strong> ${host.mac || 'Unknown'}
                    </div>`;
                });

                // Service Enumeration
                if (data.results.service_enumeration.length > 0) {
                    html += '<h6>Services Found:</h6>';
                    data.results.service_enumeration.forEach(service => {
                        if (service.state === 'open') {
                            html += `<div class="scan-result risk-medium">
                                <strong>${service.host}:${service.port}</strong> (${service.service})
                            </div>`;
                        }
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Reconnaissance failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // CVE Search
    function searchCVE() {
        const query = document.getElementById('cve-query').value;
        const resultsDiv = document.getElementById('cve-search-results');

        if (!query) {
            alert('Please enter a search query');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Searching CVEs...</div>';

        fetch('/api/cve_search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: query
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Search completed!</div>';

                if (data.results.length > 0) {
                    html += '<h6>Vulnerabilities Found:</h6>';
                    data.results.forEach(cve => {
                        const riskClass = cve.severity === 'HIGH' ? 'risk-high' :
                                       cve.severity === 'MEDIUM' ? 'risk-medium' : 'risk-low';
                        html += `<div class="scan-result ${riskClass}">
                            <strong>${cve.cve_id}</strong> (${cve.severity})<br>
                            <small>${cve.description.substring(0, 100)}...</small>
                        </div>`;
                    });
                } else {
                    html += '<div class="alert alert-info">No CVEs found for this query</div>';
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Search failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // ==================== PENETRATION TESTING FUNCTIONS ====================

    // Network Penetration Testing
    function networkPenetration() {
        const target = document.getElementById('network-pen-target').value;
        const resultsDiv = document.getElementById('network-pen-results');

        if (!target) {
            alert('Please enter a target network');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing network penetration testing...</div>';

        fetch('/api/network_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Network penetration test completed!</div>';

                // Network mapping results
                if (data.results.network_mapping) {
                    html += '<h6>Network Mapping:</h6>';
                    Object.keys(data.results.network_mapping).forEach(host => {
                        const hostInfo = data.results.network_mapping[host];
                        html += `<div class="scan-result risk-medium">
                            <strong>Host:</strong> ${host}<br>
                            <strong>OS:</strong> ${hostInfo.os_detection || 'Unknown'}<br>
                            <strong>Open Ports:</strong> ${hostInfo.open_ports ? hostInfo.open_ports.length : 0}
                        </div>`;
                    });
                }

                // Vulnerability scan results
                if (data.results.vulnerability_scan && data.results.vulnerability_scan.length > 0) {
                    html += '<h6>Vulnerabilities Found:</h6>';
                    data.results.vulnerability_scan.forEach(vuln => {
                        html += `<div class="scan-result risk-high">${vuln.description}</div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Network penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Server Penetration Testing
    function serverPenetration() {
        const target = document.getElementById('server-pen-target').value;
        const username = document.getElementById('server-username').value;
        const password = document.getElementById('server-password').value;
        const resultsDiv = document.getElementById('server-pen-results');

        if (!target) {
            alert('Please enter a target server');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing server penetration testing...</div>';

        const credentials = username && password ? { username, password } : {};

        fetch('/api/server_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target,
                credentials: credentials
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Server penetration test completed!</div>';

                // SSH Analysis
                if (data.results.ssh_analysis && Object.keys(data.results.ssh_analysis).length > 0) {
                    html += '<h6>SSH Analysis:</h6>';
                    const ssh = data.results.ssh_analysis;
                    if (ssh.successful_logins && ssh.successful_logins.length > 0) {
                        html += `<div class="scan-result risk-high">
                            <strong>Successful Logins:</strong> ${ssh.successful_logins.length} found
                        </div>`;
                    }
                }

                // FTP Analysis
                if (data.results.ftp_analysis && Object.keys(data.results.ftp_analysis).length > 0) {
                    html += '<h6>FTP Analysis:</h6>';
                    const ftp = data.results.ftp_analysis;
                    if (ftp.anonymous_access) {
                        html += `<div class="scan-result risk-high">Anonymous FTP access enabled</div>`;
                    }
                }

                // Web Server Analysis
                if (data.results.web_server_analysis && Object.keys(data.results.web_server_analysis).length > 0) {
                    html += '<h6>Web Server Analysis:</h6>';
                    const web = data.results.web_server_analysis;
                    if (web.vulnerabilities && web.vulnerabilities.length > 0) {
                        web.vulnerabilities.forEach(vuln => {
                            html += `<div class="scan-result risk-${vuln.severity}">${vuln.description}</div>`;
                        });
                    }
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Server penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Device Penetration Testing
    function devicePenetration() {
        const target = document.getElementById('device-pen-target').value;
        const resultsDiv = document.getElementById('device-pen-results');

        if (!target) {
            alert('Please enter a target device');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing device penetration testing...</div>';

        fetch('/api/device_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Device penetration test completed!</div>';

                // Device discovery results
                if (data.results.device_discovery && data.results.device_discovery.length > 0) {
                    html += '<h6>Discovered Devices:</h6>';
                    data.results.device_discovery.forEach(device => {
                        html += `<div class="scan-result risk-medium">
                            <strong>IP:</strong> ${device.ip}<br>
                            <strong>Type:</strong> ${device.device_type}<br>
                            <strong>Open Ports:</strong> ${device.open_tcp_ports.length + device.open_udp_ports.length}
                        </div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Device penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Website Penetration Testing
    function websitePenetration() {
        const url = document.getElementById('website-pen-url').value;
        const resultsDiv = document.getElementById('website-pen-results');

        if (!url) {
            alert('Please enter a target website');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing website penetration testing...</div>';

        fetch('/api/website_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: url
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Website penetration test completed!</div>';

                // Information gathering
                if (data.results.information_gathering) {
                    html += '<h6>Information Gathering:</h6>';
                    const info = data.results.information_gathering;
                    if (info.technologies && info.technologies.length > 0) {
                        html += `<div class="scan-result risk-low">
                            <strong>Technologies:</strong> ${info.technologies.join(', ')}
                        </div>`;
                    }
                }

                // SQL Injection results
                if (data.results.sql_injection_test && data.results.sql_injection_test.length > 0) {
                    html += '<h6>SQL Injection Vulnerabilities:</h6>';
                    data.results.sql_injection_test.forEach(vuln => {
                        html += `<div class="scan-result risk-high">
                            <strong>Type:</strong> ${vuln.type}<br>
                            <strong>Evidence:</strong> ${vuln.evidence}
                        </div>`;
                    });
                }

                // XSS results
                if (data.results.xss_test && data.results.xss_test.length > 0) {
                    html += '<h6>XSS Vulnerabilities:</h6>';
                    data.results.xss_test.forEach(vuln => {
                        html += `<div class="scan-result risk-high">
                            <strong>Type:</strong> ${vuln.type}<br>
                            <strong>Evidence:</strong> ${vuln.evidence}
                        </div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Website penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Database Penetration Testing
    function databasePenetration() {
        const target = document.getElementById('db-pen-target').value;
        const username = document.getElementById('db-username').value;
        const password = document.getElementById('db-password').value;
        const resultsDiv = document.getElementById('db-pen-results');

        if (!target) {
            alert('Please enter a target database');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing database penetration testing...</div>';

        const credentials = username && password ? { username, password } : {};

        fetch('/api/database_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target,
                credentials: credentials
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Database penetration test completed!</div>';

                // MySQL Analysis
                if (data.results.mysql_analysis && data.results.mysql_analysis.connected) {
                    html += '<h6>MySQL Analysis:</h6>';
                    const mysql = data.results.mysql_analysis;
                    html += `<div class="scan-result risk-medium">
                        <strong>Version:</strong> ${mysql.version}<br>
                        <strong>Databases:</strong> ${mysql.databases ? mysql.databases.length : 0}<br>
                        <strong>Users:</strong> ${mysql.users ? mysql.users.length : 0}
                    </div>`;
                }

                // PostgreSQL Analysis
                if (data.results.postgresql_analysis && data.results.postgresql_analysis.connected) {
                    html += '<h6>PostgreSQL Analysis:</h6>';
                    const pg = data.results.postgresql_analysis;
                    html += `<div class="scan-result risk-medium">
                        <strong>Version:</strong> ${pg.version}<br>
                        <strong>Databases:</strong> ${pg.databases ? pg.databases.length : 0}<br>
                        <strong>Users:</strong> ${pg.users ? pg.users.length : 0}
                    </div>`;
                }

                // MongoDB Analysis
                if (data.results.mongodb_analysis && data.results.mongodb_analysis.connected) {
                    html += '<h6>MongoDB Analysis:</h6>';
                    const mongo = data.results.mongodb_analysis;
                    html += `<div class="scan-result risk-medium">
                        <strong>Version:</strong> ${mongo.version}<br>
                        <strong>Databases:</strong> ${mongo.databases ? mongo.databases.length : 0}<br>
                        <strong>Collections:</strong> ${mongo.collections ? mongo.collections.length : 0}
                    </div>`;
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Database penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Windows OS Penetration Testing
    function windowsPenetration() {
        const target = document.getElementById('windows-pen-target').value;
        const username = document.getElementById('windows-username').value;
        const password = document.getElementById('windows-password').value;
        const resultsDiv = document.getElementById('windows-pen-results');

        if (!target) {
            alert('Please enter a target Windows system');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing Windows OS penetration testing...</div>';

        const credentials = username && password ? { username, password } : {};

        fetch('/api/windows_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target,
                credentials: credentials
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Windows OS penetration test completed!</div>';

                // System information
                if (data.results.system_info) {
                    html += '<h6>System Information:</h6>';
                    const sys = data.results.system_info;
                    html += `<div class="scan-result risk-low">
                        <strong>Computer Name:</strong> ${sys.computer_name}<br>
                        <strong>OS Version:</strong> ${sys.os_version}
                    </div>`;
                }

                // User enumeration
                if (data.results.user_enumeration && data.results.user_enumeration.length > 0) {
                    html += '<h6>User Enumeration:</h6>';
                    data.results.user_enumeration.forEach(user => {
                        html += `<div class="scan-result risk-medium">
                            <strong>Username:</strong> ${user.username}<br>
                            <strong>Status:</strong> ${user.status}
                        </div>`;
                    });
                }

                // Service enumeration
                if (data.results.service_enumeration && data.results.service_enumeration.length > 0) {
                    html += '<h6>Service Enumeration:</h6>';
                    data.results.service_enumeration.forEach(service => {
                        html += `<div class="scan-result risk-medium">
                            <strong>Service:</strong> ${service.name}<br>
                            <strong>Status:</strong> ${service.status}
                        </div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Windows OS penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Log Analysis Penetration Testing
    function logPenetration() {
        const target = document.getElementById('log-pen-target').value;
        const resultsDiv = document.getElementById('log-pen-results');

        if (!target) {
            alert('Please enter a target system');
            return;
        }

        resultsDiv.innerHTML = '<div class="alert alert-info">Performing log analysis penetration testing...</div>';

        fetch('/api/log_penetration', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                target: target
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div class="alert alert-success">Log analysis penetration test completed!</div>';

                // Log files found
                if (data.results.log_files && data.results.log_files.length > 0) {
                    html += '<h6>Log Files Found:</h6>';
                    data.results.log_files.forEach(log => {
                        html += `<div class="scan-result risk-low">
                            <strong>File:</strong> ${log.file_path}<br>
                            <strong>Entries:</strong> ${log.entries ? log.entries.length : 0}
                        </div>`;
                    });
                }

                // Pattern analysis
                if (data.results.pattern_analysis) {
                    html += '<h6>Pattern Analysis:</h6>';
                    const patterns = data.results.pattern_analysis;
                    html += `<div class="scan-result risk-medium">
                        <strong>Failed Logins:</strong> ${patterns.failed_logins}<br>
                        <strong>Successful Logins:</strong> ${patterns.successful_logins}<br>
                        <strong>Error Messages:</strong> ${patterns.error_messages}
                    </div>`;
                }

                // Anomaly detection
                if (data.results.anomaly_detection && data.results.anomaly_detection.length > 0) {
                    html += '<h6>Anomalies Detected:</h6>';
                    data.results.anomaly_detection.forEach(anomaly => {
                        html += `<div class="scan-result risk-high">${anomaly}</div>`;
                    });
                }

                resultsDiv.innerHTML = html;
            } else {
                resultsDiv.innerHTML = `<div class="alert alert-danger">Log analysis penetration test failed: ${data.error}</div>`;
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = `<div class="alert alert-danger">Error: ${error}</div>`;
        });
    }

    // Real-Time Dashboard functionality
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Real-Time Dashboard loaded');

        // Initialize real-time updates
        initializeRealTimeDashboard();

        // Update dashboard every 30 seconds
        setInterval(updateDashboardStats, 30000);
    });

    function initializeRealTimeDashboard() {
        // Add enhanced hover effects to cards
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Initial data load
        updateDashboardStats();
    }

    function updateDashboardStats() {
        // Fetch real-time statistics from database
        fetch('/api/dashboard_stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats with smooth animation - NO MOCK DATA
                    animateCounterUpdate('total-scans', data.total_scans || 0);
                    animateCounterUpdate('open-incidents', data.open_incidents || 0);
                    animateCounterUpdate('high-threats', data.high_severity_threats || 0);
                    animateCounterUpdate('active-campaigns', data.active_campaigns || 0);

                    // Update additional Augie-Pentest H1 metrics
                    animateCounterUpdate('cve-count', data.cve_count || 0);
                    animateCounterUpdate('users-count', data.users_count || 0);
                    animateCounterUpdate('phone-tracking', data.phone_tracking_sessions || 0);
                    animateCounterUpdate('harvested-creds', data.harvested_credentials || 0);
                    animateCounterUpdate('bitcoin-analyses', data.bitcoin_analyses || 0);
                    animateCounterUpdate('blockchain-scans', data.blockchain_scans || 0);
                    animateCounterUpdate('network-scans', data.network_scans || 0);
                    animateCounterUpdate('malware-deployments', data.malware_deployments || 0);

                    // Update framework version display
                    const versionElement = document.getElementById('framework-version');
                    if (versionElement) {
                        versionElement.textContent = data.framework_version || 'Augie-Pentest H1 v2.0';
                    }

                    const poweredByElement = document.getElementById('powered-by');
                    if (poweredByElement) {
                        poweredByElement.textContent = data.powered_by || 'Augment AI';
                    }

                    // Update last refresh time
                    updateLastRefreshTime();
                }
            })
            .catch(error => {
                console.log('Dashboard update in progress...');
                // Graceful fallback - keep current values
            });
    }

    function animateCounterUpdate(elementId, newValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const currentValue = parseInt(element.textContent) || 0;

        if (currentValue !== newValue) {
            // Smooth counter animation
            const duration = 1000;
            const steps = 20;
            const stepValue = (newValue - currentValue) / steps;
            const stepDuration = duration / steps;

            let currentStep = 0;
            const timer = setInterval(() => {
                currentStep++;
                const displayValue = Math.round(currentValue + (stepValue * currentStep));
                element.textContent = displayValue;

                if (currentStep >= steps) {
                    clearInterval(timer);
                    element.textContent = newValue;
                }
            }, stepDuration);

            // Add pulse effect for changes
            element.parentElement.style.animation = 'pulse 0.5s ease-in-out';
            setTimeout(() => {
                element.parentElement.style.animation = '';
            }, 500);
        }
    }

    function updateLastRefreshTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();

        // Update refresh indicator
        let refreshIndicator = document.getElementById('last-refresh');
        if (!refreshIndicator) {
            refreshIndicator = document.createElement('small');
            refreshIndicator.id = 'last-refresh';
            refreshIndicator.className = 'text-muted';
            refreshIndicator.style.cssText = `
                position: fixed;
                bottom: 10px;
                right: 10px;
                background: rgba(0,0,0,0.7);
                padding: 5px 10px;
                border-radius: 15px;
                font-size: 0.75rem;
                z-index: 1000;
            `;
            document.body.appendChild(refreshIndicator);
        }

        refreshIndicator.textContent = `Live: ${timeString}`;
    }
</script>

{% endblock %}
