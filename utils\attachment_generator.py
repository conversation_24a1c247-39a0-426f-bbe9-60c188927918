import os
import io
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
import hashlib

class AttachmentGenerator:
    """Generate benign phishing attachments for educational purposes"""
    
    def __init__(self):
        self.styles = getSampleStyleSheet()
    
    def generate_pdf(self, filename="document.pdf", content_type="invoice"):
        """Generate a benign PDF document"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=letter)
        story = []
        
        # Add title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        if content_type == "invoice":
            title = Paragraph("INVOICE", title_style)
            story.append(title)
            
            # Add invoice content
            story.append(Paragraph("Invoice #: INV-2024-001", self.styles['Normal']))
            story.append(Paragraph("Date: " + datetime.now().strftime("%B %d, %Y"), self.styles['Normal']))
            story.append(Spacer(1, 12))
            
            story.append(Paragraph("Bill To:", self.styles['Heading2']))
            story.append(Paragraph("Company Name<br/>123 Business St<br/>City, State 12345", self.styles['Normal']))
            story.append(Spacer(1, 12))
            
            story.append(Paragraph("Description:", self.styles['Heading2']))
            story.append(Paragraph("IT Security Services - Phishing Awareness Training", self.styles['Normal']))
            story.append(Paragraph("Amount: $1,500.00", self.styles['Normal']))
            
        elif content_type == "report":
            title = Paragraph("SECURITY REPORT", title_style)
            story.append(title)
            
            story.append(Paragraph("Executive Summary:", self.styles['Heading2']))
            story.append(Paragraph("This document contains confidential security information regarding recent phishing attempts and employee training results.", self.styles['Normal']))
            story.append(Spacer(1, 12))
            
            story.append(Paragraph("Key Findings:", self.styles['Heading2']))
            story.append(Paragraph("• 67% of employees clicked on simulated phishing emails<br/>• 23% submitted credentials to fake login pages<br/>• Training effectiveness increased by 45% after awareness program", self.styles['Normal']))
            
        else:  # Default document
            title = Paragraph("IMPORTANT DOCUMENT", title_style)
            story.append(title)
            
            story.append(Paragraph("This is a sample document generated for educational purposes.", self.styles['Normal']))
            story.append(Spacer(1, 12))
            story.append(Paragraph("Please review the attached information and take appropriate action.", self.styles['Normal']))
        
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_xlsx(self, filename="spreadsheet.xlsx", content_type="budget"):
        """Generate a benign Excel spreadsheet"""
        wb = Workbook()
        ws = wb.active
        
        if content_type == "budget":
            ws.title = "Budget 2024"
            
            # Headers
            headers = ['Department', 'Q1', 'Q2', 'Q3', 'Q4', 'Total']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Data
            data = [
                ['IT Security', 50000, 55000, 60000, 65000, 230000],
                ['Training', 25000, 30000, 35000, 40000, 130000],
                ['Infrastructure', 75000, 80000, 85000, 90000, 330000],
                ['Software', 40000, 45000, 50000, 55000, 190000]
            ]
            
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)
            
        elif content_type == "employee_data":
            ws.title = "Employee List"
            
            headers = ['ID', 'Name', 'Department', 'Email', 'Phone']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            data = [
                ['EMP001', 'John Doe', 'IT', '<EMAIL>', '555-0101'],
                ['EMP002', 'Jane Smith', 'HR', '<EMAIL>', '555-0102'],
                ['EMP003', 'Bob Johnson', 'Finance', '<EMAIL>', '555-0103'],
                ['EMP004', 'Alice Brown', 'Marketing', '<EMAIL>', '555-0104']
            ]
            
            for row, row_data in enumerate(data, 2):
                for col, value in enumerate(row_data, 1):
                    ws.cell(row=row, column=col, value=value)
        
        else:  # Default spreadsheet
            ws.title = "Data"
            ws['A1'] = "Sample Data"
            ws['A2'] = "This is a sample spreadsheet for educational purposes."
        
        buffer = io.BytesIO()
        wb.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()
    
    def generate_docx(self, filename="document.docx", content_type="policy"):
        """Generate a benign Word document"""
        doc = Document()
        
        if content_type == "policy":
            # Add title
            title = doc.add_heading('COMPANY SECURITY POLICY', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add content
            doc.add_paragraph("This document outlines the company's security policies and procedures.")
            doc.add_paragraph("")
            
            doc.add_heading('Password Requirements', level=1)
            doc.add_paragraph("• Minimum 8 characters")
            doc.add_paragraph("• Must contain uppercase and lowercase letters")
            doc.add_paragraph("• Must contain numbers and special characters")
            doc.add_paragraph("• Change passwords every 90 days")
            
            doc.add_heading('Email Security', level=1)
            doc.add_paragraph("• Never click on suspicious links")
            doc.add_paragraph("• Verify sender email addresses")
            doc.add_paragraph("• Report suspicious emails to IT")
            doc.add_paragraph("• Don't open attachments from unknown sources")
            
        elif content_type == "report":
            doc.add_heading('QUARTERLY SECURITY REPORT', 0)
            doc.add_paragraph("Prepared by: IT Security Team")
            doc.add_paragraph("Date: " + datetime.now().strftime("%B %d, %Y"))
            doc.add_paragraph("")
            
            doc.add_heading('Executive Summary', level=1)
            doc.add_paragraph("This quarter, we conducted comprehensive security assessments and phishing awareness training.")
            
            doc.add_heading('Key Metrics', level=1)
            doc.add_paragraph("• Phishing simulation success rate: 67%")
            doc.add_paragraph("• Employees trained: 150")
            doc.add_paragraph("• Security incidents: 3 (down from 12)")
            doc.add_paragraph("• Training completion rate: 95%")
            
        else:  # Default document
            doc.add_heading('IMPORTANT DOCUMENT', 0)
            doc.add_paragraph("This is a sample document generated for educational purposes.")
            doc.add_paragraph("")
            doc.add_paragraph("Please review the information contained in this document and take appropriate action as necessary.")
        
        buffer = io.BytesIO()
        doc.save(buffer)
        buffer.seek(0)
        return buffer.getvalue()
    
    def get_file_hash(self, file_content):
        """Generate SHA256 hash of file content"""
        return hashlib.sha256(file_content).hexdigest()
    
    def generate_attachment(self, file_type, content_type="default"):
        """Generate attachment based on type and content"""
        if file_type.upper() == "PDF":
            content = self.generate_pdf(content_type=content_type)
        elif file_type.upper() == "XLSX":
            content = self.generate_xlsx(content_type=content_type)
        elif file_type.upper() == "DOCX":
            content = self.generate_docx(content_type=content_type)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
        
        file_hash = self.get_file_hash(content)
        return {
            'content': content,
            'hash': file_hash,
            'size': len(content),
            'type': file_type.upper()
        }

# Example usage
if __name__ == "__main__":
    generator = AttachmentGenerator()
    
    # Generate sample attachments
    pdf_attachment = generator.generate_attachment("PDF", "invoice")
    xlsx_attachment = generator.generate_attachment("XLSX", "budget")
    docx_attachment = generator.generate_attachment("DOCX", "policy")
    
    print(f"Generated PDF: {pdf_attachment['hash']}")
    print(f"Generated XLSX: {xlsx_attachment['hash']}")
    print(f"Generated DOCX: {docx_attachment['hash']}") 