{% extends "base.html" %}

{% block title %}New Campaign - AMADIOHA-M257{% endblock %}

{% block content %}
<style>
.card {
    background: var(--card-bg);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: box-shadow 0.2s, transform 0.2s;
    animation: fadeInUp 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.card:hover {
    box-shadow: 0 12px 36px rgba(124,58,237,0.18), var(--shadow);
    transform: scale(1.015);
}
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(40px); }
    to { opacity: 1; transform: translateY(0); }
}
.form-control {
    background: var(--dark-bg);
    border: var(--border-width) solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius);
}
.form-control:focus {
    background: var(--dark-bg);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.2rem rgba(124,58,237,0.15);
}
.btn-cyber, .btn-primary, .btn-secondary {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border: var(--border-width) solid var(--border-color);
    color: #fff;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: background 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.btn-cyber:hover, .btn-primary:hover, .btn-secondary:hover {
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    box-shadow: 0 4px 16px rgba(124,58,237,0.18);
}
</style>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-plus"></i> New Campaign</h2>
        </div>
    </div>
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bullhorn"></i> Campaign Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Campaign Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="template_type" class="form-label">Template Type *</label>
                                <select class="form-control" id="template_type" name="template_type" required>
                                    <option value="">Select type...</option>
                                    <option value="email">Email Template</option>
                                    <option value="web">Web Template</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="Brief description of the campaign purpose..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="target_url" class="form-label">Target URL</label>
                            <input type="url" class="form-control" id="target_url" name="target_url" 
                                   placeholder="https://example.com">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i> URL to clone or redirect to (optional)
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Security Settings</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="consent_required" checked>
                                    <label class="form-check-label" for="consent_required">
                                        Require consent before execution
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="logging_enabled" checked>
                                    <label class="form-check-label" for="logging_enabled">
                                        Enable detailed logging
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Notification Settings</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" checked>
                                    <label class="form-check-label" for="email_notifications">
                                        Email notifications
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="dashboard_notifications" checked>
                                    <label class="form-check-label" for="dashboard_notifications">
                                        Dashboard notifications
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('campaigns') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Campaign
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 
