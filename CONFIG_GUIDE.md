# 🔧 AMADIOHA-M257 Configuration Guide

## 📋 Overview

This guide provides comprehensive configuration instructions for the AMADIOHA-M257 cybersecurity platform environment variables and settings.

## 🚀 Quick Start

1. **Copy the configuration template:**
   ```bash
   cp .env .env.backup  # Backup existing config
   # Use the new comprehensive .env file
   ```

2. **Configure essential settings:**
   - Update `SECRET_KEY` and `SECURITY_KEY`
   - Add your API keys
   - Configure database settings
   - Set up email/SMTP if needed

3. **Test the configuration:**
   ```bash
   python test_phone_tracking.py
   ```

## 🔑 Essential Configuration Sections

### 1. Core Flask Settings
```env
SECRET_KEY=AMADIOHA-M257-ULTRA-SECURE-KEY-CHANGE-IN-PRODUCTION-2024
FLASK_ENV=development  # Change to 'production' for live deployment
FLASK_DEBUG=True       # Set to False in production
```

### 2. Database Configuration
```env
DATABASE_URL=sqlite:///cybersecurity_platform.db
SQLALCHEMY_DATABASE_URI=sqlite:///cybersecurity_platform.db
```

**For Production (PostgreSQL):**
```env
DATABASE_URL=postgresql://user:password@localhost:5432/amadioha_m257
SQLALCHEMY_DATABASE_URI=postgresql://user:password@localhost:5432/amadioha_m257
```

### 3. Security Settings
```env
SECURITY_KEY=AMADIOHA-M257-CYBER-OPS-SECURITY-KEY-2024
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=900
```

## 🔌 API Key Configuration

### Cybersecurity APIs

#### VirusTotal
```env
VIRUSTOTAL_API_KEY=your-virustotal-api-key-here
```
- **Get API Key:** https://www.virustotal.com/gui/join-us
- **Free Tier:** 1,000 requests/day
- **Used for:** File analysis, malware detection

#### Shodan
```env
SHODAN_API_KEY=your-shodan-api-key-here
```
- **Get API Key:** https://account.shodan.io/register
- **Free Tier:** 100 queries/month
- **Used for:** Network reconnaissance, device discovery

#### Censys
```env
CENSYS_API_ID=your-censys-api-id-here
CENSYS_API_SECRET=your-censys-api-secret-here
```
- **Get API Key:** https://search.censys.io/register
- **Free Tier:** 250 queries/month
- **Used for:** Internet-wide scanning, certificate analysis

### Phone Tracking APIs

#### NumVerify
```env
NUMVERIFY_API_KEY=your-numverify-api-key-here
```
- **Get API Key:** https://numverify.com/product
- **Free Tier:** 1,000 requests/month
- **Used for:** Phone number validation and carrier lookup

#### OpenCellID
```env
OPENCELLID_API_KEY=your-opencellid-api-key-here
```
- **Get API Key:** https://opencellid.org/
- **Free Tier:** Limited requests
- **Used for:** Cell tower location data

#### IPGeolocation
```env
IPGEOLOCATION_API_KEY=your-ipgeolocation-api-key-here
```
- **Get API Key:** https://ipgeolocation.io/signup
- **Free Tier:** 1,000 requests/month
- **Used for:** IP-based geolocation

#### TrueCaller (Optional)
```env
TRUECALLER_API_KEY=your-truecaller-api-key-here
```
- **Get API Key:** Contact TrueCaller for business API
- **Used for:** Enhanced phone number intelligence

## 📧 Email Configuration

### SMTP Settings
```env
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USE_TLS=True
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
```

### Gmail Setup
1. Enable 2-Factor Authentication
2. Generate App Password: https://myaccount.google.com/apppasswords
3. Use App Password in `SMTP_PASSWORD`

### Other Email Providers
- **Outlook:** `smtp-mail.outlook.com:587`
- **Yahoo:** `smtp.mail.yahoo.com:587`
- **Custom SMTP:** Configure your server details

## 🤖 Telegram Bot Configuration

### Setup Instructions
1. **Create Bot:**
   - Message @BotFather on Telegram
   - Use `/newbot` command
   - Get bot token

2. **Get Chat ID:**
   - Add bot to group/channel
   - Send message to bot
   - Visit: `https://api.telegram.org/bot<TOKEN>/getUpdates`
   - Find chat ID in response

3. **Configure:**
```env
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_CHAT_ID=your-telegram-chat-id-here
```

## 🌐 Ngrok Configuration

### Setup for External Access
```env
NGROK_AUTH_TOKEN=your-ngrok-auth-token-here
NGROK_REGION=us
NGROK_SUBDOMAIN=amadioha-m257
```

1. **Get Auth Token:** https://dashboard.ngrok.com/get-started/your-authtoken
2. **Install Ngrok:** https://ngrok.com/download
3. **Authenticate:** `ngrok authtoken <your-token>`

## 🗄️ Database Configuration

### SQLite (Development)
```env
DATABASE_URL=sqlite:///cybersecurity_platform.db
```
- **Pros:** No setup required, portable
- **Cons:** Single-user, limited performance

### PostgreSQL (Production)
```env
DATABASE_URL=postgresql://user:password@localhost:5432/amadioha_m257
```

**Setup PostgreSQL:**
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database
sudo -u postgres createdb amadioha_m257
sudo -u postgres createuser amadioha_user
sudo -u postgres psql -c "ALTER USER amadioha_user PASSWORD 'secure_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE amadioha_m257 TO amadioha_user;"
```

### MySQL (Alternative)
```env
DATABASE_URL=mysql://user:password@localhost:3306/amadioha_m257
```

## 🔒 Security Best Practices

### Production Security
```env
# Change these in production
SECRET_KEY=your-ultra-secure-production-secret-key-here
SECURITY_KEY=your-ultra-secure-production-security-key-here
FLASK_ENV=production
FLASK_DEBUG=False
DEBUG=False
```

### Key Generation
```python
import secrets
import string

# Generate secure keys
def generate_key(length=64):
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

print("SECRET_KEY:", generate_key())
print("SECURITY_KEY:", generate_key())
```

### SSL/HTTPS Configuration
```env
REQUIRE_HTTPS=True
SECURE_COOKIES=True
PHISHING_SSL_CERT_PATH=/path/to/ssl/cert.pem
PHISHING_SSL_KEY_PATH=/path/to/ssl/key.pem
```

## 📊 Monitoring & Logging

### Log Configuration
```env
LOG_LEVEL=INFO              # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_FILE=logs/amadioha_m257.log
LOG_MAX_SIZE=10485760       # 10MB
LOG_BACKUP_COUNT=5
```

### Audit Logging
```env
AUDIT_LOG_ENABLED=True
AUDIT_LOG_FILE=logs/audit.log
AUDIT_LOG_LEVEL=INFO
```

### Performance Monitoring
```env
PERFORMANCE_MONITORING=True
METRICS_ENDPOINT=/metrics
HEALTH_CHECK_ENDPOINT=/health
```

## 🚀 Performance Optimization

### Redis Caching (Optional)
```env
REDIS_URL=redis://localhost:6379/0
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300
```

**Install Redis:**
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# Start Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### Celery Task Queue (Optional)
```env
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
```

## 🔧 Feature Flags

### Enable/Disable Features
```env
ENABLE_PHONE_TRACKING=True
ENABLE_REAL_MALWARE=True
ENABLE_CREDENTIAL_HARVESTING=True
ENABLE_PDF_EXPLOITS=True
ENABLE_SOCIAL_ENGINEERING=True
ENABLE_NETWORK_PENETRATION=True
```

### Advanced Features
```env
FEATURE_ADVANCED_KEYLOGGER=True
FEATURE_BROWSER_CREDENTIAL_THEFT=True
FEATURE_REAL_TIME_MONITORING=True
FEATURE_LOCATION_TRACKING=True
```

## 🌍 Environment-Specific Configurations

### Development
```env
FLASK_ENV=development
DEBUG=True
MOCK_EXTERNAL_APIS=False
TEST_DATABASE_URL=sqlite:///test_cybersecurity_platform.db
```

### Testing
```env
TESTING=True
MOCK_EXTERNAL_APIS=True
TEST_PHONE_NUMBER=+1234567890
TEST_EMAIL=<EMAIL>
```

### Production
```env
FLASK_ENV=production
DEBUG=False
DEVELOPMENT_MODE=False
REQUIRE_HTTPS=True
SECURE_COOKIES=True
```

## 🔍 Troubleshooting

### Common Issues

#### Database Connection Errors
- Check `DATABASE_URL` format
- Verify database server is running
- Ensure user has proper permissions

#### API Key Errors
- Verify API keys are correct
- Check API rate limits
- Ensure APIs are accessible from your network

#### Email/SMTP Issues
- Verify SMTP credentials
- Check firewall/network restrictions
- Test with simple email client first

#### Phone Tracking Not Working
- Verify phone tracking API keys
- Check phone number format (+country_code_number)
- Ensure internet connectivity

### Debug Mode
```env
DEBUG=True
LOG_LEVEL=DEBUG
FLASK_DEBUG=True
```

## 📚 Additional Resources

- **Flask Configuration:** https://flask.palletsprojects.com/en/2.3.x/config/
- **SQLAlchemy:** https://docs.sqlalchemy.org/
- **Redis:** https://redis.io/documentation
- **Celery:** https://docs.celeryproject.org/
- **Ngrok:** https://ngrok.com/docs

## ⚠️ Security Warnings

1. **Never commit `.env` files to version control**
2. **Use strong, unique keys in production**
3. **Regularly rotate API keys and passwords**
4. **Enable HTTPS in production**
5. **Monitor logs for suspicious activity**
6. **Use this platform only for authorized testing**

## 📞 Support

For configuration assistance:
1. Review this documentation thoroughly
2. Check the troubleshooting section
3. Verify all required API keys are configured
4. Test with minimal configuration first
5. Ensure proper legal authorization for cybersecurity testing
