{% extends 'base.html' %}
{% block title %}Contact Methods | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-phone-alt text-primary"></i> TARGET CONTACT METHODS</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Target Phone Number Input -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-crosshairs"></i> Target Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="targetPhone" class="form-label">Target Phone Number</label>
                        <input type="text" class="form-control" id="targetPhone" placeholder="+2348031234567" required>
                        <div class="form-text">Nigerian format: +234803... or 0803...</div>
                    </div>
                    <button type="button" class="btn btn-primary w-100" id="validateTargetBtn">
                        <i class="fas fa-search"></i> Validate Target
                    </button>
                </div>
            </div>

            <!-- Target Information Display -->
            <div class="card mb-4" id="targetInfoCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Target Details</h5>
                </div>
                <div class="card-body" id="targetInfoContent">
                    <!-- Target info will be populated here -->
                </div>
            </div>
        </div>

        <!-- Contact Methods -->
        <div class="col-lg-8">
            <!-- SMS Campaign -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-sms"></i> SMS Campaign</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smsCategory" class="form-label">Template Category</label>
                                <select class="form-control" id="smsCategory">
                                    <option value="">Select Category</option>
                                    <option value="banking">🏦 Banking</option>
                                    <option value="telecom">📱 Telecom</option>
                                    <option value="government">🏛️ Government</option>
                                    <option value="ecommerce">🛒 E-commerce</option>
                                    <option value="social">📱 Social Media</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smsTemplate" class="form-label">Template</label>
                                <select class="form-control" id="smsTemplate">
                                    <option value="">Select Template</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="customLink" class="form-label">Custom Phishing Link (Optional)</label>
                        <input type="url" class="form-control" id="customLink" placeholder="https://secure-verify.banking-security.com/verify/abc123">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Message Preview</label>
                        <div class="alert alert-secondary" id="smsPreview">
                            Select a template to see message preview
                        </div>
                    </div>
                    <button type="button" class="btn btn-success w-100" id="sendSMSBtn" disabled>
                        <i class="fas fa-paper-plane"></i> Send SMS Campaign
                    </button>
                </div>
            </div>

            <!-- Voice Call Campaign -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-phone"></i> Voice Call Campaign</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="voiceScript" class="form-label">Voice Script</label>
                                <select class="form-control" id="voiceScript">
                                    <option value="">Select Script</option>
                                    <option value="security_alert">🔒 Security Alert</option>
                                    <option value="prize_notification">🎁 Prize Notification</option>
                                    <option value="service_suspension">⚠️ Service Suspension</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="voiceLanguage" class="form-label">Language</label>
                                <select class="form-control" id="voiceLanguage">
                                    <option value="english">🇬🇧 English</option>
                                    <option value="pidgin">🇳🇬 Nigerian Pidgin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Script Preview</label>
                        <div class="alert alert-secondary" id="voicePreview">
                            Select a script to see preview
                        </div>
                    </div>
                    <button type="button" class="btn btn-warning w-100" id="initiateCallBtn" disabled>
                        <i class="fas fa-phone-alt"></i> Initiate Voice Call
                    </button>
                </div>
            </div>

            <!-- WhatsApp Campaign -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fab fa-whatsapp"></i> WhatsApp Campaign</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> WhatsApp campaigns require additional setup with WhatsApp Business API
                    </div>
                    <div class="mb-3">
                        <label for="whatsappMessage" class="form-label">WhatsApp Message</label>
                        <textarea class="form-control" id="whatsappMessage" rows="3" placeholder="Hello! This is a security alert from your bank..."></textarea>
                    </div>
                    <button type="button" class="btn btn-success w-100" disabled>
                        <i class="fab fa-whatsapp"></i> Send WhatsApp Message (Coming Soon)
                    </button>
                </div>
            </div>

            <!-- Email Campaign -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> Email Campaign</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Email campaigns require target email address discovery
                    </div>
                    <div class="mb-3">
                        <label for="targetEmail" class="form-label">Target Email (if known)</label>
                        <input type="email" class="form-control" id="targetEmail" placeholder="<EMAIL>">
                    </div>
                    <div class="mb-3">
                        <label for="emailTemplate" class="form-label">Email Template</label>
                        <select class="form-control" id="emailTemplate">
                            <option value="">Select Template</option>
                            <option value="banking">Banking Security Alert</option>
                            <option value="social_media">Social Media Notification</option>
                            <option value="work">Work Document</option>
                            <option value="shipping">Package Delivery</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-primary w-100" disabled>
                        <i class="fas fa-envelope"></i> Send Email Campaign (Coming Soon)
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Campaign History</h5>
                    <button class="btn btn-sm btn-outline-primary" id="refreshHistoryBtn">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Target</th>
                                    <th>Template/Script</th>
                                    <th>Status</th>
                                    <th>Sent/Initiated</th>
                                    <th>Response</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="campaignHistoryBody">
                                <!-- Campaign history will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let targetPhoneNumber = '';
let smsTemplates = {};
let voiceScripts = {};

// Load templates and scripts on page load
document.addEventListener('DOMContentLoaded', function() {
    loadSMSTemplates();
    loadVoiceScripts();
    loadCampaignHistory();
});

// Validate target phone number
document.getElementById('validateTargetBtn').addEventListener('click', function() {
    const phoneNumber = document.getElementById('targetPhone').value;
    
    if (!phoneNumber) {
        alert('Please enter a phone number');
        return;
    }
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Validating...';
    this.disabled = true;
    
    fetch('/api/phone_info', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone_number: phoneNumber })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            targetPhoneNumber = phoneNumber;
            displayTargetInfo(data.data);
            document.getElementById('targetInfoCard').style.display = 'block';
            enableCampaignButtons();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to validate phone number');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-search"></i> Validate Target';
        this.disabled = false;
    });
});

function displayTargetInfo(data) {
    const content = document.getElementById('targetInfoContent');
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="mb-2"><strong>Number:</strong> ${data.number}</div>
                <div class="mb-2"><strong>Country:</strong> ${data.country || 'Unknown'}</div>
                <div class="mb-2"><strong>Carrier:</strong> ${data.carrier || 'Unknown'}</div>
            </div>
            <div class="col-md-6">
                <div class="mb-2"><strong>Valid:</strong> ${data.is_valid ? 'Yes' : 'No'}</div>
                <div class="mb-2"><strong>Type:</strong> ${data.number_type || 'Unknown'}</div>
                ${data.nigerian_carrier ? `<div class="mb-2"><strong>🇳🇬 Network:</strong> ${data.nigerian_carrier}</div>` : ''}
            </div>
        </div>
    `;
}

function enableCampaignButtons() {
    document.getElementById('sendSMSBtn').disabled = false;
    document.getElementById('initiateCallBtn').disabled = false;
}

// Load SMS templates
function loadSMSTemplates() {
    fetch('/api/sms_templates')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            smsTemplates = data.templates;
        }
    })
    .catch(error => console.error('Error loading SMS templates:', error));
}

// Load voice scripts
function loadVoiceScripts() {
    fetch('/api/voice_scripts')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            voiceScripts = data.scripts;
        }
    })
    .catch(error => console.error('Error loading voice scripts:', error));
}

// SMS category change handler
document.getElementById('smsCategory').addEventListener('change', function() {
    const category = this.value;
    const templateSelect = document.getElementById('smsTemplate');
    
    templateSelect.innerHTML = '<option value="">Select Template</option>';
    
    if (category && smsTemplates[category]) {
        Object.keys(smsTemplates[category]).forEach(template => {
            const option = document.createElement('option');
            option.value = template;
            option.textContent = template.replace('_', ' ').toUpperCase();
            templateSelect.appendChild(option);
        });
    }
    
    updateSMSPreview();
});

// SMS template change handler
document.getElementById('smsTemplate').addEventListener('change', updateSMSPreview);

function updateSMSPreview() {
    const category = document.getElementById('smsCategory').value;
    const template = document.getElementById('smsTemplate').value;
    const preview = document.getElementById('smsPreview');
    
    if (category && template && smsTemplates[category] && smsTemplates[category][template]) {
        let message = smsTemplates[category][template];
        message = message.replace('{link}', 'https://secure-verify.example.com/verify/abc123');
        message = message.replace('{code}', '123456');
        message = message.replace('{random}', '12345');
        message = message.replace('{amount}', '₦25,000');
        
        preview.innerHTML = message;
        preview.className = 'alert alert-warning';
    } else {
        preview.innerHTML = 'Select a template to see message preview';
        preview.className = 'alert alert-secondary';
    }
}

// Voice script change handler
document.getElementById('voiceScript').addEventListener('change', updateVoicePreview);
document.getElementById('voiceLanguage').addEventListener('change', updateVoicePreview);

function updateVoicePreview() {
    const script = document.getElementById('voiceScript').value;
    const language = document.getElementById('voiceLanguage').value;
    const preview = document.getElementById('voicePreview');
    
    if (script && voiceScripts[script] && voiceScripts[script][language]) {
        preview.innerHTML = voiceScripts[script][language];
        preview.className = 'alert alert-warning';
    } else {
        preview.innerHTML = 'Select a script to see preview';
        preview.className = 'alert alert-secondary';
    }
}

// Send SMS campaign
document.getElementById('sendSMSBtn').addEventListener('click', function() {
    if (!targetPhoneNumber) {
        alert('Please validate target phone number first');
        return;
    }
    
    const category = document.getElementById('smsCategory').value;
    const template = document.getElementById('smsTemplate').value;
    const customLink = document.getElementById('customLink').value;
    
    if (!category || !template) {
        alert('Please select SMS category and template');
        return;
    }
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    this.disabled = true;
    
    fetch('/api/sms_campaign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: targetPhoneNumber,
            template_category: category,
            template_name: template,
            custom_link: customLink || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('SMS campaign sent successfully!');
            loadCampaignHistory();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to send SMS campaign');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-paper-plane"></i> Send SMS Campaign';
        this.disabled = false;
    });
});

// Initiate voice call
document.getElementById('initiateCallBtn').addEventListener('click', function() {
    if (!targetPhoneNumber) {
        alert('Please validate target phone number first');
        return;
    }
    
    const script = document.getElementById('voiceScript').value;
    const language = document.getElementById('voiceLanguage').value;
    
    if (!script) {
        alert('Please select voice script');
        return;
    }
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calling...';
    this.disabled = true;
    
    fetch('/api/voice_campaign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: targetPhoneNumber,
            script_type: script,
            language: language
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Voice call initiated successfully!');
            loadCampaignHistory();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to initiate voice call');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-phone-alt"></i> Initiate Voice Call';
        this.disabled = false;
    });
});

// Load campaign history
function loadCampaignHistory() {
    fetch('/api/campaign_history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCampaignHistory(data);
        }
    })
    .catch(error => console.error('Error loading campaign history:', error));
}

function displayCampaignHistory(data) {
    const tbody = document.getElementById('campaignHistoryBody');
    tbody.innerHTML = '';
    
    // Combine and sort campaigns
    const allCampaigns = [...data.sms_campaigns, ...data.voice_campaigns];
    allCampaigns.sort((a, b) => new Date(b.sent_at || b.initiated_at) - new Date(a.sent_at || a.initiated_at));
    
    allCampaigns.slice(0, 20).forEach(campaign => {
        const row = tbody.insertRow();
        
        const type = campaign.type === 'sms' ? '📱 SMS' : '📞 Voice';
        const template = campaign.type === 'sms' ? 
            `${campaign.template_category}/${campaign.template_name}` : 
            `${campaign.script_type} (${campaign.language})`;
        const timestamp = campaign.type === 'sms' ? 
            new Date(campaign.sent_at).toLocaleString() : 
            new Date(campaign.initiated_at).toLocaleString();
        const response = campaign.type === 'sms' ? 
            (campaign.clicked_at ? '✅ Clicked' : '⏳ Pending') :
            (campaign.user_input ? `Input: ${campaign.user_input}` : '⏳ Pending');
        
        row.innerHTML = `
            <td>${type}</td>
            <td class="font-monospace">${campaign.phone_number}</td>
            <td>${template}</td>
            <td><span class="badge bg-${getStatusColor(campaign.status)}">${campaign.status}</span></td>
            <td>${timestamp}</td>
            <td>${response}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewCampaignDetails(${campaign.id}, '${campaign.type}')">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
    });
}

function getStatusColor(status) {
    const colors = {
        'sent': 'success',
        'delivered': 'success',
        'clicked': 'warning',
        'answered': 'success',
        'completed': 'success',
        'failed': 'danger',
        'pending': 'secondary'
    };
    return colors[status] || 'secondary';
}

function viewCampaignDetails(id, type) {
    // Implement campaign details view
    alert(`View details for ${type} campaign #${id}`);
}

// Refresh history button
document.getElementById('refreshHistoryBtn').addEventListener('click', loadCampaignHistory);
</script>

{% endblock %}
