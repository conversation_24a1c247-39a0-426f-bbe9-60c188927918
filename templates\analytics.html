{% extends "base.html" %}

{% block title %}Analytics - Phishing Awareness Platform{% endblock %}

{% block content %}
<style>
    .stats-card {
        background: linear-gradient(135deg, rgba(124,58,237,0.18) 0%, rgba(36,40,54,0.85) 100%);
        color: var(--text-primary);
        border-radius: var(--border-radius);
        border: var(--border-width) solid var(--border-color);
        box-shadow: 0 8px 32px rgba(124,58,237,0.10), var(--shadow);
        padding: 1.5rem;
        margin-bottom: 20px;
        transition: box-shadow 0.3s, transform 0.3s;
        position: relative;
    }
    .stats-card:hover {
        box-shadow: 0 16px 48px rgba(124,58,237,0.18), var(--shadow);
        transform: scale(1.025);
    }
    .card {
        background: var(--card-bg);
        border: var(--border-width) solid var(--border-color);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        transition: box-shadow 0.2s, transform 0.2s;
    }
    .card:hover {
        box-shadow: 0 12px 36px rgba(124,58,237,0.18), var(--shadow);
        transform: scale(1.015);
    }
    .table {
        color: var(--text-primary);
    }
    .table th {
        background: rgba(124,58,237,0.08);
        color: var(--primary-color);
        border-color: var(--border-color);
    }
    .table td {
        border-color: var(--border-color);
    }
    .badge.bg-success {
        background: var(--success-color) !important;
        color: #fff;
    }
    .badge.bg-warning {
        background: var(--warning-color) !important;
        color: #fff;
    }
    .badge.bg-danger {
        background: var(--danger-color) !important;
        color: #fff;
    }
</style>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-chart-bar"></i> Analytics Dashboard</h2>
        </div>
    </div>
    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ analytics_data.values() | sum(attribute='total_actions') }}</div>
                <div class="stats-label">Total Actions</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ analytics_data.values() | sum(attribute='clicks') }}</div>
                <div class="stats-label">Total Clicks</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ analytics_data.values() | sum(attribute='form_fills') }}</div>
                <div class="stats-label">Form Submissions</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ analytics_data.values() | sum(attribute='downloads') }}</div>
                <div class="stats-label">Downloads</div>
            </div>
        </div>
    </div>
    <div class="row">
        <!-- Campaign Performance Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Campaign Performance</h5>
                </div>
                <div class="card-body">
                    <canvas id="campaignChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <!-- Action Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Action Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="actionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
    <!-- Campaign Details Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Campaign Analytics</h5>
                </div>
                <div class="card-body">
                    {% if analytics_data %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Campaign</th>
                                        <th>Total Actions</th>
                                        <th>Clicks</th>
                                        <th>Form Fills</th>
                                        <th>Downloads</th>
                                        <th>Success Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for campaign_name, data in analytics_data.items() %}
                                    <tr>
                                        <td><strong>{{ campaign_name }}</strong></td>
                                        <td>{{ data.total_actions }}</td>
                                        <td>{{ data.clicks }}</td>
                                        <td>{{ data.form_fills }}</td>
                                        <td>{{ data.downloads }}</td>
                                        <td>
                                            {% set success_rate = ((data.clicks + data.form_fills + data.downloads) / data.total_actions * 100) if data.total_actions > 0 else 0 %}
                                            <span class="badge bg-{{ 'success' if success_rate > 50 else 'warning' if success_rate > 25 else 'danger' }}">
                                                {{ "%.1f"|format(success_rate) }}%
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-secondary mb-3"></i>
                            <p class="text-secondary">No analytics data available</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Campaign Performance Chart
const campaignCtx = document.getElementById('campaignChart').getContext('2d');
const campaignChart = new Chart(campaignCtx, {
    type: 'bar',
    data: {
        labels: [{% for campaign_name in analytics_data.keys() %}'{{ campaign_name }}'{% if not loop.last %}, {% endif %}{% endfor %}],
        datasets: [{
            label: 'Clicks',
            data: [{% for data in analytics_data.values() %}{{ data.clicks }}{% if not loop.last %}, {% endif %}{% endfor %}],
            backgroundColor: 'var(--primary-color)',
            borderColor: 'var(--primary-color)',
            borderWidth: 1
        }, {
            label: 'Form Fills',
            data: [{% for data in analytics_data.values() %}{{ data.form_fills }}{% if not loop.last %}, {% endif %}{% endfor %}],
            backgroundColor: 'var(--warning-color)',
            borderColor: 'var(--warning-color)',
            borderWidth: 1
        }, {
            label: 'Downloads',
            data: [{% for data in analytics_data.values() %}{{ data.downloads }}{% if not loop.last %}, {% endif %}{% endfor %}],
            backgroundColor: 'var(--accent-color)',
            borderColor: 'var(--accent-color)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: 'var(--text-primary)'
                }
            },
            x: {
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: 'var(--text-primary)'
                }
            }
        },
        plugins: {
            legend: {
                labels: {
                    color: 'var(--text-primary)'
                }
            }
        }
    }
});
// Action Distribution Chart
const actionCtx = document.getElementById('actionChart').getContext('2d');
const actionChart = new Chart(actionCtx, {
    type: 'doughnut',
    data: {
        labels: ['Clicks', 'Form Fills', 'Downloads'],
        datasets: [{
            data: [
                {{ analytics_data.values() | sum(attribute='clicks') }},
                {{ analytics_data.values() | sum(attribute='form_fills') }},
                {{ analytics_data.values() | sum(attribute='downloads') }}
            ],
            backgroundColor: [
                'var(--primary-color)',
                'var(--warning-color)',
                'var(--accent-color)'
            ],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: 'var(--text-primary)'
                }
            }
        }
    }
});
</script>
{% endblock %} 