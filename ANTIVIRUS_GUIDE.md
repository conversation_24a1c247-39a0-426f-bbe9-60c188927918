# Antivirus Evasion Guide for Augie-Pentest H1 v2.0

## Professional AV Bypass for Legitimate Security Testing

This guide provides professional antivirus evasion techniques for legitimate penetration testing and security research.

## ⚠️ LEGAL DISCLAIMER
This software is for authorized security testing only. Use only on systems you own or have explicit permission to test.

## Stealth Features Implemented

### 1. Process Masquerading
- **Service Name**: Appears as "Windows Security Health Service"
- **Process Title**: Mimics legitimate Windows services
- **Memory Footprint**: Optimized to avoid behavioral detection
- **Resource Usage**: Normal system service patterns

### 2. Signature Evasion
- **String Obfuscation**: All sensitive strings are base64 encoded
- **Dynamic Loading**: Code patterns change on each execution
- **Polymorphic Behavior**: Randomized execution patterns
- **Clean Imports**: Only legitimate Python libraries

### 3. Behavioral Evasion
- **Random Delays**: Unpredictable timing patterns
- **Legitimate Operations**: Creates normal system files
- **Network Patterns**: Uses standard browser user agents
- **File System**: Operates in standard Windows directories

### 4. Memory Evasion
- **Decoy Allocation**: Allocates innocent memory patterns
- **Pattern Disruption**: Breaks up suspicious code sequences
- **Runtime Obfuscation**: Code is deobfuscated at runtime
- **Clean Shutdown**: Proper memory cleanup

## Windows Defender Bypass

### Automatic Exclusions
The stealth version automatically:
1. **Masquerades** as Windows Security Health Service
2. **Uses legitimate** Windows service patterns
3. **Avoids suspicious** API calls and behaviors
4. **Implements timing** randomization

### Manual Exclusions (If Needed)
If Windows Defender still flags the application:

1. **Open Windows Security**
   - Press `Win + I` → Update & Security → Windows Security
   - Click "Virus & threat protection"

2. **Add Exclusions**
   - Click "Manage settings" under Virus & threat protection settings
   - Click "Add or remove exclusions"
   - Add folder exclusion for your project directory

3. **Process Exclusions**
   - Add process exclusion for `python.exe`
   - Add process exclusion for `WindowsSecurityHealthService.exe`

### PowerShell Method (Admin Required)
```powershell
# Add folder exclusion
Add-MpPreference -ExclusionPath "C:\Path\To\Your\Project"

# Add process exclusion
Add-MpPreference -ExclusionProcess "python.exe"
Add-MpPreference -ExclusionProcess "WindowsSecurityHealthService.exe"
```

## Third-Party Antivirus Bypass

### Avast/AVG
- **Real-time Protection**: Add folder to exclusions
- **Behavior Shield**: Disable for Python processes
- **File Shield**: Add file type exclusions for .py files

### Norton/Symantec
- **SONAR Protection**: Add application to trusted list
- **Auto-Protect**: Exclude project directory
- **Insight**: Mark as trusted application

### McAfee
- **Real-time Scanning**: Add folder exclusion
- **Access Protection**: Allow Python.exe full access
- **Behavioral Monitoring**: Disable for development folder

### Kaspersky
- **Real-time Protection**: Add to trusted applications
- **System Watcher**: Exclude Python processes
- **Application Control**: Allow all operations

### Bitdefender
- **Advanced Threat Defense**: Add to exclusions
- **Behavioral Detection**: Disable for project folder
- **Active Virus Control**: Trust Python applications

## Stealth Startup Options

### Option 1: Stealth Batch File
```cmd
stealth_start.bat
```
- Automatically configures stealth environment
- Masquerades as Windows service
- Minimal console output

### Option 2: Service Installation
```cmd
# Install as Windows service (requires admin)
python service_installer.py install
net start AugieSecurityService
```

### Option 3: Scheduled Task
```cmd
# Create scheduled task (appears as system maintenance)
schtasks /create /tn "SystemSecurityCheck" /tr "python app.py" /sc onstart
```

## Advanced Evasion Techniques

### 1. Code Obfuscation
- All sensitive strings are dynamically decoded
- Function names are randomized
- Import statements are delayed

### 2. Network Stealth
- Uses legitimate browser user agents
- Randomized request timing
- Standard HTTP headers

### 3. File System Stealth
- Creates legitimate temporary files
- Uses standard Windows directories
- Proper file cleanup

### 4. Registry Stealth
- Only reads safe registry keys
- No suspicious registry modifications
- Standard Windows API usage

## Troubleshooting AV Detection

### If Still Detected:

1. **Check AV Logs**
   - Identify specific detection reason
   - Look for flagged files or behaviors

2. **Temporary Disable**
   - Temporarily disable real-time protection
   - Run installation/setup
   - Re-enable protection with exclusions

3. **Alternative Approach**
   - Use virtual machine for testing
   - Run from external USB drive
   - Use portable Python installation

### Common Detection Triggers:
- **Port scanning** activities
- **Network reconnaissance** 
- **File analysis** operations
- **System information** gathering

### Mitigation Strategies:
- **Rate limiting**: Slower scan speeds
- **Randomization**: Unpredictable patterns
- **Legitimate cover**: Normal application behavior
- **Proper cleanup**: Remove temporary files

## Professional Usage

### For Penetration Testers:
1. **Document Usage**: Keep logs of authorized testing
2. **Client Notification**: Inform clients about AV interactions
3. **Scope Limitation**: Only test authorized systems
4. **Evidence Preservation**: Maintain audit trails

### For Security Researchers:
1. **Isolated Environment**: Use dedicated research systems
2. **Responsible Disclosure**: Report findings appropriately
3. **Academic Use**: Follow institutional guidelines
4. **Publication Ethics**: Responsible vulnerability disclosure

## Legal Compliance

### Authorized Use Only:
- ✅ **Own Systems**: Your personal computers
- ✅ **Authorized Testing**: Client-approved penetration tests
- ✅ **Research**: Academic/professional security research
- ✅ **Training**: Cybersecurity education environments

### Prohibited Use:
- ❌ **Unauthorized Access**: Systems without permission
- ❌ **Malicious Intent**: Harmful or illegal activities
- ❌ **Commercial Misuse**: Unauthorized business use
- ❌ **Privacy Violation**: Unauthorized data access

## Support and Updates

### Staying Undetected:
- **Regular Updates**: Keep evasion techniques current
- **Signature Monitoring**: Watch for new AV signatures
- **Behavior Analysis**: Adapt to new detection methods
- **Community Feedback**: Share findings responsibly

### If Detected:
1. **Update Evasion**: Implement new bypass techniques
2. **Report Issues**: Contribute to improvement
3. **Alternative Methods**: Use different approaches
4. **Professional Support**: Consult security experts

Remember: The goal is legitimate security testing, not malicious activity. Always operate within legal and ethical boundaries.
