{% extends "base.html" %}

{% block title %}Results - Phishing Awareness Platform{% endblock %}

{% block content %}
<style>
.card {
    background: var(--card-bg);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
    box-shadow: 0 12px 36px rgba(124,58,237,0.18), var(--shadow);
    transform: scale(1.015);
}
.table {
    color: var(--text-primary);
}
.table th {
    background: rgba(124,58,237,0.08);
    color: var(--primary-color);
    border-color: var(--border-color);
}
.table td {
    border-color: var(--border-color);
}
.btn-cyber {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border: var(--border-width) solid var(--border-color);
    color: #fff;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: background 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.btn-cyber:hover {
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    box-shadow: 0 4px 16px rgba(124,58,237,0.18);
}
</style>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-chart-bar"></i> Campaign Results</h2>
            <div>
                <button class="btn btn-secondary me-2">
                    <i class="fas fa-download"></i> Export
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-filter"></i> Filter
                </button>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ results|length }}</div>
                <div class="stats-label">Total Results</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ results|selectattr('action_type', 'equalto', 'click')|list|length }}</div>
                <div class="stats-label">Clicks</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ results|selectattr('action_type', 'equalto', 'form_fill')|list|length }}</div>
                <div class="stats-label">Form Submissions</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ results|selectattr('action_type', 'equalto', 'download')|list|length }}</div>
                <div class="stats-label">Downloads</div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> All Results</h5>
                </div>
                <div class="card-body">
                    {% if results %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Campaign</th>
                                        <th>Target Email</th>
                                        <th>IP Address</th>
                                        <th>Timestamp</th>
                                        <th>Credentials</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for result in results %}
                                    <tr>
                                        <td>
                                            {% if result.action_type == 'click' %}
                                                <span class="badge bg-primary"><i class="fas fa-mouse-pointer"></i> Click</span>
                                            {% elif result.action_type == 'form_fill' %}
                                                <span class="badge bg-warning"><i class="fas fa-edit"></i> Form Fill</span>
                                            {% elif result.action_type == 'download' %}
                                                <span class="badge bg-info"><i class="fas fa-download"></i> Download</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <strong>{{ result.campaign.name if result.campaign else 'Unknown' }}</strong>
                                        </td>
                                        <td>{{ result.target_email or 'N/A' }}</td>
                                        <td>{{ result.ip_address or 'N/A' }}</td>
                                        <td>{{ result.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td>
                                            {% if result.credentials %}
                                                <button class="btn btn-sm btn-outline-info" onclick="viewCredentials('{{ result.id }}')">
                                                    <i class="fas fa-eye"></i> View
                                                </button>
                                            {% else %}
                                                <span class="text-secondary">None</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" title="Export">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-chart-bar fa-3x text-secondary mb-3"></i>
                            <h5 class="text-secondary">No results yet</h5>
                            <p class="text-secondary">Results will appear here when campaigns are executed</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Credentials Modal -->
    <div class="modal fade" id="credentialsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Captured Credentials</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="credentialsContent">
                        <!-- Credentials will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Export</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewCredentials(resultId) {
    // Simulate loading credentials
    const modal = new bootstrap.Modal(document.getElementById('credentialsModal'));
    const content = document.getElementById('credentialsContent');
    
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
    modal.show();
    
    // Simulate API call
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-warning">
                <strong>Warning:</strong> This data was captured for educational purposes only.
            </div>
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Username</strong></td>
                        <td><EMAIL></td>
                    </tr>
                    <tr>
                        <td><strong>Password</strong></td>
                        <td>••••••••</td>
                    </tr>
                    <tr>
                        <td><strong>User Agent</strong></td>
                        <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36</td>
                    </tr>
                    <tr>
                        <td><strong>IP Address</strong></td>
                        <td>*************</td>
                    </tr>
                </tbody>
            </table>
        `;
    }, 1000);
}
</script>
{% endblock %} 