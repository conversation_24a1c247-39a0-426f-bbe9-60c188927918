"""
Windows-Compatible Security Tools for Augie-Pentest H1 v2.0
Real Windows OS implementations that actually work
"""

import socket
import subprocess
import os
import sys
import threading
import time
from concurrent.futures import ThreadPoolExecutor
import requests
import json
from datetime import datetime

class WindowsPortScanner:
    """Real Windows port scanner using native Windows tools"""
    
    def __init__(self):
        self.timeout = 3
        self.max_threads = 50
    
    def scan_port(self, target, port):
        """Scan a single port on Windows"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((target, port))
            sock.close()
            
            if result == 0:
                # Get service name
                service = self.get_service_name(port)
                return {
                    'port': port,
                    'state': 'open',
                    'service': service
                }
        except:
            pass
        return None
    
    def get_service_name(self, port):
        """Get Windows service name for port"""
        windows_services = {
            21: 'FTP', 22: 'SSH', 23: 'Telnet', 25: 'SMTP',
            53: 'DNS', 80: 'HTTP', 110: 'POP3', 135: 'RPC',
            139: 'NetBIOS', 143: 'IMAP', 443: 'HTTPS', 445: 'SMB',
            993: 'IMAPS', 995: 'POP3S', 1433: 'MSSQL', 3389: 'RDP',
            5985: 'WinRM', 5986: 'WinRM-HTTPS'
        }
        return windows_services.get(port, 'Unknown')
    
    def scan_range(self, target, start_port, end_port):
        """Scan port range using Windows threading"""
        open_ports = []
        
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            futures = []
            for port in range(start_port, min(end_port + 1, start_port + 100)):
                future = executor.submit(self.scan_port, target, port)
                futures.append(future)
            
            for future in futures:
                result = future.result()
                if result:
                    open_ports.append(result)
        
        return open_ports

class WindowsNetworkRecon:
    """Windows network reconnaissance using native tools"""
    
    def ping_host(self, ip):
        """Ping host using Windows ping command"""
        try:
            result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def get_arp_table(self):
        """Get ARP table using Windows arp command"""
        try:
            result = subprocess.run(['arp', '-a'], 
                                  capture_output=True, text=True, timeout=10)
            hosts = []
            for line in result.stdout.split('\n'):
                if 'dynamic' in line.lower():
                    parts = line.split()
                    if len(parts) >= 2:
                        ip = parts[0]
                        mac = parts[1]
                        hosts.append({'ip': ip, 'mac': mac, 'type': 'dynamic'})
            return hosts
        except:
            return []
    
    def scan_network(self, network_range):
        """Scan network range on Windows"""
        import ipaddress
        
        try:
            network = ipaddress.IPv4Network(network_range, strict=False)
            active_hosts = []
            
            # Limit to first 50 IPs for Windows performance
            ips_to_scan = list(network.hosts())[:50]
            
            with ThreadPoolExecutor(max_workers=20) as executor:
                futures = {executor.submit(self.ping_host, str(ip)): str(ip) 
                          for ip in ips_to_scan}
                
                for future in futures:
                    ip = futures[future]
                    if future.result():
                        active_hosts.append(ip)
            
            return {
                'network_range': network_range,
                'total_scanned': len(ips_to_scan),
                'active_hosts': active_hosts,
                'inactive_hosts': len(ips_to_scan) - len(active_hosts)
            }
        except Exception as e:
            return {'error': str(e)}

class WindowsWebScanner:
    """Windows web security scanner"""
    
    def scan_website(self, url):
        """Scan website for security issues"""
        try:
            # Disable SSL warnings for testing
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            response = requests.get(url, timeout=10, verify=False)
            
            vulnerabilities = []
            security_headers = {}
            
            # Check security headers
            headers_to_check = [
                'X-Frame-Options', 'X-XSS-Protection', 'X-Content-Type-Options',
                'Strict-Transport-Security', 'Content-Security-Policy'
            ]
            
            for header in headers_to_check:
                value = response.headers.get(header, 'Missing')
                security_headers[header] = value
                if value == 'Missing':
                    vulnerabilities.append(f'Missing security header: {header}')
            
            # Check for information disclosure
            if 'Server' in response.headers:
                vulnerabilities.append(f'Server header disclosure: {response.headers["Server"]}')
            
            # Basic content analysis
            content = response.text.lower()
            if 'sql' in content and 'error' in content:
                vulnerabilities.append('Potential SQL injection indicators')
            
            if '<script>' in content and 'alert(' in content:
                vulnerabilities.append('Potential XSS vulnerability')
            
            risk_level = 'High' if len(vulnerabilities) > 3 else 'Medium' if len(vulnerabilities) > 1 else 'Low'
            
            return {
                'url': url,
                'status_code': response.status_code,
                'vulnerabilities': vulnerabilities,
                'security_headers': security_headers,
                'risk_level': risk_level,
                'scan_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'error': str(e)}

class WindowsFileAnalyzer:
    """Windows file analysis and malware detection"""
    
    def analyze_file(self, file_path):
        """Analyze file for threats on Windows"""
        try:
            if not os.path.exists(file_path):
                return {'error': 'File not found'}
            
            file_size = os.path.getsize(file_path)
            file_name = os.path.basename(file_path)
            
            # Read file content
            with open(file_path, 'rb') as f:
                content = f.read(1024)  # Read first 1KB
            
            analysis = {
                'file_name': file_name,
                'file_size': file_size,
                'file_type': self.detect_file_type(content),
                'threat_level': 'Low',
                'signatures': [],
                'recommendations': []
            }
            
            # Check for suspicious patterns
            suspicious_strings = [b'eval', b'exec', b'cmd.exe', b'powershell', b'shell']
            for pattern in suspicious_strings:
                if pattern in content:
                    analysis['signatures'].append(f'Suspicious pattern: {pattern.decode("utf-8", errors="ignore")}')
                    analysis['threat_level'] = 'High'
            
            # Windows-specific checks
            if content.startswith(b'MZ'):
                analysis['file_type'] = 'Windows Executable'
                analysis['threat_level'] = 'Medium'
                
                # Check for packed executables
                if b'UPX' in content or b'packed' in content.lower():
                    analysis['signatures'].append('Potentially packed executable')
                    analysis['threat_level'] = 'High'
            
            # Generate recommendations
            if analysis['threat_level'] == 'High':
                analysis['recommendations'] = [
                    'Quarantine file immediately',
                    'Run full antivirus scan',
                    'Check system for compromise'
                ]
            else:
                analysis['recommendations'] = [
                    'File appears safe',
                    'Continue monitoring'
                ]
            
            return analysis
            
        except Exception as e:
            return {'error': str(e)}
    
    def detect_file_type(self, content):
        """Detect file type from content"""
        if content.startswith(b'MZ'):
            return 'Windows Executable'
        elif content.startswith(b'PK'):
            return 'ZIP Archive'
        elif content.startswith(b'%PDF'):
            return 'PDF Document'
        elif content.startswith(b'\x89PNG'):
            return 'PNG Image'
        elif content.startswith(b'\xff\xd8\xff'):
            return 'JPEG Image'
        else:
            return 'Unknown'

class WindowsSystemInfo:
    """Get Windows system information"""
    
    def get_system_info(self):
        """Get comprehensive Windows system information"""
        try:
            info = {
                'os': sys.platform,
                'python_version': sys.version,
                'architecture': os.environ.get('PROCESSOR_ARCHITECTURE', 'Unknown'),
                'computer_name': os.environ.get('COMPUTERNAME', 'Unknown'),
                'username': os.environ.get('USERNAME', 'Unknown'),
                'domain': os.environ.get('USERDOMAIN', 'Unknown')
            }
            
            # Get Windows version
            try:
                result = subprocess.run(['ver'], shell=True, capture_output=True, text=True)
                info['windows_version'] = result.stdout.strip()
            except:
                info['windows_version'] = 'Unknown'
            
            # Get network interfaces
            try:
                result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True)
                info['network_config'] = result.stdout
            except:
                info['network_config'] = 'Unable to retrieve'
            
            return info
            
        except Exception as e:
            return {'error': str(e)}
