#!/usr/bin/env python3
"""
AMADIOHA-M257 Direct Server Startup
Bypasses any batch file issues
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def start_server():
    """Start the AMADIOHA-M257 server directly"""
    print("🚀 AMADIOHA-M257 CYBER OPS PLATFORM")
    print("=" * 50)
    print("🎯 Unified Operations Centers")
    print("📱 Phone Operations | 🦠 Malware Operations | 🎣 Phishing Operations")
    print("=" * 50)
    
    try:
        # Import Flask app
        from app import app, db
        
        print("✅ Flask app imported successfully")
        
        # Initialize database
        with app.app_context():
            try:
                db.create_all()
                print("✅ Database initialized")
            except Exception as e:
                print(f"⚠️ Database warning: {e}")
        
        # Server configuration
        print("\n🌐 Server Configuration:")
        print("   Host: 0.0.0.0 (all interfaces)")
        print("   Port: 5000")
        print("   Debug: Enabled")
        print("   Environment: Development")
        
        print("\n📱 Access URLs:")
        print("   🏠 Dashboard: http://localhost:5000")
        print("   📱 Phone Ops: http://localhost:5000/phone_operations")
        print("   🦠 Malware Ops: http://localhost:5000/malware_operations")
        print("   🎣 Phishing Ops: http://localhost:5000/phishing_operations")
        
        print("\n⚠️  AUTHORIZED TESTING ONLY!")
        print("🔒 This platform is for cybersecurity professionals.")
        print("=" * 50)
        print("🚀 Starting server...")
        print()
        
        # Start the Flask development server
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # Disable reloader to prevent issues
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Install dependencies:")
        print("   pip install -r requirements_minimal.txt")
        return False
    except Exception as e:
        print(f"❌ Server startup error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    start_server()
