#!/usr/bin/env python3
"""
Test Precise Location API Call for +2349063978612
AMADIOHA-M257 Enhanced Geolocation Test
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TARGET_NUMBER = "+2349063978612"
USERNAME = "admin"
PASSWORD = "admin123"

def login_and_get_session():
    """Login to get authenticated session"""
    session = requests.Session()
    
    try:
        # Get login page
        response = session.get(f"{BASE_URL}/login")
        if response.status_code != 200:
            print(f"❌ Failed to access login page: {response.status_code}")
            return None
        
        # Login
        login_data = {
            'username': USERNAME,
            'password': PASSWORD
        }
        
        response = session.post(f"{BASE_URL}/login", data=login_data)
        if response.status_code == 200:
            print("✅ Login successful")
            return session
        else:
            print(f"❌ Login failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def get_phone_info(session, phone_number):
    """Get detailed phone information"""
    print(f"\n📱 Getting phone info for: {phone_number}")
    print("-" * 50)
    
    try:
        data = {'phone_number': phone_number}
        response = session.post(f"{BASE_URL}/api/phone_info", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                phone_data = result['data']
                
                print("📊 PHONE INFORMATION:")
                print(f"   Number: {phone_data.get('number', 'Unknown')}")
                print(f"   Country: {phone_data.get('country', 'Unknown')}")
                print(f"   Carrier: {phone_data.get('carrier', 'Unknown')}")
                print(f"   Valid: {phone_data.get('is_valid', False)}")
                
                # Nigerian-specific data
                if 'nigerian_carrier' in phone_data:
                    print(f"   🇳🇬 Nigerian Carrier: {phone_data.get('nigerian_carrier')}")
                    print(f"   📶 Network Prefix: {phone_data.get('network_prefix')}")
                    print(f"   📱 Network Type: {phone_data.get('network_type')}")
                
                # NumVerify data
                if 'carrier_info' in phone_data:
                    print(f"   🔍 NumVerify Carrier: {phone_data.get('carrier_info')}")
                    print(f"   📍 NumVerify Location: {phone_data.get('location')}")
                    print(f"   📞 Line Type: {phone_data.get('line_type')}")
                
                return phone_data
            else:
                print(f"❌ Phone info failed: {result.get('error')}")
                return None
        else:
            print(f"❌ API request failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Phone info error: {e}")
        return None

def start_precise_tracking(session, phone_number):
    """Start precise location tracking"""
    print(f"\n🛰️ Starting precise tracking for: {phone_number}")
    print("-" * 50)
    
    try:
        data = {
            'phone_number': phone_number,
            'duration': 5  # 5 minutes for testing
        }
        
        response = session.post(f"{BASE_URL}/api/start_tracking", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                tracking_id = result.get('tracking_id')
                print(f"✅ Tracking started successfully")
                print(f"🆔 Tracking ID: {tracking_id}")
                return tracking_id
            else:
                print(f"❌ Failed to start tracking: {result.get('error')}")
                return None
        else:
            print(f"❌ Tracking request failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Tracking start error: {e}")
        return None

def get_precise_location_updates(session, tracking_id, num_updates=5):
    """Get precise location updates"""
    print(f"\n📍 Getting precise location updates...")
    print("-" * 50)
    
    locations = []
    
    for i in range(num_updates):
        try:
            print(f"\n📡 Update #{i+1}:")
            
            # Get tracking status
            response = session.get(f"{BASE_URL}/api/tracking_status/{tracking_id}")
            
            if response.status_code == 200:
                status_data = response.json()
                if status_data.get('success'):
                    current_loc = status_data.get('current_location')
                    
                    if current_loc:
                        locations.append(current_loc)
                        
                        # Display precise location data
                        print(f"   📍 Coordinates: {current_loc.get('latitude', 0):.7f}, {current_loc.get('longitude', 0):.7f}")
                        print(f"   🎯 Accuracy: ±{current_loc.get('accuracy', 0):.1f} meters")
                        print(f"   📡 Source: {current_loc.get('source', 'Unknown')}")
                        
                        # Enhanced location details
                        if 'street_address' in current_loc:
                            print(f"   🏠 Street Address: {current_loc.get('street_address')}")
                        
                        if 'city' in current_loc:
                            print(f"   🏙️ City: {current_loc.get('city')}")
                        
                        if 'state' in current_loc:
                            print(f"   🗺️ State: {current_loc.get('state')}")
                        
                        if 'lga' in current_loc:
                            print(f"   📍 LGA: {current_loc.get('lga')}")
                        
                        if 'landmark' in current_loc:
                            print(f"   🏛️ Landmark: {current_loc.get('landmark')}")
                        
                        if 'carrier' in current_loc:
                            print(f"   📶 Carrier: {current_loc.get('carrier')}")
                        
                        if 'precision_level' in current_loc:
                            precision = current_loc.get('precision_level', '').replace('_', ' ').title()
                            print(f"   🎯 Precision Level: {precision}")
                        
                        print(f"   ⏰ Timestamp: {current_loc.get('timestamp', 'Unknown')}")
                    else:
                        print("   ⏳ No location data available yet")
                else:
                    print(f"   ❌ Status check failed: {status_data.get('error')}")
            else:
                print(f"   ❌ Status request failed: {response.status_code}")
            
            # Wait before next update
            if i < num_updates - 1:
                print("   ⏳ Waiting 10 seconds for next update...")
                time.sleep(10)
                
        except Exception as e:
            print(f"   ❌ Update error: {e}")
    
    return locations

def analyze_location_accuracy(locations):
    """Analyze location accuracy and precision"""
    print(f"\n📊 LOCATION ACCURACY ANALYSIS")
    print("-" * 50)
    
    if not locations:
        print("❌ No location data to analyze")
        return
    
    # Calculate statistics
    accuracies = [loc.get('accuracy', 0) for loc in locations if 'accuracy' in loc]
    latitudes = [loc.get('latitude', 0) for loc in locations if 'latitude' in loc]
    longitudes = [loc.get('longitude', 0) for loc in locations if 'longitude' in loc]
    
    if accuracies:
        avg_accuracy = sum(accuracies) / len(accuracies)
        best_accuracy = min(accuracies)
        worst_accuracy = max(accuracies)
        
        print(f"📈 Accuracy Statistics:")
        print(f"   Average: ±{avg_accuracy:.1f} meters")
        print(f"   Best: ±{best_accuracy:.1f} meters")
        print(f"   Worst: ±{worst_accuracy:.1f} meters")
    
    # Check for street addresses
    street_addresses = [loc.get('street_address') for loc in locations if 'street_address' in loc]
    if street_addresses:
        print(f"\n🏠 Street Addresses Found:")
        for i, addr in enumerate(street_addresses, 1):
            print(f"   {i}. {addr}")
    
    # Check precision levels
    precision_levels = [loc.get('precision_level') for loc in locations if 'precision_level' in loc]
    if precision_levels:
        print(f"\n🎯 Precision Levels:")
        for level in set(precision_levels):
            count = precision_levels.count(level)
            print(f"   {level.replace('_', ' ').title()}: {count} updates")
    
    # Movement analysis
    if len(latitudes) > 1 and len(longitudes) > 1:
        lat_range = max(latitudes) - min(latitudes)
        lon_range = max(longitudes) - min(longitudes)
        
        print(f"\n🚶 Movement Analysis:")
        print(f"   Latitude range: {lat_range:.6f} degrees")
        print(f"   Longitude range: {lon_range:.6f} degrees")
        
        # Estimate movement distance (rough calculation)
        movement_km = ((lat_range * 111) ** 2 + (lon_range * 111) ** 2) ** 0.5
        print(f"   Estimated movement: {movement_km:.2f} km")

def stop_tracking(session, tracking_id):
    """Stop location tracking"""
    print(f"\n🛑 Stopping tracking...")
    
    try:
        data = {'tracking_id': tracking_id}
        response = session.post(f"{BASE_URL}/api/stop_tracking", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Tracking stopped successfully")
                return True
            else:
                print(f"❌ Failed to stop tracking: {result.get('error')}")
                return False
        else:
            print(f"❌ Stop tracking request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Stop tracking error: {e}")
        return False

def main():
    """Main function to test precise location tracking"""
    print("🎯 AMADIOHA-M257 PRECISE LOCATION TEST")
    print("=" * 60)
    print(f"Target: {TARGET_NUMBER} (MTN Nigeria)")
    print(f"Test: Enhanced geolocation with street-level accuracy")
    print("=" * 60)
    
    # Step 1: Login
    session = login_and_get_session()
    if not session:
        print("❌ Cannot proceed without login")
        return
    
    # Step 2: Get phone information
    phone_info = get_phone_info(session, TARGET_NUMBER)
    if not phone_info:
        print("❌ Cannot proceed without phone info")
        return
    
    # Step 3: Start precise tracking
    tracking_id = start_precise_tracking(session, TARGET_NUMBER)
    if not tracking_id:
        print("❌ Cannot proceed without tracking ID")
        return
    
    # Step 4: Get precise location updates
    locations = get_precise_location_updates(session, tracking_id, 5)
    
    # Step 5: Analyze accuracy
    analyze_location_accuracy(locations)
    
    # Step 6: Stop tracking
    stop_tracking(session, tracking_id)
    
    print(f"\n🎉 PRECISE LOCATION TEST COMPLETED")
    print("=" * 60)
    print(f"✅ Phone validated: {TARGET_NUMBER}")
    print(f"✅ Carrier identified: MTN Nigeria (906 prefix)")
    print(f"✅ Location updates: {len(locations)} precise coordinates")
    print(f"✅ Enhanced features: Street addresses, states, landmarks")
    print("\n🔒 Remember: Use only for authorized cybersecurity testing!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
