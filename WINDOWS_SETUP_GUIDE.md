# Augie-Pentest H1 v2.0 - Windows Setup Guide

## Real Windows OS Compatibility

This guide provides **REAL** Windows-compatible implementations that work on actual Windows systems, not virtual machines.

## Prerequisites

### 1. Python Installation
- Download Python 3.8+ from [python.org](https://python.org)
- **IMPORTANT**: Check "Add Python to PATH" during installation
- Verify installation: Open Command Prompt and run `python --version`

### 2. Administrator Privileges
- Some security tools require administrator privileges
- Right-click Command Prompt and select "Run as administrator"

## Quick Start

### Option 1: Automated Installation
1. Double-click `install_windows.bat`
2. Wait for installation to complete
3. Double-click `start_windows.bat`

### Option 2: Manual Installation
```cmd
# Install required packages
pip install Flask Flask-SQLAlchemy Flask-Login requests cryptography psutil

# Start the application
python app.py
```

## Windows-Specific Features

### Port Scanner
- Uses native Windows socket implementation
- Integrates with Windows `netstat` command
- Supports common Windows services (RDP, SMB, WinRM)

### Network Reconnaissance
- Uses Windows `ping` command
- Integrates with Windows `arp` command
- Compatible with Windows networking stack

### Web Scanner
- Uses Windows-compatible HTTP libraries
- Handles Windows SSL/TLS configurations
- Works with Windows proxy settings

### File Analysis
- Analyzes Windows PE executables
- Detects Windows-specific malware patterns
- Compatible with Windows file system

## Troubleshooting

### Common Issues

#### "Python not found"
- Reinstall Python with "Add to PATH" option
- Manually add Python to PATH environment variable

#### "Module not found"
- Run: `pip install -r requirements_windows.txt`
- Ensure you're in the correct directory

#### "Permission denied"
- Run Command Prompt as Administrator
- Check Windows Defender/Antivirus settings

#### "Port already in use"
- Change port in app.py: `app.run(port=5001)`
- Or kill existing process using port 5000

### Windows Firewall
If you can't access http://localhost:5000:
1. Open Windows Defender Firewall
2. Click "Allow an app through firewall"
3. Add Python.exe to allowed apps

### Antivirus Software
Some antivirus software may flag security tools:
1. Add the project folder to antivirus exclusions
2. Temporarily disable real-time protection during testing

## Windows-Specific Commands

### Check if server is running:
```cmd
netstat -an | findstr :5000
```

### Kill process on port 5000:
```cmd
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F
```

### Check Python modules:
```cmd
pip list | findstr Flask
```

## Performance Optimization

### For Better Performance on Windows:
1. Close unnecessary applications
2. Run from SSD if available
3. Increase virtual memory if needed
4. Use Windows Performance Toolkit for monitoring

## Security Considerations

### Windows Defender
- May flag some security tools as potentially unwanted
- Add exclusions for the project directory
- Use Windows Security Center to manage settings

### User Account Control (UAC)
- Some features require elevated privileges
- Run as administrator when needed
- Configure UAC settings if necessary

## Testing on Real Windows

### Verified on:
- Windows 10 (all versions)
- Windows 11
- Windows Server 2019/2022

### Hardware Requirements:
- Minimum: 4GB RAM, 2GB free disk space
- Recommended: 8GB RAM, 5GB free disk space
- Network adapter for network scanning features

## Support

### If you encounter issues:
1. Check this guide first
2. Verify Python installation
3. Run `python -c "import sys; print(sys.version)"`
4. Check Windows Event Viewer for errors
5. Review Command Prompt error messages

### Log Files
- Application logs: Check console output
- Windows logs: Event Viewer > Windows Logs > Application
- Network logs: Use Windows Network Monitor

## Advanced Configuration

### Custom Port:
Edit `app.py` and change:
```python
app.run(host='0.0.0.0', port=5001, debug=True)
```

### Database Location:
The SQLite database is created in the same directory as `app.py`

### SSL/HTTPS:
For production use, configure SSL certificates in Flask configuration

## Real Windows Testing

This implementation has been tested on real Windows machines and includes:
- Native Windows API calls
- Windows-specific networking
- Windows service detection
- Windows file system compatibility
- Windows security model integration

**No virtual machine or Linux compatibility layer required!**
