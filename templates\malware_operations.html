{% extends 'base.html' %}
{% block title %}Malware Operations | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-virus text-danger"></i> MALWARE OPERATIONS CENTER</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <!-- Operation Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> Malware Operation Type</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="malwareType" class="form-label">Malware Type</label>
                            <select class="form-control" id="malwareType">
                                <option value="keylogger">🔑 Keylogger</option>
                                <option value="ransomware">🔒 Ransomware</option>
                                <option value="trojan">🐴 Trojan</option>
                                <option value="backdoor">🚪 Backdoor</option>
                                <option value="stealer">💳 Credential Stealer</option>
                                <option value="rat">🖥️ Remote Access Tool</option>
                                <option value="cryptominer">⛏️ Crypto Miner</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="deliveryMethod" class="form-label">Delivery Method</label>
                            <select class="form-control" id="deliveryMethod">
                                <option value="pdf">📄 PDF Exploit</option>
                                <option value="excel">📊 Excel Macro</option>
                                <option value="word">📝 Word Document</option>
                                <option value="exe">💻 Executable</option>
                                <option value="powershell">⚡ PowerShell Script</option>
                                <option value="batch">📜 Batch File</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="evasionLevel" class="form-label">Evasion Level</label>
                            <select class="form-control" id="evasionLevel">
                                <option value="basic">🟢 Basic (FUD Level 1)</option>
                                <option value="advanced">🟡 Advanced (FUD Level 2)</option>
                                <option value="military">🔴 Military Grade (FUD Level 3)</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger w-100" id="generateMalwareBtn">
                                <i class="fas fa-virus"></i> GENERATE MALWARE
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Operations -->
    <div class="row">
        <!-- Left Column: Configuration -->
        <div class="col-lg-8">
            <!-- Keylogger Configuration -->
            <div class="card mb-4" id="keyloggerCard">
                <div class="card-header">
                    <h5><i class="fas fa-keyboard"></i> Advanced Keylogger Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🎯 Target Configuration</h6>
                            <div class="mb-3">
                                <label class="form-label">Target Applications</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetBrowsers" checked>
                                    <label class="form-check-label" for="targetBrowsers">🌐 Web Browsers (Chrome, Firefox, Edge)</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetBanking" checked>
                                    <label class="form-check-label" for="targetBanking">🏦 Banking Applications</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetEmail" checked>
                                    <label class="form-check-label" for="targetEmail">📧 Email Clients</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="targetCrypto" checked>
                                    <label class="form-check-label" for="targetCrypto">₿ Cryptocurrency Wallets</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>📡 Data Exfiltration</h6>
                            <div class="mb-3">
                                <label for="telegramBot" class="form-label">Telegram Bot Token</label>
                                <input type="text" class="form-control" id="telegramBot" placeholder="Bot token for data exfiltration">
                            </div>
                            <div class="mb-3">
                                <label for="ngrokTunnel" class="form-label">Ngrok Tunnel</label>
                                <input type="text" class="form-control" id="ngrokTunnel" placeholder="Ngrok URL for C2 communication">
                            </div>
                            <div class="mb-3">
                                <label for="encryptionKey" class="form-label">AES Encryption Key</label>
                                <input type="text" class="form-control" id="encryptionKey" placeholder="256-bit encryption key">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payload Generation -->
            <div class="card mb-4" id="payloadCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-code"></i> Payload Generation</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>🎯 Target System</h6>
                            <select class="form-control mb-2" id="targetOS">
                                <option value="windows">🪟 Windows 10/11</option>
                                <option value="macos">🍎 macOS</option>
                                <option value="linux">🐧 Linux</option>
                                <option value="android">🤖 Android</option>
                            </select>
                            <select class="form-control mb-2" id="architecture">
                                <option value="x64">64-bit (x64)</option>
                                <option value="x86">32-bit (x86)</option>
                                <option value="arm">ARM (Mobile)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <h6>🛡️ Evasion Techniques</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="antiVM" checked>
                                <label class="form-check-label" for="antiVM">Anti-VM Detection</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="antiDebug" checked>
                                <label class="form-check-label" for="antiDebug">Anti-Debug</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="polymorphic" checked>
                                <label class="form-check-label" for="polymorphic">Polymorphic Code</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="processHollowing" checked>
                                <label class="form-check-label" for="processHollowing">Process Hollowing</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>🔒 Persistence</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="registryPersist" checked>
                                <label class="form-check-label" for="registryPersist">Registry Persistence</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="servicePersist" checked>
                                <label class="form-check-label" for="servicePersist">Service Installation</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="startupPersist" checked>
                                <label class="form-check-label" for="startupPersist">Startup Folder</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="taskPersist" checked>
                                <label class="form-check-label" for="taskPersist">Scheduled Task</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-warning me-2" id="generatePayloadBtn">
                                <i class="fas fa-cog"></i> Generate Payload
                            </button>
                            <button class="btn btn-info me-2" id="testPayloadBtn">
                                <i class="fas fa-vial"></i> Test in Sandbox
                            </button>
                            <button class="btn btn-success" id="downloadPayloadBtn" disabled>
                                <i class="fas fa-download"></i> Download Payload
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Weaponization -->
            <div class="card mb-4" id="documentCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt"></i> Document Weaponization</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📄 Document Type</h6>
                            <div class="mb-3">
                                <select class="form-control" id="docType">
                                    <option value="pdf">📄 PDF with JavaScript</option>
                                    <option value="docx">📝 Word with Macros</option>
                                    <option value="xlsx">📊 Excel with Macros</option>
                                    <option value="pptx">📊 PowerPoint with Macros</option>
                                    <option value="rtf">📄 RTF with Embedded Objects</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="docTemplate" class="form-label">Document Template</label>
                                <select class="form-control" id="docTemplate">
                                    <option value="invoice">🧾 Invoice Template</option>
                                    <option value="resume">📋 Resume Template</option>
                                    <option value="contract">📜 Contract Template</option>
                                    <option value="report">📊 Report Template</option>
                                    <option value="certificate">🏆 Certificate Template</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>🎯 Social Engineering</h6>
                            <div class="mb-3">
                                <label for="senderName" class="form-label">Sender Name</label>
                                <input type="text" class="form-control" id="senderName" placeholder="e.g., HR Department">
                            </div>
                            <div class="mb-3">
                                <label for="companyName" class="form-label">Company Name</label>
                                <input type="text" class="form-control" id="companyName" placeholder="e.g., Microsoft Nigeria">
                            </div>
                            <div class="mb-3">
                                <label for="urgencyLevel" class="form-label">Urgency Level</label>
                                <select class="form-control" id="urgencyLevel">
                                    <option value="high">🔴 High - Immediate Action Required</option>
                                    <option value="medium">🟡 Medium - Response Needed</option>
                                    <option value="low">🟢 Low - Informational</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" id="generateDocBtn">
                                <i class="fas fa-file-alt"></i> Generate Document
                            </button>
                            <button class="btn btn-secondary me-2" id="previewDocBtn">
                                <i class="fas fa-eye"></i> Preview
                            </button>
                            <button class="btn btn-success" id="downloadDocBtn" disabled>
                                <i class="fas fa-download"></i> Download Document
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Status & Results -->
        <div class="col-lg-4">
            <!-- Generation Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-tachometer-alt"></i> Generation Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>🦠 Malware Type:</strong> <span id="statusMalwareType">Not selected</span>
                    </div>
                    <div class="mb-2">
                        <strong>📦 Delivery Method:</strong> <span id="statusDelivery">Not selected</span>
                    </div>
                    <div class="mb-2">
                        <strong>🛡️ Evasion Level:</strong> <span id="statusEvasion">Not selected</span>
                    </div>
                    <div class="mb-2">
                        <strong>📈 Progress:</strong>
                        <div class="progress mt-1">
                            <div class="progress-bar" id="malwareProgressBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>🎯 Detection Rate:</strong> <span id="detectionRate" class="badge bg-success">0/70 AV</span>
                    </div>
                    <div class="mb-2">
                        <strong>📊 FUD Status:</strong> <span id="fudStatus" class="badge bg-success">Fully Undetectable</span>
                    </div>
                </div>
            </div>

            <!-- AV Evasion Results -->
            <div class="card mb-4" id="avResultsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt"></i> AV Evasion Results</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>🛡️ Windows Defender:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>🔥 Kaspersky:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>🦅 McAfee:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>🔒 Norton:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>🌐 Bitdefender:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>⚡ Avast:</strong> <span class="badge bg-success">Bypassed</span>
                    </div>
                    <div class="mb-2">
                        <strong>🎯 Overall Score:</strong> <span class="badge bg-success">70/70 Bypassed</span>
                    </div>
                </div>
            </div>

            <!-- Deployment Options -->
            <div class="card mb-4" id="deploymentCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-rocket"></i> Deployment Options</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button class="btn btn-warning w-100 mb-2" id="emailDeployBtn">
                            <i class="fas fa-envelope"></i> Email Campaign
                        </button>
                        <button class="btn btn-info w-100 mb-2" id="usbDeployBtn">
                            <i class="fas fa-usb"></i> USB Autorun
                        </button>
                        <button class="btn btn-primary w-100 mb-2" id="webDeployBtn">
                            <i class="fas fa-globe"></i> Web Download
                        </button>
                        <button class="btn btn-secondary w-100" id="networkDeployBtn">
                            <i class="fas fa-network-wired"></i> Network Share
                        </button>
                    </div>
                </div>
            </div>

            <!-- C2 Communication -->
            <div class="card mb-4" id="c2Card" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-satellite-dish"></i> C2 Communication</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>📡 Status:</strong> <span id="c2Status" class="badge bg-success">Active</span>
                    </div>
                    <div class="mb-2">
                        <strong>🔗 Ngrok URL:</strong><br>
                        <small id="ngrokUrl" class="text-muted">https://kodiak-pleasing-hornet.ngrok-free.app</small>
                    </div>
                    <div class="mb-2">
                        <strong>🤖 Telegram Bot:</strong> <span id="telegramStatus" class="badge bg-success">Connected</span>
                    </div>
                    <div class="mb-2">
                        <strong>💻 Active Infections:</strong> <span id="activeInfections" class="badge bg-info">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>📊 Data Collected:</strong> <span id="dataCollected">0 KB</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Generated Malware History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Generated Malware History</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="refreshMalwareHistoryBtn">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-danger" id="clearHistoryBtn">
                            <i class="fas fa-trash"></i> Clear History
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Malware Type</th>
                                    <th>Delivery Method</th>
                                    <th>Evasion Level</th>
                                    <th>Detection Rate</th>
                                    <th>Generated</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="malwareHistoryBody">
                                <!-- History will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentMalwareConfig = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Event listeners
    document.getElementById('malwareType').addEventListener('change', updateMalwareConfig);
    document.getElementById('deliveryMethod').addEventListener('change', updateMalwareConfig);
    document.getElementById('evasionLevel').addEventListener('change', updateMalwareConfig);
    document.getElementById('generateMalwareBtn').addEventListener('click', generateMalware);
    document.getElementById('generatePayloadBtn').addEventListener('click', generatePayload);
    document.getElementById('testPayloadBtn').addEventListener('click', testPayload);
    document.getElementById('generateDocBtn').addEventListener('click', generateDocument);
    document.getElementById('refreshMalwareHistoryBtn').addEventListener('click', loadMalwareHistory);

    // Initialize configuration
    updateMalwareConfig();
    loadMalwareHistory();
});

function updateMalwareConfig() {
    const malwareType = document.getElementById('malwareType').value;
    const deliveryMethod = document.getElementById('deliveryMethod').value;
    const evasionLevel = document.getElementById('evasionLevel').value;

    // Update status display
    document.getElementById('statusMalwareType').textContent = malwareType.charAt(0).toUpperCase() + malwareType.slice(1);
    document.getElementById('statusDelivery').textContent = deliveryMethod.toUpperCase();
    document.getElementById('statusEvasion').textContent = evasionLevel.charAt(0).toUpperCase() + evasionLevel.slice(1);

    // Show/hide relevant cards
    showRelevantCards(malwareType, deliveryMethod);

    // Update detection rate based on evasion level
    updateDetectionRate(evasionLevel);
}

function showRelevantCards(malwareType, deliveryMethod) {
    // Hide all cards first
    document.getElementById('keyloggerCard').style.display = 'none';
    document.getElementById('payloadCard').style.display = 'none';
    document.getElementById('documentCard').style.display = 'none';

    // Show keylogger card for keylogger type
    if (malwareType === 'keylogger' || malwareType === 'stealer') {
        document.getElementById('keyloggerCard').style.display = 'block';
    }

    // Show payload card for executable delivery
    if (['exe', 'powershell', 'batch'].includes(deliveryMethod)) {
        document.getElementById('payloadCard').style.display = 'block';
    }

    // Show document card for document delivery
    if (['pdf', 'excel', 'word'].includes(deliveryMethod)) {
        document.getElementById('documentCard').style.display = 'block';
    }
}

function updateDetectionRate(evasionLevel) {
    let detectionRate, fudStatus, badgeClass;

    switch(evasionLevel) {
        case 'basic':
            detectionRate = '5/70 AV';
            fudStatus = 'Good FUD';
            badgeClass = 'bg-warning';
            break;
        case 'advanced':
            detectionRate = '2/70 AV';
            fudStatus = 'Excellent FUD';
            badgeClass = 'bg-success';
            break;
        case 'military':
            detectionRate = '0/70 AV';
            fudStatus = 'Military Grade FUD';
            badgeClass = 'bg-success';
            break;
    }

    document.getElementById('detectionRate').textContent = detectionRate;
    document.getElementById('detectionRate').className = `badge ${badgeClass}`;
    document.getElementById('fudStatus').textContent = fudStatus;
    document.getElementById('fudStatus').className = `badge ${badgeClass}`;
}

function generateMalware() {
    const malwareType = document.getElementById('malwareType').value;
    const deliveryMethod = document.getElementById('deliveryMethod').value;
    const evasionLevel = document.getElementById('evasionLevel').value;

    const btn = document.getElementById('generateMalwareBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Simulate malware generation
    let progress = 0;
    const progressBar = document.getElementById('malwareProgressBar');

    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressBar.style.width = progress + '%';

        if (progress >= 100) {
            clearInterval(interval);

            // Show results
            document.getElementById('avResultsCard').style.display = 'block';
            document.getElementById('deploymentCard').style.display = 'block';
            document.getElementById('c2Card').style.display = 'block';

            // Enable download buttons
            document.getElementById('downloadPayloadBtn').disabled = false;
            document.getElementById('downloadDocBtn').disabled = false;

            btn.innerHTML = '<i class="fas fa-virus"></i> GENERATE MALWARE';
            btn.disabled = false;

            // Add to history
            addToMalwareHistory(malwareType, deliveryMethod, evasionLevel);

            alert('✅ Malware generated successfully with full AV evasion!');
        }
    }, 200);
}

function generatePayload() {
    const targetOS = document.getElementById('targetOS').value;
    const architecture = document.getElementById('architecture').value;

    const btn = document.getElementById('generatePayloadBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Simulate payload generation
    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-cog"></i> Generate Payload';
        btn.disabled = false;

        document.getElementById('downloadPayloadBtn').disabled = false;
        alert(`✅ Payload generated for ${targetOS} (${architecture})`);
    }, 3000);
}

function testPayload() {
    const btn = document.getElementById('testPayloadBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    btn.disabled = true;

    // Simulate sandbox testing
    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-vial"></i> Test in Sandbox';
        btn.disabled = false;

        alert('✅ Sandbox test completed: 0 detections, full stealth mode confirmed');
    }, 5000);
}

function generateDocument() {
    const docType = document.getElementById('docType').value;
    const template = document.getElementById('docTemplate').value;
    const senderName = document.getElementById('senderName').value;
    const companyName = document.getElementById('companyName').value;

    const btn = document.getElementById('generateDocBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Simulate document generation
    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-file-alt"></i> Generate Document';
        btn.disabled = false;

        document.getElementById('downloadDocBtn').disabled = false;
        alert(`✅ Weaponized ${docType.toUpperCase()} document generated with ${template} template`);
    }, 4000);
}

function addToMalwareHistory(type, delivery, evasion) {
    const tbody = document.getElementById('malwareHistoryBody');
    const row = tbody.insertRow(0); // Insert at top

    const detectionRates = {
        'basic': '5/70',
        'advanced': '2/70',
        'military': '0/70'
    };

    row.innerHTML = `
        <td>${type.charAt(0).toUpperCase() + type.slice(1)}</td>
        <td>${delivery.toUpperCase()}</td>
        <td>${evasion.charAt(0).toUpperCase() + evasion.slice(1)}</td>
        <td><span class="badge bg-success">${detectionRates[evasion]} AV</span></td>
        <td>${new Date().toLocaleString()}</td>
        <td><span class="badge bg-success">Generated</span></td>
        <td>
            <button class="btn btn-sm btn-outline-primary me-1" onclick="downloadMalware('${type}_${Date.now()}')">
                <i class="fas fa-download"></i>
            </button>
            <button class="btn btn-sm btn-outline-info me-1" onclick="viewMalwareDetails('${type}_${Date.now()}')">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteMalware('${type}_${Date.now()}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
}

function loadMalwareHistory() {
    // Load malware generation history
    fetch('/api/malware_history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayMalwareHistory(data.malware);
        }
    })
    .catch(error => console.error('History load error:', error));
}

function displayMalwareHistory(malwareList) {
    const tbody = document.getElementById('malwareHistoryBody');
    tbody.innerHTML = '';

    malwareList.forEach(malware => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${malware.type}</td>
            <td>${malware.delivery_method}</td>
            <td>${malware.evasion_level}</td>
            <td><span class="badge bg-${malware.detection_rate === '0/70' ? 'success' : 'warning'}">${malware.detection_rate} AV</span></td>
            <td>${new Date(malware.generated_at).toLocaleString()}</td>
            <td><span class="badge bg-${malware.status === 'active' ? 'success' : 'secondary'}">${malware.status}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="downloadMalware('${malware.id}')">
                    <i class="fas fa-download"></i>
                </button>
                <button class="btn btn-sm btn-outline-info me-1" onclick="viewMalwareDetails('${malware.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteMalware('${malware.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
    });
}

function downloadMalware(malwareId) {
    // Download generated malware
    window.open(`/api/download_malware/${malwareId}`, '_blank');
}

function viewMalwareDetails(malwareId) {
    // View detailed malware information
    alert(`View malware details: ${malwareId}`);
}

function deleteMalware(malwareId) {
    if (confirm('Are you sure you want to delete this malware?')) {
        fetch(`/api/delete_malware/${malwareId}`, { method: 'DELETE' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadMalwareHistory();
                alert('Malware deleted successfully');
            }
        })
        .catch(error => console.error('Delete error:', error));
    }
}

// Deployment functions
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('emailDeployBtn').addEventListener('click', () => {
        alert('📧 Email deployment configured - malware will be sent via phishing campaign');
    });

    document.getElementById('usbDeployBtn').addEventListener('click', () => {
        alert('💾 USB autorun payload generated - copy to USB device');
    });

    document.getElementById('webDeployBtn').addEventListener('click', () => {
        alert('🌐 Web deployment ready - malware hosted on secure server');
    });

    document.getElementById('networkDeployBtn').addEventListener('click', () => {
        alert('🖧 Network share deployment configured - lateral movement enabled');
    });
});

// Update C2 status
setInterval(() => {
    // Simulate C2 updates
    const infections = Math.floor(Math.random() * 5);
    const dataSize = Math.floor(Math.random() * 1000);

    document.getElementById('activeInfections').textContent = infections;
    document.getElementById('dataCollected').textContent = `${dataSize} KB`;
}, 10000);
</script>

{% endblock %}
