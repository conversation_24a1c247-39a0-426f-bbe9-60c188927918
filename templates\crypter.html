{% extends 'base.html' %}
{% block title %}Crypter & Obfuscation | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-lock text-success"></i> CRYPTER & OBFUSCATION</h2>
                <div class="alert alert-success mb-0">
                    <i class="fas fa-shield-alt"></i> PAYLOAD PROTECTION
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Payload Encryption -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-code text-success"></i> PAYLOAD ENCRYPTION</h5>
                </div>
                <div class="card-body">
                    <form id="encryptPayloadForm">
                        <div class="mb-3">
                            <label class="form-label">PAYLOAD CODE</label>
                            <textarea class="form-control" id="payloadCode" rows="8" placeholder="Enter your payload code here..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ENCRYPTION METHOD</label>
                            <select class="form-select" id="encryptMethod" required>
                                <option value="XOR">XOR ENCRYPTION</option>
                                <option value="Base64">BASE64 ENCODING</option>
                                <option value="ROT13">ROT13 CIPHER</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">ENCRYPTION KEY (OPTIONAL)</label>
                            <input type="text" class="form-control" id="encryptKey" placeholder="Leave empty for auto-generation">
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-lock"></i> ENCRYPT PAYLOAD
                        </button>
                    </form>
                    
                    <div id="encryptResult" class="mt-3" style="display: none;">
                        <h6>ENCRYPTED PAYLOAD:</h6>
                        <div class="alert alert-dark">
                            <textarea id="encryptedCode" class="form-control" rows="12" readonly></textarea>
                        </div>
                        <div class="mb-2">
                            <strong>KEY:</strong> <code id="usedKey"></code>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('encryptedCode')">
                            <i class="fas fa-copy"></i> COPY CODE
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadPayload('encrypted_payload.py', 'encryptedCode')">
                            <i class="fas fa-download"></i> DOWNLOAD
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Obfuscation -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-eye-slash text-warning"></i> CODE OBFUSCATION</h5>
                </div>
                <div class="card-body">
                    <form id="obfuscateForm">
                        <div class="mb-3">
                            <label class="form-label">PYTHON CODE</label>
                            <textarea class="form-control" id="sourceCode" rows="10" placeholder="Enter Python code to obfuscate..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-mask"></i> OBFUSCATE CODE
                        </button>
                    </form>
                    
                    <div id="obfuscateResult" class="mt-3" style="display: none;">
                        <h6>OBFUSCATED CODE:</h6>
                        <div class="alert alert-dark">
                            <textarea id="obfuscatedCode" class="form-control" rows="12" readonly></textarea>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('obfuscatedCode')">
                            <i class="fas fa-copy"></i> COPY CODE
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="downloadPayload('obfuscated.py', 'obfuscatedCode')">
                            <i class="fas fa-download"></i> DOWNLOAD
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Encryption -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-file-lock text-info"></i> FILE ENCRYPTION</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <form id="fileEncryptForm">
                                <div class="mb-3">
                                    <label class="form-label">FILE CONTENT</label>
                                    <textarea class="form-control" id="fileContent" rows="8" placeholder="Paste file content here..." required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ENCRYPTION METHOD</label>
                                    <select class="form-select" id="fileEncryptMethod" required>
                                        <option value="AES">AES-256 ENCRYPTION</option>
                                        <option value="XOR">XOR ENCRYPTION</option>
                                        <option value="Base64">BASE64 ENCODING</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">PASSWORD</label>
                                    <input type="password" class="form-control" id="filePassword" placeholder="Enter encryption password" required>
                                </div>
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-shield-alt"></i> ENCRYPT FILE
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div id="fileEncryptResult" style="display: none;">
                                <h6>ENCRYPTED DATA:</h6>
                                <div class="alert alert-dark">
                                    <textarea id="encryptedFileData" class="form-control" rows="10" readonly></textarea>
                                </div>
                                <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('encryptedFileData')">
                                    <i class="fas fa-copy"></i> COPY DATA
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="downloadPayload('encrypted_file.txt', 'encryptedFileData')">
                                    <i class="fas fa-download"></i> DOWNLOAD
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-dark">
                    <h5><i class="fas fa-info-circle"></i> CRYPTER INFORMATION</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>ENCRYPTION METHODS:</h6>
                            <ul>
                                <li><strong>XOR:</strong> Simple but effective obfuscation</li>
                                <li><strong>Base64:</strong> Encoding to hide plaintext</li>
                                <li><strong>ROT13:</strong> Caesar cipher variant</li>
                                <li><strong>AES-256:</strong> Military-grade encryption</li>
                            </ul>
                            
                            <h6>OBFUSCATION TECHNIQUES:</h6>
                            <ul>
                                <li>Variable name randomization</li>
                                <li>Junk code insertion</li>
                                <li>Control flow modification</li>
                                <li>String encoding</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>DETECTION EVASION:</h6>
                            <ul>
                                <li>Signature-based antivirus bypass</li>
                                <li>Static analysis evasion</li>
                                <li>Runtime decryption</li>
                                <li>Polymorphic techniques</li>
                            </ul>
                            
                            <h6>DEFENSE STRATEGIES:</h6>
                            <ul>
                                <li>Behavioral analysis systems</li>
                                <li>Sandboxing environments</li>
                                <li>Machine learning detection</li>
                                <li>Code integrity verification</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-danger mt-3">
                        <strong>LEGAL WARNING:</strong> These crypter tools are for educational and authorized penetration testing only. 
                        Using them to create malware or bypass security controls without permission is illegal.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('encryptPayloadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        payload: document.getElementById('payloadCode').value,
        method: document.getElementById('encryptMethod').value,
        key: document.getElementById('encryptKey').value || null
    };
    
    fetch('/api/encrypt_payload', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('encryptedCode').value = data.encrypted_payload;
            document.getElementById('usedKey').textContent = data.key;
            document.getElementById('encryptResult').style.display = 'block';
        }
    });
});

document.getElementById('obfuscateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        code: document.getElementById('sourceCode').value
    };
    
    fetch('/api/obfuscate_code', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('obfuscatedCode').value = data.obfuscated_code;
            document.getElementById('obfuscateResult').style.display = 'block';
        }
    });
});

document.getElementById('fileEncryptForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        content: document.getElementById('fileContent').value,
        method: document.getElementById('fileEncryptMethod').value,
        password: document.getElementById('filePassword').value
    };
    
    fetch('/api/encrypt_file', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('encryptedFileData').value = JSON.stringify(data.encrypted_data, null, 2);
            document.getElementById('fileEncryptResult').style.display = 'block';
        }
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}

function downloadPayload(filename, elementId) {
    const content = document.getElementById(elementId).value;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>

{% endblock %}
