{% extends 'base.html' %}
{% block title %}Social Engineering Tools | CyberSec Platform{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-secret text-warning"></i> Social Engineering Tools</h2>
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle"></i> FOR EDUCATIONAL PURPOSES ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Phishing Email Generator -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-envelope text-danger"></i> Phishing Email Generator</h5>
                </div>
                <div class="card-body">
                    <form id="phishingEmailForm">
                        <div class="mb-3">
                            <label class="form-label">Template Type</label>
                            <select class="form-select" id="templateType" required>
                                <option value="">Select Template</option>
                                <option value="banking">Banking Alert</option>
                                <option value="social_media">Social Media</option>
                                <option value="work">Work Related</option>
                                <option value="shipping">Shipping/Delivery</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Target Name</label>
                            <input type="text" class="form-control" id="targetName" placeholder="John Doe">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Malicious Link</label>
                            <input type="url" class="form-control" id="maliciousLink" placeholder="http://evil-site.com/phish">
                        </div>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-magic"></i> Generate Email
                        </button>
                    </form>
                    
                    <div id="emailResult" class="mt-3" style="display: none;">
                        <h6>Generated Email Content:</h6>
                        <div class="alert alert-dark">
                            <pre id="emailContent"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credential Harvester Generator -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-fishing text-danger"></i> Credential Harvester</h5>
                </div>
                <div class="card-body">
                    <form id="harvesterForm">
                        <div class="mb-3">
                            <label class="form-label">Target Site Name</label>
                            <input type="text" class="form-control" id="targetSite" placeholder="Facebook" required>
                        </div>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-code"></i> Generate Harvester
                        </button>
                    </form>
                    
                    <div id="harvesterResult" class="mt-3" style="display: none;">
                        <h6>Generated HTML:</h6>
                        <div class="alert alert-dark">
                            <textarea id="harvesterCode" class="form-control" rows="10" readonly></textarea>
                        </div>
                        <button class="btn btn-sm btn-outline-light" onclick="copyToClipboard('harvesterCode')">
                            <i class="fas fa-copy"></i> Copy Code
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Educational Information -->
    <div class="row">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-graduation-cap"></i> Educational Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Social Engineering Techniques:</h6>
                            <ul>
                                <li><strong>Phishing:</strong> Fraudulent emails to steal credentials</li>
                                <li><strong>Spear Phishing:</strong> Targeted phishing attacks</li>
                                <li><strong>Pretexting:</strong> Creating false scenarios</li>
                                <li><strong>Baiting:</strong> Offering something enticing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Defense Strategies:</h6>
                            <ul>
                                <li>Employee security awareness training</li>
                                <li>Email filtering and authentication</li>
                                <li>Multi-factor authentication</li>
                                <li>Regular security assessments</li>
                            </ul>
                        </div>
                    </div>
                    <div class="alert alert-danger mt-3">
                        <strong>Legal Notice:</strong> These tools are for educational and authorized testing only. 
                        Unauthorized use against systems you don't own is illegal and unethical.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('phishingEmailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        template_type: document.getElementById('templateType').value,
        target_info: {
            name: document.getElementById('targetName').value
        },
        malicious_link: document.getElementById('maliciousLink').value
    };
    
    fetch('/api/generate_phishing_email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('emailContent').textContent = data.email_content;
            document.getElementById('emailResult').style.display = 'block';
        }
    });
});

document.getElementById('harvesterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_site: document.getElementById('targetSite').value
    };
    
    fetch('/api/generate_credential_harvester', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('harvesterCode').value = data.html_content;
            document.getElementById('harvesterResult').style.display = 'block';
        }
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    // Show feedback
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}
</script>

{% endblock %}
