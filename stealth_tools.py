"""
Stealth Security Tools for Augie-Pentest H1 v2.0
Antivirus evasion and stealth implementations
"""

import base64
import time
import random
import string
import os
import sys
from datetime import datetime

class StealthObfuscator:
    """Advanced obfuscation to avoid AV detection"""
    
    def __init__(self):
        self.key = self._generate_key()
        self.delay_range = (0.1, 0.5)
    
    def _generate_key(self):
        """Generate dynamic obfuscation key"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=16))
    
    def obfuscate_string(self, text):
        """Obfuscate strings to avoid signature detection"""
        encoded = base64.b64encode(text.encode()).decode()
        return f"base64.b64decode('{encoded}').decode()"
    
    def random_delay(self):
        """Random delays to avoid behavioral detection"""
        delay = random.uniform(*self.delay_range)
        time.sleep(delay)
    
    def split_operations(self, operation_list):
        """Split operations to avoid pattern detection"""
        for op in operation_list:
            self.random_delay()
            yield op

class StealthNetworkScanner:
    """Stealth network scanning with AV evasion"""
    
    def __init__(self):
        self.obfuscator = StealthObfuscator()
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101',
            'Mozilla/5.0 (compatible; MSIE 11.0; Windows NT 10.0; WOW64)'
        ]
    
    def stealth_port_scan(self, target, ports):
        """Stealth port scanning with evasion techniques"""
        import socket
        
        results = []
        port_list = self._parse_ports(ports)
        
        # Randomize scan order to avoid detection
        random.shuffle(port_list)
        
        for port in self.obfuscator.split_operations(port_list):
            try:
                # Use random source ports
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(random.uniform(1, 3))  # Random timeouts
                
                # Connect with stealth
                result = sock.connect_ex((target, port))
                
                if result == 0:
                    service = self._get_stealth_service(port)
                    results.append({
                        'port': port,
                        'state': 'open',
                        'service': service,
                        'method': 'stealth'
                    })
                
                sock.close()
                
            except Exception:
                continue
        
        return results
    
    def _parse_ports(self, port_range):
        """Parse port range safely"""
        if '-' in str(port_range):
            start, end = map(int, str(port_range).split('-'))
            return list(range(start, min(end + 1, start + 50)))  # Limit for stealth
        else:
            return [int(port_range)]
    
    def _get_stealth_service(self, port):
        """Get service name without triggering AV"""
        # Obfuscated service mapping
        services = {
            21: 'file-transfer', 22: 'secure-shell', 23: 'remote-terminal',
            25: 'mail-transfer', 53: 'name-resolution', 80: 'web-service',
            110: 'mail-retrieval', 135: 'rpc-service', 139: 'file-sharing',
            143: 'mail-access', 443: 'secure-web', 445: 'network-sharing',
            993: 'secure-mail', 995: 'secure-pop', 1433: 'database',
            3389: 'remote-desktop', 5985: 'management'
        }
        return services.get(port, 'unknown-service')

class StealthWebAnalyzer:
    """Stealth web analysis with AV evasion"""
    
    def __init__(self):
        self.obfuscator = StealthObfuscator()
        self.headers = {
            'User-Agent': random.choice([
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101'
            ]),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def stealth_web_scan(self, url):
        """Stealth web scanning with evasion"""
        try:
            import requests
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            # Random delay before request
            self.obfuscator.random_delay()
            
            # Make request with stealth headers
            response = requests.get(
                url, 
                headers=self.headers,
                timeout=10,
                verify=False,
                allow_redirects=True
            )
            
            analysis = {
                'url': url,
                'status': response.status_code,
                'headers_analysis': self._analyze_headers(response.headers),
                'content_analysis': self._analyze_content(response.text),
                'security_score': 0,
                'recommendations': []
            }
            
            # Calculate security score
            analysis['security_score'] = self._calculate_security_score(analysis)
            
            return analysis
            
        except Exception as e:
            return {'error': f'Analysis failed: {str(e)}'}
    
    def _analyze_headers(self, headers):
        """Analyze security headers stealthily"""
        security_headers = [
            'X-Frame-Options', 'X-XSS-Protection', 'X-Content-Type-Options',
            'Strict-Transport-Security', 'Content-Security-Policy'
        ]
        
        analysis = {}
        for header in security_headers:
            value = headers.get(header, 'Not-Present')
            analysis[header.replace('-', '_').lower()] = value
        
        return analysis
    
    def _analyze_content(self, content):
        """Analyze content for security issues"""
        issues = []
        content_lower = content.lower()
        
        # Check for common issues (obfuscated)
        patterns = [
            ('script_injection', '<script>'),
            ('sql_patterns', 'sql error'),
            ('debug_info', 'debug'),
            ('server_info', 'server:')
        ]
        
        for issue_type, pattern in patterns:
            if pattern in content_lower:
                issues.append(issue_type)
        
        return {'potential_issues': issues}
    
    def _calculate_security_score(self, analysis):
        """Calculate security score"""
        score = 100
        
        # Deduct for missing headers
        headers = analysis['headers_analysis']
        for header, value in headers.items():
            if value == 'Not-Present':
                score -= 15
        
        # Deduct for content issues
        issues = analysis['content_analysis']['potential_issues']
        score -= len(issues) * 10
        
        return max(0, score)

class StealthFileAnalyzer:
    """Stealth file analysis with AV evasion"""
    
    def __init__(self):
        self.obfuscator = StealthObfuscator()
        self.safe_extensions = ['.txt', '.log', '.cfg', '.ini', '.json']
    
    def stealth_file_analysis(self, file_path):
        """Analyze files without triggering AV"""
        try:
            if not os.path.exists(file_path):
                return {'error': 'File not accessible'}
            
            file_info = {
                'name': os.path.basename(file_path),
                'size': os.path.getsize(file_path),
                'extension': os.path.splitext(file_path)[1].lower(),
                'analysis_time': datetime.now().isoformat()
            }
            
            # Safe file analysis
            analysis = self._safe_file_check(file_path, file_info)
            
            return analysis
            
        except Exception as e:
            return {'error': f'Analysis error: {str(e)}'}
    
    def _safe_file_check(self, file_path, file_info):
        """Safe file checking without AV triggers"""
        analysis = {
            'file_info': file_info,
            'safety_level': 'unknown',
            'characteristics': [],
            'recommendations': []
        }
        
        # Check file extension
        if file_info['extension'] in self.safe_extensions:
            analysis['safety_level'] = 'safe'
            analysis['recommendations'].append('File type appears safe')
        else:
            analysis['safety_level'] = 'review'
            analysis['recommendations'].append('Review file type and source')
        
        # Check file size
        if file_info['size'] > 10 * 1024 * 1024:  # 10MB
            analysis['characteristics'].append('large_file')
            analysis['recommendations'].append('Large file - verify legitimacy')
        
        return analysis

class StealthSystemInfo:
    """Stealth system information gathering"""
    
    def __init__(self):
        self.obfuscator = StealthObfuscator()
    
    def get_stealth_info(self):
        """Get system info without AV detection"""
        try:
            info = {
                'platform': sys.platform,
                'architecture': os.environ.get('PROCESSOR_ARCHITECTURE', 'unknown'),
                'user_context': os.environ.get('USERNAME', 'unknown'),
                'domain_context': os.environ.get('USERDOMAIN', 'unknown'),
                'analysis_time': datetime.now().isoformat()
            }
            
            # Add safe system information
            info['python_version'] = f"{sys.version_info.major}.{sys.version_info.minor}"
            info['working_directory'] = os.getcwd()
            
            return info
            
        except Exception as e:
            return {'error': f'Info gathering failed: {str(e)}'}

class StealthLogger:
    """Stealth logging to avoid AV detection"""
    
    def __init__(self):
        self.log_file = None
        self.obfuscator = StealthObfuscator()
    
    def log_activity(self, activity, details=None):
        """Log activities stealthily"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = {
                'timestamp': timestamp,
                'activity': activity,
                'details': details or {}
            }
            
            # Store in memory instead of files to avoid AV
            if not hasattr(self, 'memory_log'):
                self.memory_log = []
            
            self.memory_log.append(log_entry)
            
            # Keep only last 100 entries
            if len(self.memory_log) > 100:
                self.memory_log = self.memory_log[-100:]
            
            return True
            
        except Exception:
            return False
    
    def get_logs(self):
        """Retrieve logs from memory"""
        return getattr(self, 'memory_log', [])

# Stealth initialization
def initialize_stealth_mode():
    """Initialize stealth mode for the application"""
    try:
        # Set process name to something innocuous
        if hasattr(os, 'environ'):
            os.environ['PYTHONOPTIMIZE'] = '1'  # Optimize to reduce signatures
        
        # Initialize stealth components
        scanner = StealthNetworkScanner()
        web_analyzer = StealthWebAnalyzer()
        file_analyzer = StealthFileAnalyzer()
        system_info = StealthSystemInfo()
        logger = StealthLogger()
        
        return {
            'scanner': scanner,
            'web_analyzer': web_analyzer,
            'file_analyzer': file_analyzer,
            'system_info': system_info,
            'logger': logger,
            'status': 'stealth_mode_active'
        }
        
    except Exception as e:
        return {'error': f'Stealth initialization failed: {str(e)}'}
