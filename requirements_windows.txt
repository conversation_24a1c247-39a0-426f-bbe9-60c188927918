# Windows-Compatible Requirements for Augie-Pentest H1 v2.0
# These packages work on real Windows OS

# Core Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-WTF==1.1.1
WTForms==3.0.1

# Database
SQLAlchemy==2.0.21

# Security & Cryptography (Windows compatible)
cryptography==41.0.4
bcrypt==4.0.1
Werkzeug==2.3.7

# HTTP Requests (Windows compatible)
requests==2.31.0
urllib3==2.0.4

# Windows-specific packages
pywin32==306
wmi==1.5.1

# Network tools (Windows compatible)
python-nmap==0.7.1
scapy==2.5.0

# File handling
python-magic-bin==0.4.14

# PDF generation
reportlab==4.0.4

# Email
smtplib
email-validator==2.0.0

# Date/Time
python-dateutil==2.8.2

# JSON handling
jsonschema==4.19.0

# Environment variables
python-dotenv==1.0.0

# Windows registry access
winreg

# Windows services
psutil==5.9.5

# Windows-specific networking
netifaces==0.11.0

# Threading (built-in but listed for clarity)
threading
concurrent.futures

# Subprocess (built-in)
subprocess

# Socket (built-in)
socket

# OS operations (built-in)
os
sys
time
datetime
json
base64
hashlib
ipaddress
re
uuid
random
string
