{% extends "base.html" %}

{% block title %}Templates - Phishing Awareness Platform{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-envelope"></i> Templates</h2>
            <a href="{{ url_for('new_template') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> New Template
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> All Templates</h5>
                </div>
                <div class="card-body">
                    {% if templates %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Template Name</th>
                                        <th>Campaign</th>
                                        <th>Subject/Type</th>
                                        <th>Attachment</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for template in templates %}
                                    <tr>
                                        <td>
                                            <strong>{{ template.template_name }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ template.campaign.name if template.campaign else 'N/A' }}</span>
                                        </td>
                                        <td>
                                            {% if template.subject %}
                                                {{ template.subject[:50] }}...
                                            {% else %}
                                                <span class="text-secondary">Web Template</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if template.attachment_type %}
                                                <span class="badge bg-warning">{{ template.attachment_type }}</span>
                                            {% else %}
                                                <span class="text-secondary">None</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ template.created_at.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" title="Preview">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info" title="Clone">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-secondary mb-3"></i>
                            <h5 class="text-secondary">No templates yet</h5>
                            <p class="text-secondary">Create your first template to get started</p>
                            <a href="{{ url_for('new_template') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Template
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 