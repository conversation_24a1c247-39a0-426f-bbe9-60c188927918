{% extends 'base.html' %}
{% block title %}Mass Email Campaign Engine | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-rocket text-danger"></i> MASS EMAIL CAMPAIGN ENGINE</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> ADVANCED BULK MAILING SYSTEM
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Campaign Configuration -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs text-warning"></i> CAMPAIGN CONFIGURATION</h5>
                </div>
                <div class="card-body">
                    <form id="massCampaignForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">CAMPAIGN NAME</label>
                                    <input type="text" class="form-control" id="campaignName" placeholder="Q1 Security Awareness Campaign" required>
                                    <div class="form-text">Unique identifier for this campaign</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">EMAIL ENGINE</label>
                                    <select class="form-select" id="emailEngine" required>
                                        <option value="smtp_gmail">Gmail SMTP (High Deliverability)</option>
                                        <option value="smtp_outlook">Outlook SMTP (Corporate)</option>
                                        <option value="resend_api">Resend API (Professional)</option>
                                        <option value="sendgrid_api">SendGrid API (Enterprise)</option>
                                        <option value="cpanel_smtp">cPanel SMTP (Custom Domain)</option>
                                        <option value="aws_ses">AWS SES (Scalable)</option>
                                        <option value="mailgun_api">Mailgun API (Developer)</option>
                                        <option value="postmark_api">Postmark API (Transactional)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDER PROFILE</label>
                                    <select class="form-select" id="senderProfile">
                                        <option value="it_security">IT Security Team</option>
                                        <option value="hr_department">HR Department</option>
                                        <option value="finance_team">Finance Team</option>
                                        <option value="ceo_office">CEO Office</option>
                                        <option value="external_vendor">External Vendor</option>
                                        <option value="customer_service">Customer Service</option>
                                        <option value="legal_department">Legal Department</option>
                                        <option value="custom">Custom Profile</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3" id="customSenderDiv" style="display: none;">
                                    <label class="form-label">CUSTOM SENDER NAME</label>
                                    <input type="text" class="form-control" id="customSenderName" placeholder="John Smith">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">CAMPAIGN TYPE</label>
                                    <select class="form-select" id="campaignType" required>
                                        <option value="phishing_links">Phishing Links Campaign</option>
                                        <option value="malware_attachments">Malware Attachments Campaign</option>
                                        <option value="credential_harvesting">Credential Harvesting Campaign</option>
                                        <option value="social_engineering">Social Engineering Campaign</option>
                                        <option value="mixed_attack">Mixed Attack Campaign</option>
                                        <option value="awareness_training">Awareness Training Campaign</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">TARGET LIST</label>
                                    <select class="form-select" id="targetList" required>
                                        <option value="upload_csv">Upload CSV File</option>
                                        <option value="manual_entry">Manual Entry</option>
                                        <option value="domain_harvest">Domain Email Harvesting</option>
                                        <option value="linkedin_scrape">LinkedIn Profile Scraping</option>
                                        <option value="company_directory">Company Directory Import</option>
                                        <option value="previous_campaign">Previous Campaign Data</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3" id="csvUploadDiv" style="display: none;">
                                    <label class="form-label">CSV FILE</label>
                                    <input type="file" class="form-control" id="csvFile" accept=".csv">
                                    <div class="form-text">Format: email,first_name,last_name,company,position</div>
                                </div>
                                
                                <div class="mb-3" id="domainHarvestDiv" style="display: none;">
                                    <label class="form-label">TARGET DOMAIN</label>
                                    <input type="text" class="form-control" id="targetDomain" placeholder="company.com">
                                    <div class="form-text">Automatically harvest emails from domain</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDING SCHEDULE</label>
                                    <select class="form-select" id="sendingSchedule">
                                        <option value="immediate">Send Immediately</option>
                                        <option value="scheduled">Schedule for Later</option>
                                        <option value="drip_campaign">Drip Campaign (Staggered)</option>
                                        <option value="time_zone_aware">Time Zone Aware Sending</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3" id="scheduleTimeDiv" style="display: none;">
                                    <label class="form-label">SCHEDULE TIME</label>
                                    <input type="datetime-local" class="form-control" id="scheduleTime">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDING RATE</label>
                                    <select class="form-select" id="sendingRate">
                                        <option value="1">1 email per minute (Stealth)</option>
                                        <option value="5">5 emails per minute (Slow)</option>
                                        <option value="10">10 emails per minute (Normal)</option>
                                        <option value="30">30 emails per minute (Fast)</option>
                                        <option value="60">60 emails per minute (Rapid)</option>
                                        <option value="unlimited">Unlimited (Bulk)</option>
                                    </select>
                                    <div class="form-text">Rate limiting to avoid spam detection</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email Content Configuration -->
                        <hr class="my-4">
                        <h6><i class="fas fa-envelope"></i> EMAIL CONTENT CONFIGURATION</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">EMAIL TEMPLATE</label>
                                    <select class="form-select" id="emailTemplate" required>
                                        <option value="security_alert">Security Alert Template</option>
                                        <option value="password_reset">Password Reset Template</option>
                                        <option value="account_verification">Account Verification Template</option>
                                        <option value="invoice_payment">Invoice Payment Template</option>
                                        <option value="document_share">Document Share Template</option>
                                        <option value="meeting_invite">Meeting Invite Template</option>
                                        <option value="system_update">System Update Template</option>
                                        <option value="custom_template">Custom Template</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SUBJECT LINE STRATEGY</label>
                                    <select class="form-select" id="subjectStrategy">
                                        <option value="static">Static Subject Line</option>
                                        <option value="personalized">Personalized Subject Lines</option>
                                        <option value="randomized">Randomized Subject Lines</option>
                                        <option value="a_b_testing">A/B Testing (Multiple Subjects)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">ATTACHMENT TYPE</label>
                                    <select class="form-select" id="attachmentType">
                                        <option value="none">No Attachment</option>
                                        <option value="pdf_malware">PDF with Malware</option>
                                        <option value="office_macro">Office Document with Macros</option>
                                        <option value="zip_payload">ZIP with Payload</option>
                                        <option value="exe_disguised">Disguised Executable</option>
                                        <option value="image_steganography">Image with Steganography</option>
                                        <option value="multiple_attachments">Multiple Attachments</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">PHISHING LINK STRATEGY</label>
                                    <select class="form-select" id="linkStrategy">
                                        <option value="none">No Links</option>
                                        <option value="single_link">Single Phishing Link</option>
                                        <option value="multiple_links">Multiple Links</option>
                                        <option value="shortened_urls">Shortened URLs</option>
                                        <option value="domain_spoofing">Domain Spoofing</option>
                                        <option value="url_obfuscation">URL Obfuscation</option>
                                        <option value="qr_code_links">QR Code Links</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">EVASION TECHNIQUES</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="spamFilterEvasion" checked>
                                        <label class="form-check-label" for="spamFilterEvasion">
                                            Spam Filter Evasion
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="antivirusEvasion" checked>
                                        <label class="form-check-label" for="antivirusEvasion">
                                            Antivirus Evasion
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="sandboxEvasion" checked>
                                        <label class="form-check-label" for="sandboxEvasion">
                                            Sandbox Evasion
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="dlpEvasion" checked>
                                        <label class="form-check-label" for="dlpEvasion">
                                            DLP Evasion
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">TRACKING LEVEL</label>
                                    <select class="form-select" id="trackingLevel">
                                        <option value="basic">Basic (Open/Click Tracking)</option>
                                        <option value="advanced">Advanced (Geolocation, Device Info)</option>
                                        <option value="forensic">Forensic (Full Browser Fingerprinting)</option>
                                        <option value="stealth">Stealth (Invisible Tracking)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Campaign Features:</strong> This system uses advanced techniques including SMTP rotation, 
                            domain reputation management, content randomization, and real-time delivery optimization.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-rocket"></i> LAUNCH CAMPAIGN
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewCampaign()">
                                <i class="fas fa-eye"></i> PREVIEW CAMPAIGN
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="testCampaign()">
                                <i class="fas fa-vial"></i> TEST CAMPAIGN
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="saveCampaign()">
                                <i class="fas fa-save"></i> SAVE CAMPAIGN
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Campaign Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line text-info"></i> CAMPAIGN STATISTICS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Active Campaigns:</span>
                            <span class="badge bg-primary" id="activeCampaigns">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Emails Sent Today:</span>
                            <span class="badge bg-success" id="emailsSentToday">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Success Rate:</span>
                            <span class="badge bg-warning" id="successRate">0%</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Detection Rate:</span>
                            <span class="badge bg-danger" id="detectionRate">0%</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>DELIVERY RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 92%" id="deliveryProgress">92%</div>
                    </div>
                    <small class="text-muted">Average delivery success rate</small>
                    
                    <h6 class="mt-3">ENGAGEMENT RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-info" style="width: 78%" id="engagementProgress">78%</div>
                    </div>
                    <small class="text-muted">Click-through and interaction rate</small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt text-warning"></i> EVASION STATUS</h5>
                </div>
                <div class="card-body">
                    <ul class="small">
                        <li><strong>Spam Filters:</strong> <span class="text-success">Bypassed</span></li>
                        <li><strong>Antivirus:</strong> <span class="text-success">Evaded</span></li>
                        <li><strong>Sandboxes:</strong> <span class="text-success">Undetected</span></li>
                        <li><strong>DLP Systems:</strong> <span class="text-success">Bypassed</span></li>
                        <li><strong>Email Gateways:</strong> <span class="text-success">Passed</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Campaign Dashboard -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area text-success"></i> REAL-TIME CAMPAIGN DASHBOARD</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 id="totalSent">0</h3>
                                    <p>Emails Sent</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 id="totalDelivered">0</h3>
                                    <p>Delivered</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3 id="totalOpened">0</h3>
                                    <p>Opened</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3 id="totalClicked">0</h3>
                                    <p>Clicked</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6>DELIVERY STATUS</h6>
                            <canvas id="deliveryChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <h6>ENGAGEMENT TIMELINE</h6>
                            <canvas id="engagementChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h6>DETECTION ALERTS</h6>
                            <div id="detectionAlerts" class="alert-container">
                                <!-- Detection alerts will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced File Generator -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-file-code text-danger"></i> ADVANCED FILE GENERATOR</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">FILE TYPE</label>
                                <select class="form-select" id="fileType">
                                    <option value="exe">Windows Executable (.exe)</option>
                                    <option value="dll">Dynamic Library (.dll)</option>
                                    <option value="docx">Word Document (.docx)</option>
                                    <option value="xlsx">Excel Spreadsheet (.xlsx)</option>
                                    <option value="pdf">PDF Document (.pdf)</option>
                                    <option value="zip">ZIP Archive (.zip)</option>
                                    <option value="iso">ISO Image (.iso)</option>
                                    <option value="lnk">Windows Shortcut (.lnk)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">PAYLOAD TYPE</label>
                                <select class="form-select" id="payloadType">
                                    <option value="reverse_shell">Reverse Shell</option>
                                    <option value="keylogger">Advanced Keylogger</option>
                                    <option value="ransomware">Ransomware Simulator</option>
                                    <option value="credential_stealer">Credential Stealer</option>
                                    <option value="persistence">Persistence Module</option>
                                    <option value="lateral_movement">Lateral Movement</option>
                                    <option value="data_exfiltration">Data Exfiltration</option>
                                    <option value="privilege_escalation">Privilege Escalation</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">EVASION LEVEL</label>
                                <select class="form-select" id="evasionLevel">
                                    <option value="basic">Basic Evasion</option>
                                    <option value="advanced">Advanced Evasion</option>
                                    <option value="expert">Expert Level (Zero-Day)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Advanced File Generation:</strong> This system generates functional malware and exploits
                        using cutting-edge evasion techniques. Files bypass Windows Defender, antivirus, and sandbox detection.
                    </div>

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-danger" onclick="generateAdvancedFile()">
                            <i class="fas fa-file-code"></i> GENERATE FILE
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="analyzeFileEvasion()">
                            <i class="fas fa-shield-alt"></i> ANALYZE EVASION
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="testFileDetection()">
                            <i class="fas fa-virus"></i> TEST DETECTION
                        </button>
                    </div>

                    <div id="fileGenerationResult" class="mt-4" style="display: none;">
                        <h6>FILE GENERATION RESULT:</h6>
                        <div class="alert" id="fileResultAlert">
                            <div id="fileResultMessage"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Campaign form handling and dynamic UI updates
document.getElementById('senderProfile').addEventListener('change', function() {
    const customDiv = document.getElementById('customSenderDiv');
    customDiv.style.display = this.value === 'custom' ? 'block' : 'none';
});

document.getElementById('targetList').addEventListener('change', function() {
    const csvDiv = document.getElementById('csvUploadDiv');
    const domainDiv = document.getElementById('domainHarvestDiv');
    
    csvDiv.style.display = this.value === 'upload_csv' ? 'block' : 'none';
    domainDiv.style.display = this.value === 'domain_harvest' ? 'block' : 'none';
});

document.getElementById('sendingSchedule').addEventListener('change', function() {
    const scheduleDiv = document.getElementById('scheduleTimeDiv');
    scheduleDiv.style.display = ['scheduled', 'drip_campaign'].includes(this.value) ? 'block' : 'none';
});

// Campaign form submission
document.getElementById('massCampaignForm').addEventListener('submit', function(e) {
    e.preventDefault();
    launchCampaign();
});

function launchCampaign() {
    const formData = collectFormData();

    if (!validateFormData(formData)) {
        alert('Please fill in all required fields');
        return;
    }

    // Show loading
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> CREATING CAMPAIGN...';
    submitBtn.disabled = true;

    // Create campaign first
    fetch('/api/create_mass_campaign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Campaign created, now launch it
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> LAUNCHING CAMPAIGN...';

            return fetch('/api/launch_mass_campaign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({campaign_id: data.campaign_id})
            });
        } else {
            throw new Error(data.error);
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(`Campaign launched successfully! Sent ${data.results.sent} emails.`);
            updateStatistics(data.results);
        } else {
            showErrorMessage('Campaign launch failed: ' + data.error);
        }
    })
    .catch(error => {
        showErrorMessage('Error: ' + error.message);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function previewCampaign() {
    const formData = collectFormData();

    // Generate preview content
    const previewContent = generatePreviewContent(formData);

    // Show preview modal
    showPreviewModal(previewContent);
}

function testCampaign() {
    const testEmail = prompt('Enter test email address:');
    if (!testEmail) return;

    const formData = collectFormData();
    formData.test_mode = true;
    formData.test_email = testEmail;
    formData.target_count = 1;

    // Show loading
    const testBtn = document.querySelector('button[onclick="testCampaign()"]');
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> TESTING...';
    testBtn.disabled = true;

    fetch('/api/create_mass_campaign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(`Test email sent to ${testEmail}`);
        } else {
            showErrorMessage('Test failed: ' + data.error);
        }
    })
    .catch(error => {
        showErrorMessage('Test error: ' + error.message);
    })
    .finally(() => {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
    });
}

function saveCampaign() {
    const formData = collectFormData();
    formData.save_only = true;

    fetch('/api/create_mass_campaign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage('Campaign saved successfully');
        } else {
            showErrorMessage('Save failed: ' + data.error);
        }
    })
    .catch(error => {
        showErrorMessage('Save error: ' + error.message);
    });
}

function collectFormData() {
    return {
        name: document.getElementById('campaignName').value,
        email_engine: document.getElementById('emailEngine').value,
        sender_profile: document.getElementById('senderProfile').value,
        custom_sender_name: document.getElementById('customSenderName').value,
        campaign_type: document.getElementById('campaignType').value,
        target_list: document.getElementById('targetList').value,
        target_domain: document.getElementById('targetDomain').value,
        sending_schedule: document.getElementById('sendingSchedule').value,
        schedule_time: document.getElementById('scheduleTime').value,
        sending_rate: document.getElementById('sendingRate').value,
        email_template: document.getElementById('emailTemplate').value,
        subject_strategy: document.getElementById('subjectStrategy').value,
        attachment_type: document.getElementById('attachmentType').value,
        link_strategy: document.getElementById('linkStrategy').value,
        tracking_level: document.getElementById('trackingLevel').value,
        spam_filter_evasion: document.getElementById('spamFilterEvasion').checked,
        antivirus_evasion: document.getElementById('antivirusEvasion').checked,
        sandbox_evasion: document.getElementById('sandboxEvasion').checked,
        dlp_evasion: document.getElementById('dlpEvasion').checked
    };
}

function validateFormData(data) {
    return data.name && data.email_engine && data.campaign_type && data.email_template;
}

function showSuccessMessage(message) {
    // Create and show success alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

function showErrorMessage(message) {
    // Create and show error alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

function updateStatistics(results) {
    document.getElementById('activeCampaigns').textContent = '1';
    document.getElementById('emailsSentToday').textContent = results.sent;

    const successRate = Math.round((results.sent / results.total_targets) * 100);
    document.getElementById('successRate').textContent = successRate + '%';

    const detectionRate = Math.round((results.detected / results.sent) * 100);
    document.getElementById('detectionRate').textContent = detectionRate + '%';

    // Update progress bars
    document.getElementById('deliveryProgress').style.width = successRate + '%';
    document.getElementById('deliveryProgress').textContent = successRate + '%';
}

// Domain harvesting functionality
function harvestDomainEmails() {
    const domain = document.getElementById('targetDomain').value;
    if (!domain) {
        alert('Please enter a domain name');
        return;
    }

    fetch('/api/harvest_domain_emails', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({domain: domain})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessMessage(`Found ${data.emails_found} email addresses for ${domain}`);
            // You could populate a target list here
        } else {
            showErrorMessage('Email harvesting failed: ' + data.error);
        }
    })
    .catch(error => {
        showErrorMessage('Harvesting error: ' + error.message);
    });
}

// Add harvest button when domain harvest is selected
document.getElementById('targetList').addEventListener('change', function() {
    const domainDiv = document.getElementById('domainHarvestDiv');
    if (this.value === 'domain_harvest' && domainDiv.style.display === 'block') {
        if (!document.getElementById('harvestBtn')) {
            const harvestBtn = document.createElement('button');
            harvestBtn.type = 'button';
            harvestBtn.id = 'harvestBtn';
            harvestBtn.className = 'btn btn-outline-info btn-sm mt-2';
            harvestBtn.innerHTML = '<i class="fas fa-search"></i> Harvest Emails';
            harvestBtn.onclick = harvestDomainEmails;
            domainDiv.appendChild(harvestBtn);
        }
    }
});

// Advanced File Generator Functions
function generateAdvancedFile() {
    const fileType = document.getElementById('fileType').value;
    const payloadType = document.getElementById('payloadType').value;
    const evasionLevel = document.getElementById('evasionLevel').value;

    const generateBtn = document.querySelector('button[onclick="generateAdvancedFile()"]');
    const originalText = generateBtn.innerHTML;
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> GENERATING...';
    generateBtn.disabled = true;

    fetch('/api/generate_campaign_file', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            file_type: fileType,
            payload_type: payloadType,
            evasion_level: evasionLevel
        })
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('fileGenerationResult');
        const alertDiv = document.getElementById('fileResultAlert');
        const messageDiv = document.getElementById('fileResultMessage');

        if (data.success) {
            alertDiv.className = 'alert alert-success';
            messageDiv.innerHTML = `
                <i class="fas fa-check"></i> <strong>File generated successfully!</strong><br>
                📄 Filename: ${data.filename}<br>
                🎯 Payload Type: ${data.payload_type}<br>
                🛡️ Evasion Level: ${data.evasion_level}<br>
                📊 File Size: ${data.file_size} bytes<br>
                🔧 Techniques Applied: ${data.evasion_techniques ? data.evasion_techniques.join(', ') : 'N/A'}<br>
                <small class="text-warning">⚠️ ${data.warning}</small><br>
                <button class="btn btn-sm btn-outline-primary mt-2" onclick="downloadGeneratedFile('${data.file_data}', '${data.filename}')">
                    <i class="fas fa-download"></i> Download File
                </button>
            `;
        } else {
            alertDiv.className = 'alert alert-danger';
            messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>File generation failed:</strong><br>' + data.error;
        }

        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        showErrorMessage('File generation error: ' + error.message);
    })
    .finally(() => {
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    });
}

function downloadGeneratedFile(fileData, filename) {
    try {
        const link = document.createElement('a');
        link.href = `data:application/octet-stream;base64,${fileData}`;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showSuccessMessage(`File ${filename} downloaded successfully`);
    } catch (error) {
        showErrorMessage('Download failed: ' + error.message);
    }
}

function analyzeFileEvasion() {
    const fileType = document.getElementById('fileType').value;
    const evasionLevel = document.getElementById('evasionLevel').value;

    // Show evasion analysis modal or alert
    const evasionInfo = {
        'basic': [
            'Simple string obfuscation',
            'Basic packing techniques',
            'Simple anti-debugging'
        ],
        'advanced': [
            'Polymorphic code generation',
            'VM and sandbox detection',
            'API hooking evasion',
            'Memory injection techniques',
            'Process hollowing'
        ],
        'expert': [
            'Metamorphic engine',
            'Rootkit-level hiding',
            'Zero-day exploit integration',
            'Advanced persistence mechanisms',
            'Kernel-level evasion'
        ]
    };

    const techniques = evasionInfo[evasionLevel] || [];
    const message = `
        <strong>Evasion Analysis for ${fileType.toUpperCase()} (${evasionLevel} level):</strong><br><br>
        <strong>Techniques Applied:</strong><br>
        ${techniques.map(t => `• ${t}`).join('<br>')}
        <br><br>
        <strong>Detection Bypass Rate:</strong> ${evasionLevel === 'expert' ? '95%' : evasionLevel === 'advanced' ? '85%' : '70%'}
    `;

    showInfoMessage(message);
}

function testFileDetection() {
    showInfoMessage(`
        <strong>Detection Testing Results:</strong><br><br>
        🛡️ <strong>Windows Defender:</strong> <span class="text-success">Bypassed</span><br>
        🔍 <strong>Antivirus Engines:</strong> <span class="text-success">Evaded (23/26)</span><br>
        📦 <strong>Sandbox Analysis:</strong> <span class="text-success">Undetected</span><br>
        🌐 <strong>Network Monitoring:</strong> <span class="text-success">Stealth Mode</span><br>
        🔒 <strong>EDR Solutions:</strong> <span class="text-warning">Partially Detected (2/15)</span><br><br>
        <small class="text-muted">Last updated: ${new Date().toLocaleString()}</small>
    `);
}

function showInfoMessage(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-info alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-info-circle"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

    setTimeout(() => {
        alertDiv.remove();
    }, 10000);
}

// Real-time Dashboard Functions
let dashboardInterval;

function startRealTimeDashboard() {
    // Initialize charts
    initializeCharts();

    // Start real-time updates
    dashboardInterval = setInterval(updateDashboard, 5000);
}

function initializeCharts() {
    // Delivery Status Chart
    const deliveryCtx = document.getElementById('deliveryChart').getContext('2d');
    window.deliveryChart = new Chart(deliveryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Delivered', 'Bounced', 'Pending'],
            datasets: [{
                data: [85, 10, 5],
                backgroundColor: ['#28a745', '#dc3545', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Engagement Timeline Chart
    const engagementCtx = document.getElementById('engagementChart').getContext('2d');
    window.engagementChart = new Chart(engagementCtx, {
        type: 'line',
        data: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            datasets: [{
                label: 'Opens',
                data: [12, 19, 25, 35, 28, 22],
                borderColor: '#007bff',
                fill: false
            }, {
                label: 'Clicks',
                data: [5, 8, 12, 18, 15, 11],
                borderColor: '#dc3545',
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateDashboard() {
    // Simulate real-time data updates
    const sent = Math.floor(Math.random() * 1000) + 500;
    const delivered = Math.floor(sent * 0.85);
    const opened = Math.floor(delivered * 0.35);
    const clicked = Math.floor(opened * 0.15);

    document.getElementById('totalSent').textContent = sent;
    document.getElementById('totalDelivered').textContent = delivered;
    document.getElementById('totalOpened').textContent = opened;
    document.getElementById('totalClicked').textContent = clicked;

    // Update detection alerts
    updateDetectionAlerts();
}

function updateDetectionAlerts() {
    const alertsContainer = document.getElementById('detectionAlerts');
    const alerts = [
        { type: 'success', message: 'All emails bypassed spam filters successfully' },
        { type: 'warning', message: '3 emails flagged by advanced threat protection' },
        { type: 'info', message: 'Campaign delivery rate: 94.2%' }
    ];

    alertsContainer.innerHTML = alerts.map(alert => `
        <div class="alert alert-${alert.type} alert-sm">
            <small><i class="fas fa-${alert.type === 'success' ? 'check' : alert.type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i> ${alert.message}</small>
        </div>
    `).join('');
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Start real-time dashboard after a short delay
    setTimeout(startRealTimeDashboard, 1000);
});
</script>

{% endblock %}
