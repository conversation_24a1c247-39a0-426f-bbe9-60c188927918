# 📱 AMADIOHA-M257 Phone Tracking System

## 🎯 Overview

The AMADIOHA-M257 Phone Tracking System provides advanced real-time location monitoring and phone number intelligence gathering capabilities for cybersecurity professionals and authorized penetration testing.

## ⚡ Key Features

### 📞 Phone Number Intelligence
- **Comprehensive Analysis**: Country code, carrier, timezone, and validity verification
- **Multi-API Integration**: NumVerify, OpenCellID, and TrueCaller integration
- **Real-time Validation**: Instant phone number format validation and standardization

### 🛰️ Real-Time Location Tracking
- **Precise LAT/LON Coordinates**: GPS-level accuracy with meter precision
- **Multiple Data Sources**: GPS, Cell Tower, WiFi, and Hybrid positioning
- **Continuous Monitoring**: Real-time updates every 30 seconds
- **Configurable Duration**: Track from 30 minutes to 12 hours

### 🗺️ Interactive Mapping
- **Live Map Interface**: Real-time location visualization with dark theme
- **Location Trail**: Visual path tracking with accuracy circles
- **Multiple Map Layers**: Satellite, street, and terrain views
- **Fullscreen Mode**: Enhanced viewing for detailed analysis

### 📊 Advanced Analytics
- **Location History**: Complete tracking timeline with timestamps
- **Accuracy Metrics**: Precision measurements for each location point
- **Source Attribution**: GPS, Cell Tower, WiFi source identification
- **Export Capabilities**: CSV export for forensic analysis

## 🔧 Technical Implementation

### Database Schema

#### PhoneTracker Table
```sql
- id: Primary key
- phone_number: Target phone number (E.164 format)
- country_code: ISO country code
- carrier: Mobile network operator
- location_data: JSON encoded phone information
- latitude/longitude: Current coordinates
- accuracy: Location precision in meters
- last_seen: Last successful location update
- tracking_status: active/stopped/completed
- created_by: User ID who initiated tracking
- created_at/updated_at: Timestamps
```

#### LocationHistory Table
```sql
- id: Primary key
- tracker_id: Foreign key to PhoneTracker
- latitude/longitude: Historical coordinates
- accuracy: Location precision
- timestamp: When location was recorded
- source: Location data source (gps/cell/wifi)
```

### API Endpoints

#### Phone Information Analysis
```
POST /api/phone_info
{
    "phone_number": "+1234567890"
}
```

#### Start Real-Time Tracking
```
POST /api/start_tracking
{
    "phone_number": "+1234567890",
    "duration": 60
}
```

#### Get Tracking Status
```
GET /api/tracking_status/<tracking_id>
```

#### Stop Active Tracking
```
POST /api/stop_tracking
{
    "tracking_id": "track_1234567890"
}
```

#### Location History
```
GET /api/location_history/<tracker_id>
```

#### Tracked Numbers List
```
GET /api/tracked_numbers
```

## 🚀 Usage Instructions

### 1. Phone Number Analysis
1. Navigate to **Phone Tracker** in the main menu
2. Enter target phone number with country code (e.g., +1234567890)
3. Click **"Analyze Number"** to get comprehensive information
4. Review carrier, country, timezone, and validity data

### 2. Starting Real-Time Tracking
1. After phone analysis, select tracking duration
2. Click **"Start Real-Time Tracking"**
3. Monitor real-time location updates on the interactive map
4. View precise LAT/LON coordinates and accuracy metrics

### 3. Monitoring Active Tracking
- **Live Map**: Watch real-time location updates with trail visualization
- **Status Panel**: Monitor tracking duration, location count, and accuracy
- **Location Details**: View current coordinates, source, and timestamp
- **History Table**: Review recent location points with export options

### 4. Managing Tracked Numbers
- View all previously tracked numbers in the summary table
- Access historical location data for any tracked number
- Monitor tracking status and last seen timestamps
- Export location history for forensic analysis

## 🔒 Security & Compliance

### Ethical Use Requirements
- **Authorized Testing Only**: Use only for legitimate cybersecurity testing
- **Legal Compliance**: Ensure proper authorization before tracking
- **Data Protection**: Secure handling of location and personal data
- **Audit Trail**: Complete logging of all tracking activities

### Privacy Safeguards
- **User Authentication**: Login required for all tracking operations
- **Access Control**: Users can only view their own tracking data
- **Data Encryption**: Sensitive data encrypted in transit and at rest
- **Retention Policies**: Configurable data retention and deletion

## 🛠️ Advanced Configuration

### API Key Setup
Configure external API keys in environment variables:
```bash
NUMVERIFY_API_KEY=your_numverify_key
OPENCELLID_API_KEY=your_opencellid_key
IPGEOLOCATION_API_KEY=your_ipgeolocation_key
TRUECALLER_API_KEY=your_truecaller_key
```

### Tracking Parameters
- **Update Interval**: Default 30 seconds (configurable)
- **Maximum Duration**: 12 hours per session
- **Accuracy Threshold**: Minimum 5-meter precision
- **History Retention**: 100 most recent locations per tracker

### Performance Optimization
- **Background Processing**: Non-blocking tracking threads
- **Database Indexing**: Optimized queries for large datasets
- **Memory Management**: Efficient session handling
- **Rate Limiting**: API call optimization

## 📈 Monitoring & Analytics

### Real-Time Metrics
- **Active Tracking Sessions**: Current number of active trackers
- **Location Updates**: Real-time coordinate updates
- **Accuracy Statistics**: Average precision measurements
- **Source Distribution**: GPS vs Cell Tower vs WiFi data

### Historical Analysis
- **Movement Patterns**: Track target movement over time
- **Location Frequency**: Identify frequently visited locations
- **Accuracy Trends**: Monitor location precision improvements
- **Time Analysis**: Peak activity periods and patterns

## 🔧 Troubleshooting

### Common Issues

#### Phone Number Not Found
- Verify correct international format (+country_code_number)
- Check if number is active and reachable
- Ensure proper API key configuration

#### Location Updates Stopped
- Check internet connectivity
- Verify tracking session hasn't expired
- Restart tracking if necessary

#### Map Not Loading
- Ensure JavaScript is enabled
- Check browser compatibility (Chrome, Firefox, Safari)
- Verify Leaflet.js library loading

#### Accuracy Issues
- GPS accuracy depends on target device settings
- Indoor locations may have reduced precision
- Cell tower data provides approximate location

### Performance Tips
- **Optimal Duration**: 1-2 hours for best accuracy vs resource balance
- **Network Conditions**: Better connectivity improves location precision
- **Device Settings**: Target device GPS settings affect accuracy
- **Environmental Factors**: Indoor/outdoor location impacts precision

## 📋 Legal Disclaimer

**IMPORTANT**: This phone tracking system is designed exclusively for:
- Authorized cybersecurity testing and penetration testing
- Educational purposes in controlled environments
- Security research with proper permissions
- Corporate security assessments with written authorization

**Unauthorized use of this system for tracking individuals without consent is illegal and strictly prohibited.**

## 🆘 Support & Documentation

For technical support or questions about the phone tracking system:
- Review this documentation thoroughly
- Check the troubleshooting section
- Ensure proper API key configuration
- Verify legal authorization for tracking activities

**Remember**: Always obtain proper legal authorization before conducting any phone tracking activities.
