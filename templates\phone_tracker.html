{% extends 'base.html' %}
{% block title %}Phone Tracker | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-mobile-alt text-primary"></i> REAL-TIME PHONE TRACKER</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-satellite-dish"></i> PRECISE LAT/LON TRACKING
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Phone Number Input -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-phone"></i> Target Phone Number</h5>
                </div>
                <div class="card-body">
                    <form id="phoneInfoForm">
                        <div class="mb-3">
                            <label for="phoneNumber" class="form-label">Phone Number</label>
                            <input type="text" class="form-control" id="phoneNumber" placeholder="+2348031234567" required>
                            <div class="form-text">
                                Nigerian format: +234803... or 0803...<br>
                                International: +1, +44, +91, etc.
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <strong>🇳🇬 Nigerian Networks:</strong><br>
                                MTN: 803, 806, 813, 814, 816, 903, 906<br>
                                Airtel: 802, 808, 812, 901, 902, 907<br>
                                Glo: 805, 807, 811, 815, 905<br>
                                9mobile: 809, 817, 818, 908, 909
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i> Analyze Number
                        </button>
                    </form>
                </div>
            </div>

            <!-- Phone Information -->
            <div class="card mb-4" id="phoneInfoCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Phone Information</h5>
                </div>
                <div class="card-body" id="phoneInfoContent">
                    <!-- Phone info will be populated here -->
                </div>
            </div>

            <!-- Tracking Controls -->
            <div class="card mb-4" id="trackingControls" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-satellite"></i> Location Tracking</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="trackingDuration" class="form-label">Tracking Duration (minutes)</label>
                        <select class="form-control" id="trackingDuration">
                            <option value="30">30 minutes</option>
                            <option value="60" selected>1 hour</option>
                            <option value="120">2 hours</option>
                            <option value="360">6 hours</option>
                            <option value="720">12 hours</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-success w-100 mb-2" id="startTrackingBtn">
                        <i class="fas fa-play"></i> Start Real-Time Tracking
                    </button>
                    <button type="button" class="btn btn-danger w-100" id="stopTrackingBtn" style="display: none;">
                        <i class="fas fa-stop"></i> Stop Tracking
                    </button>
                </div>
            </div>

            <!-- Tracking Status -->
            <div class="card" id="trackingStatus" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Tracking Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>Status:</strong> <span id="statusText" class="badge bg-secondary">Inactive</span>
                    </div>
                    <div class="mb-2">
                        <strong>Duration:</strong> <span id="durationText">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>Locations Found:</strong> <span id="locationCount">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>Last Update:</strong> <span id="lastUpdate">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>Accuracy:</strong> <span id="accuracyText">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map and Results -->
        <div class="col-lg-8">
            <!-- Real-time Map -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-map-marked-alt"></i> Real-Time Location Map</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="centerMapBtn">
                            <i class="fas fa-crosshairs"></i> Center
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" id="fullscreenBtn">
                            <i class="fas fa-expand"></i> Fullscreen
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="trackingMap" style="height: 400px; background: #1a1a1a; border-radius: 0 0 8px 8px;">
                        <div class="d-flex align-items-center justify-content-center h-100 text-muted">
                            <div class="text-center">
                                <i class="fas fa-map-marked-alt fa-3x mb-3"></i>
                                <p>Start tracking to view real-time location</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Details -->
            <div class="card mb-4" id="locationDetails" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-map-pin"></i> Current Location Details</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>Latitude:</strong> <span id="currentLat" class="font-monospace">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>Longitude:</strong> <span id="currentLon" class="font-monospace">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>Accuracy:</strong> <span id="currentAccuracy">-</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2">
                                <strong>Source:</strong> <span id="currentSource">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>Timestamp:</strong> <span id="currentTimestamp">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>🏙️ City:</strong> <span id="currentCity">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>🗺️ State:</strong> <span id="currentState">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>📍 LGA:</strong> <span id="currentLGA">-</span>
                            </div>
                        </div>
                        <div class="col-12 mt-3">
                            <div class="mb-2">
                                <strong>🏠 Street Address:</strong><br>
                                <span id="currentAddress" class="text-primary">Resolving...</span>
                            </div>
                            <div class="mb-2">
                                <strong>🏛️ Landmark:</strong> <span id="currentLandmark">-</span>
                            </div>
                            <div class="mb-2">
                                <strong>🎯 Precision:</strong> <span id="precisionLevel" class="badge bg-secondary">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location History -->
            <div class="card" id="locationHistory" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Location History</h5>
                    <button class="btn btn-sm btn-outline-primary" id="exportHistoryBtn">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Latitude</th>
                                    <th>Longitude</th>
                                    <th>Accuracy</th>
                                    <th>Source</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History entries will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tracked Numbers -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Previously Tracked Numbers</h5>
                    <button class="btn btn-sm btn-outline-primary" id="refreshTrackedBtn">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Phone Number</th>
                                    <th>Country</th>
                                    <th>Carrier</th>
                                    <th>Last Location</th>
                                    <th>Last Seen</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="trackedNumbersBody">
                                <!-- Tracked numbers will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet CSS and JS for mapping -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<style>
.leaflet-container {
    background: #1a1a1a !important;
}

.tracking-marker {
    background: #ff4444;
    border: 3px solid #ffffff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
}

.location-trail {
    stroke: #00ff00;
    stroke-width: 3;
    stroke-opacity: 0.8;
    fill: none;
}

.accuracy-circle {
    fill: rgba(0, 255, 0, 0.1);
    stroke: #00ff00;
    stroke-width: 2;
    stroke-opacity: 0.5;
}
</style>

<script>
let map = null;
let currentMarker = null;
let trackingInterval = null;
let currentTrackingId = null;
let locationMarkers = [];
let trailPolyline = null;

// Initialize map
function initMap() {
    if (!map) {
        map = L.map('trackingMap').setView([40.7128, -74.0060], 10);
        
        // Dark theme tile layer
        L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);
    }
}

// Phone info form submission
document.getElementById('phoneInfoForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const phoneNumber = document.getElementById('phoneNumber').value;
    
    if (!phoneNumber) {
        alert('Please enter a phone number');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
    submitBtn.disabled = true;
    
    fetch('/api/phone_info', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone_number: phoneNumber })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPhoneInfo(data.data);
            document.getElementById('phoneInfoCard').style.display = 'block';
            document.getElementById('trackingControls').style.display = 'block';
            initMap();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to analyze phone number');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function displayPhoneInfo(data) {
    const content = document.getElementById('phoneInfoContent');

    // Check if it's a Nigerian number for enhanced display
    const isNigerian = data.number && data.number.includes('+234');

    let html = `
        <div class="row">
            <div class="col-md-6">
                <div class="mb-2"><strong>Number:</strong> ${data.number}</div>
                <div class="mb-2"><strong>Country:</strong> ${data.country || 'Unknown'}</div>
                <div class="mb-2"><strong>Carrier:</strong> ${data.carrier || 'Unknown'}</div>
                <div class="mb-2"><strong>Valid:</strong> ${data.is_valid ? 'Yes' : 'No'}</div>
            </div>
            <div class="col-md-6">
                <div class="mb-2"><strong>Type:</strong> ${data.number_type || 'Unknown'}</div>
                <div class="mb-2"><strong>Timezone:</strong> ${data.timezone ? data.timezone.join(', ') : 'Unknown'}</div>
    `;

    // Add Nigerian-specific information
    if (isNigerian && data.nigerian_carrier) {
        html += `
                <div class="mb-2"><strong>🇳🇬 Nigerian Carrier:</strong> ${data.nigerian_carrier}</div>
                <div class="mb-2"><strong>📶 Network Prefix:</strong> ${data.network_prefix || 'Unknown'}</div>
        `;
    }

    html += `
            </div>
        </div>
    `;

    // Add NumVerify information if available
    if (data.carrier_info || data.location || data.line_type) {
        html += `
        <hr>
        <div class="row">
            <div class="col-12">
                <h6>📡 NumVerify Data:</h6>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
        `;

        if (data.carrier_info) {
            html += `<div class="mb-2"><strong>Carrier Info:</strong> ${data.carrier_info}</div>`;
        }
        if (data.location) {
            html += `<div class="mb-2"><strong>Location:</strong> ${data.location}</div>`;
        }

        html += `
            </div>
            <div class="col-md-6">
        `;

        if (data.line_type) {
            html += `<div class="mb-2"><strong>Line Type:</strong> ${data.line_type}</div>`;
        }
        if (data.international_format) {
            html += `<div class="mb-2"><strong>International:</strong> ${data.international_format}</div>`;
        }

        html += `
            </div>
        </div>
        `;
    }

    // Add fallback warning if applicable
    if (data.fallback_mode) {
        html += `
        <div class="alert alert-warning mt-2">
            <i class="fas fa-exclamation-triangle"></i> Using fallback mode - NumVerify API may be unavailable
        </div>
        `;
    }

    content.innerHTML = html;
}

// Start tracking
document.getElementById('startTrackingBtn').addEventListener('click', function() {
    const phoneNumber = document.getElementById('phoneNumber').value;
    const duration = document.getElementById('trackingDuration').value;
    
    if (!phoneNumber) {
        alert('Please enter a phone number first');
        return;
    }
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';
    this.disabled = true;
    
    fetch('/api/start_tracking', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            phone_number: phoneNumber,
            duration: parseInt(duration)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentTrackingId = data.tracking_id;
            startTrackingUI();
            startLocationUpdates();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to start tracking');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-play"></i> Start Real-Time Tracking';
        this.disabled = false;
    });
});

function startTrackingUI() {
    document.getElementById('startTrackingBtn').style.display = 'none';
    document.getElementById('stopTrackingBtn').style.display = 'block';
    document.getElementById('trackingStatus').style.display = 'block';
    document.getElementById('locationDetails').style.display = 'block';
    document.getElementById('locationHistory').style.display = 'block';
    
    document.getElementById('statusText').textContent = 'Active';
    document.getElementById('statusText').className = 'badge bg-success';
}

function startLocationUpdates() {
    if (trackingInterval) {
        clearInterval(trackingInterval);
    }
    
    trackingInterval = setInterval(() => {
        if (currentTrackingId) {
            updateTrackingStatus();
        }
    }, 5000); // Update every 5 seconds
}

function updateTrackingStatus() {
    fetch(`/api/tracking_status/${currentTrackingId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateTrackingUI(data);
            if (data.current_location) {
                updateMapLocation(data.current_location);
                updateLocationDetails(data.current_location);
            }
            updateLocationHistory(data.location_history);
        }
    })
    .catch(error => {
        console.error('Error updating tracking status:', error);
    });
}

function updateTrackingUI(data) {
    document.getElementById('locationCount').textContent = data.total_locations;
    document.getElementById('lastUpdate').textContent = new Date(data.last_update).toLocaleString();
    
    if (data.status === 'completed') {
        stopTrackingUI();
    }
}

function updateMapLocation(location) {
    if (!map) return;
    
    const lat = location.latitude;
    const lon = location.longitude;
    const accuracy = location.accuracy || 50;
    
    // Update or create current location marker
    if (currentMarker) {
        map.removeLayer(currentMarker);
    }
    
    currentMarker = L.circleMarker([lat, lon], {
        color: '#ff4444',
        fillColor: '#ff4444',
        fillOpacity: 0.8,
        radius: 8
    }).addTo(map);
    
    // Add accuracy circle
    L.circle([lat, lon], {
        color: '#00ff00',
        fillColor: '#00ff00',
        fillOpacity: 0.1,
        radius: accuracy
    }).addTo(map);
    
    // Center map on new location
    map.setView([lat, lon], 15);
    
    // Add to trail
    locationMarkers.push([lat, lon]);
    if (trailPolyline) {
        map.removeLayer(trailPolyline);
    }
    if (locationMarkers.length > 1) {
        trailPolyline = L.polyline(locationMarkers, {
            color: '#00ff00',
            weight: 3,
            opacity: 0.8
        }).addTo(map);
    }
}

function updateLocationDetails(location) {
    document.getElementById('currentLat').textContent = location.latitude.toFixed(7);
    document.getElementById('currentLon').textContent = location.longitude.toFixed(7);
    document.getElementById('currentAccuracy').textContent = `±${location.accuracy}m`;
    document.getElementById('currentSource').textContent = location.source;
    document.getElementById('currentTimestamp').textContent = new Date(location.timestamp).toLocaleString();

    // Enhanced location display for precise tracking
    if (location.street_address) {
        document.getElementById('currentAddress').innerHTML = `<strong>${location.street_address}</strong>`;
    } else if (location.city && location.country) {
        document.getElementById('currentAddress').textContent = `${location.city}, ${location.country}`;
    } else {
        document.getElementById('currentAddress').textContent = `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    }

    // Update Nigerian-specific location details
    if (location.city) {
        document.getElementById('currentCity').textContent = location.city;
    }

    if (location.state) {
        document.getElementById('currentState').textContent = location.state;
    }

    if (location.lga) {
        document.getElementById('currentLGA').textContent = location.lga;
    }

    if (location.landmark) {
        document.getElementById('currentLandmark').textContent = location.landmark;
    }

    if (location.precision_level) {
        const precisionElement = document.getElementById('precisionLevel');
        precisionElement.textContent = location.precision_level.replace('_', ' ').toUpperCase();

        // Color code precision level
        if (location.precision_level === 'street_level') {
            precisionElement.className = 'badge bg-success';
        } else if (location.precision_level === 'enhanced_simulation') {
            precisionElement.className = 'badge bg-warning';
        } else {
            precisionElement.className = 'badge bg-secondary';
        }
    }
}

function updateLocationHistory(locations) {
    const tbody = document.getElementById('historyTableBody');
    tbody.innerHTML = '';
    
    locations.slice(0, 10).forEach(location => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${new Date(location.timestamp).toLocaleTimeString()}</td>
            <td class="font-monospace">${location.latitude.toFixed(6)}</td>
            <td class="font-monospace">${location.longitude.toFixed(6)}</td>
            <td>±${location.accuracy}m</td>
            <td><span class="badge bg-secondary">${location.source}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="centerOnLocation(${location.latitude}, ${location.longitude})">
                    <i class="fas fa-crosshairs"></i>
                </button>
            </td>
        `;
    });
}

function centerOnLocation(lat, lon) {
    if (map) {
        map.setView([lat, lon], 16);
    }
}

// Stop tracking
document.getElementById('stopTrackingBtn').addEventListener('click', function() {
    if (!currentTrackingId) return;
    
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Stopping...';
    this.disabled = true;
    
    fetch('/api/stop_tracking', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tracking_id: currentTrackingId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            stopTrackingUI();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to stop tracking');
    })
    .finally(() => {
        this.innerHTML = '<i class="fas fa-stop"></i> Stop Tracking';
        this.disabled = false;
    });
});

function stopTrackingUI() {
    if (trackingInterval) {
        clearInterval(trackingInterval);
        trackingInterval = null;
    }
    
    document.getElementById('startTrackingBtn').style.display = 'block';
    document.getElementById('stopTrackingBtn').style.display = 'none';
    document.getElementById('statusText').textContent = 'Stopped';
    document.getElementById('statusText').className = 'badge bg-secondary';
    
    currentTrackingId = null;
}

// Load tracked numbers on page load
document.addEventListener('DOMContentLoaded', function() {
    loadTrackedNumbers();
});

function loadTrackedNumbers() {
    fetch('/api/tracked_numbers')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayTrackedNumbers(data.tracked_numbers);
        }
    })
    .catch(error => {
        console.error('Error loading tracked numbers:', error);
    });
}

function displayTrackedNumbers(numbers) {
    const tbody = document.getElementById('trackedNumbersBody');
    tbody.innerHTML = '';
    
    numbers.forEach(number => {
        const row = tbody.insertRow();
        const lastLocation = number.latitude && number.longitude ? 
            `${number.latitude.toFixed(4)}, ${number.longitude.toFixed(4)}` : 'Unknown';
        const lastSeen = number.last_seen ? new Date(number.last_seen).toLocaleString() : 'Never';
        
        row.innerHTML = `
            <td class="font-monospace">${number.phone_number}</td>
            <td>${number.country_code || 'Unknown'}</td>
            <td>${number.carrier || 'Unknown'}</td>
            <td class="font-monospace">${lastLocation}</td>
            <td>${lastSeen}</td>
            <td><span class="badge bg-${number.tracking_status === 'active' ? 'success' : 'secondary'}">${number.tracking_status}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewHistory(${number.id})">
                    <i class="fas fa-history"></i> History
                </button>
            </td>
        `;
    });
}

function viewHistory(trackerId) {
    fetch(`/api/location_history/${trackerId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Display history in modal or update current view
            updateLocationHistory(data.locations);
            document.getElementById('locationHistory').style.display = 'block';
        }
    })
    .catch(error => {
        console.error('Error loading history:', error);
    });
}

// Refresh tracked numbers
document.getElementById('refreshTrackedBtn').addEventListener('click', loadTrackedNumbers);

// Center map button
document.getElementById('centerMapBtn').addEventListener('click', function() {
    if (currentMarker && map) {
        map.setView(currentMarker.getLatLng(), 15);
    }
});

// Export history
document.getElementById('exportHistoryBtn').addEventListener('click', function() {
    // Simple CSV export
    const rows = Array.from(document.querySelectorAll('#historyTableBody tr'));
    let csv = 'Time,Latitude,Longitude,Accuracy,Source\n';
    
    rows.forEach(row => {
        const cells = Array.from(row.cells);
        csv += cells.slice(0, 5).map(cell => cell.textContent.trim()).join(',') + '\n';
    });
    
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `phone_tracking_history_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
});
</script>

{% endblock %}
