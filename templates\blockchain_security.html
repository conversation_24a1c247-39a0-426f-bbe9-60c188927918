{% extends "base.html" %}

{% block title %}Blockchain Security Analysis - AMADIOHA-M257{% endblock %}

{% block content %}
<style>
    .security-card {
        background: #000000;
        border: none;
        border-radius: 15px;
        box-shadow: 
            10px 10px 20px rgba(0,0,0,0.5),
            -10px -10px 20px rgba(50,50,50,0.1),
            inset 0 0 0 1.8px #dc3545;
        padding: 1.5rem;
        margin: 7px;
        transition: all 0.3s ease;
        color: #ffffff;
    }
    
    .analysis-result {
        background: #000000;
        border: 1px solid #333333;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        color: #ffffff;
    }
    
    .vulnerability-high {
        color: #dc3545;
        font-weight: bold;
    }
    
    .vulnerability-medium {
        color: #ffc107;
        font-weight: bold;
    }
    
    .vulnerability-low {
        color: #28a745;
        font-weight: bold;
    }
    
    .form-control {
        background: #000000 !important;
        border: 1px solid #333333 !important;
        color: #ffffff !important;
    }
    
    .form-control:focus {
        background: #000000 !important;
        border-color: #dc3545 !important;
        color: #ffffff !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
    
    .btn-analyze {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: #ffffff;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .btn-analyze:hover {
        background: linear-gradient(45deg, #c82333, #dc3545);
        color: #ffffff;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-shield-alt text-danger"></i>
                Blockchain Security Analysis
            </h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="security-card">
                <h4><i class="fas fa-key"></i> Seed Phrase Security Analysis</h4>
                <p class="text-muted">Analyze seed phrase security vulnerabilities and entropy</p>
                
                <form id="seedAnalysisForm">
                    <div class="mb-3">
                        <label class="form-label">Seed Phrase</label>
                        <textarea class="form-control" 
                                  id="seedPhrase" 
                                  rows="3" 
                                  placeholder="Enter seed phrase for security analysis..."
                                  required></textarea>
                        <small class="text-muted">Enter 12-24 word seed phrase for analysis</small>
                    </div>
                    
                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-search"></i> Analyze Security
                    </button>
                </form>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="security-card">
                <h4><i class="fas fa-wallet"></i> Wallet Security Assessment</h4>
                <p class="text-muted">Comprehensive wallet security evaluation</p>
                
                <form id="walletAnalysisForm">
                    <div class="mb-3">
                        <label class="form-label">Wallet Address</label>
                        <input type="text" 
                               class="form-control" 
                               id="walletAddress" 
                               placeholder="Enter wallet address..."
                               required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Blockchain Network</label>
                        <select class="form-control" id="networkType">
                            <option value="ethereum">Ethereum</option>
                            <option value="bitcoin">Bitcoin</option>
                            <option value="binance">Binance Smart Chain</option>
                            <option value="polygon">Polygon</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-shield-alt"></i> Assess Security
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="security-card" id="analysisResults" style="display: none;">
                <h4><i class="fas fa-chart-line"></i> Security Analysis Results</h4>
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('seedAnalysisForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const seedPhrase = document.getElementById('seedPhrase').value;
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');
    
    if (!seedPhrase.trim()) {
        alert('Please enter a seed phrase for analysis');
        return;
    }
    
    // Show loading
    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Analyzing seed phrase security...</div>';
    resultsDiv.style.display = 'block';
    
    fetch('/api/analyze_seed_phrase', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            seed_phrase: seedPhrase
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySeedAnalysisResults(data.analysis);
        } else {
            resultsContent.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsContent.innerHTML = `<div class="alert alert-danger">Analysis failed: ${error.message}</div>`;
    });
});

function displaySeedAnalysisResults(analysis) {
    const resultsContent = document.getElementById('resultsContent');
    
    let vulnerabilityClass = 'vulnerability-low';
    if (analysis.vulnerability_score > 70) {
        vulnerabilityClass = 'vulnerability-high';
    } else if (analysis.vulnerability_score > 40) {
        vulnerabilityClass = 'vulnerability-medium';
    }
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h5>Security Metrics</h5>
                <ul class="list-unstyled">
                    <li><strong>Word Count:</strong> ${analysis.word_count}</li>
                    <li><strong>BIP39 Validity:</strong> ${analysis.valid_bip39.toFixed(1)}%</li>
                    <li><strong>Entropy Score:</strong> ${analysis.entropy_score.toFixed(1)}%</li>
                    <li><strong>Vulnerability Score:</strong> <span class="${vulnerabilityClass}">${analysis.vulnerability_score}%</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>Security Status</h5>
                <div class="progress mb-3">
                    <div class="progress-bar bg-${analysis.vulnerability_score > 50 ? 'danger' : 'success'}" 
                         style="width: ${100 - analysis.vulnerability_score}%">
                        Security: ${(100 - analysis.vulnerability_score).toFixed(0)}%
                    </div>
                </div>
            </div>
        </div>
    `;
    
    if (analysis.vulnerabilities.length > 0) {
        html += `
            <div class="mt-3">
                <h5 class="text-danger">Security Vulnerabilities</h5>
                <ul class="list-group">
        `;
        analysis.vulnerabilities.forEach(vuln => {
            html += `<li class="list-group-item bg-dark text-white border-danger">${vuln}</li>`;
        });
        html += `</ul></div>`;
    }
    
    if (analysis.security_recommendations.length > 0) {
        html += `
            <div class="mt-3">
                <h5 class="text-info">Security Recommendations</h5>
                <ul class="list-group">
        `;
        analysis.security_recommendations.forEach(rec => {
            html += `<li class="list-group-item bg-dark text-white border-info">${rec}</li>`;
        });
        html += `</ul></div>`;
    }
    
    resultsContent.innerHTML = html;
}
</script>
{% endblock %}
