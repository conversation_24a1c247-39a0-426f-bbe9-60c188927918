{% extends "base.html" %}

{% block title %}Blockchain Security Analysis - AMADIOHA-M257{% endblock %}

{% block content %}
<style>
    .security-card {
        background: #000000;
        border: none;
        border-radius: 15px;
        box-shadow: 
            10px 10px 20px rgba(0,0,0,0.5),
            -10px -10px 20px rgba(50,50,50,0.1),
            inset 0 0 0 1.8px #dc3545;
        padding: 1.5rem;
        margin: 7px;
        transition: all 0.3s ease;
        color: #ffffff;
    }
    
    .analysis-result {
        background: #000000;
        border: 1px solid #333333;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        color: #ffffff;
    }
    
    .vulnerability-high {
        color: #dc3545;
        font-weight: bold;
    }
    
    .vulnerability-medium {
        color: #ffc107;
        font-weight: bold;
    }
    
    .vulnerability-low {
        color: #28a745;
        font-weight: bold;
    }
    
    .form-control {
        background: #000000 !important;
        border: 1px solid #333333 !important;
        color: #ffffff !important;
    }
    
    .form-control:focus {
        background: #000000 !important;
        border-color: #dc3545 !important;
        color: #ffffff !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
    
    .btn-analyze {
        background: linear-gradient(45deg, #dc3545, #c82333);
        border: none;
        color: #ffffff;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .btn-analyze:hover {
        background: linear-gradient(45deg, #c82333, #dc3545);
        color: #ffffff;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-shield-alt text-danger"></i>
                Blockchain Security Analysis
            </h2>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="security-card">
                <h4><i class="fas fa-key"></i> Seed Phrase Security Analysis</h4>
                <p class="text-muted">Analyze seed phrase security vulnerabilities and entropy</p>
                
                <form id="seedAnalysisForm">
                    <div class="mb-3">
                        <label class="form-label">Seed Phrase</label>
                        <textarea class="form-control" 
                                  id="seedPhrase" 
                                  rows="3" 
                                  placeholder="Enter seed phrase for security analysis..."
                                  required></textarea>
                        <small class="text-muted">Enter 12-24 word seed phrase for analysis</small>
                    </div>
                    
                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-search"></i> Analyze Security
                    </button>
                </form>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="security-card">
                <h4><i class="fas fa-wallet"></i> Wallet Security Assessment</h4>
                <p class="text-muted">Comprehensive wallet security evaluation</p>
                
                <form id="walletAnalysisForm">
                    <div class="mb-3">
                        <label class="form-label">Wallet Address</label>
                        <input type="text" 
                               class="form-control" 
                               id="walletAddress" 
                               placeholder="Enter wallet address..."
                               required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Blockchain Network</label>
                        <select class="form-control" id="networkType">
                            <option value="ethereum">Ethereum</option>
                            <option value="bitcoin">Bitcoin</option>
                            <option value="binance">Binance Smart Chain</option>
                            <option value="polygon">Polygon</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-shield-alt"></i> Assess Security
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4">
            <div class="security-card">
                <h4><i class="fas fa-bolt"></i> Bitcoin Flash Testing</h4>
                <p class="text-muted">Advanced Bitcoin Flash Loan & Transaction Exploit Testing</p>

                <form id="flashTestForm">
                    <div class="mb-3">
                        <label class="form-label">Test Type</label>
                        <select class="form-control" id="flashTestType">
                            <option value="flash_exploit">Flash Loan Exploit</option>
                            <option value="malleability_test">Transaction Malleability</option>
                            <option value="stack_overflow">Script Stack Overflow</option>
                            <option value="timelock_bypass">Timelock Bypass</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Loan Amount (ETH)</label>
                        <input type="number"
                               class="form-control"
                               id="loanAmount"
                               placeholder="1000"
                               value="1000">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Target Hash</label>
                        <input type="text"
                               class="form-control"
                               id="targetHash"
                               placeholder="Target address hash..."
                               required>
                    </div>

                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-bolt"></i> Execute Flash Test
                    </button>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="security-card">
                <h4><i class="fas fa-search"></i> Advanced Blockchain Scan</h4>
                <p class="text-muted">Comprehensive blockchain analysis and scanning</p>

                <form id="blockchainScanForm">
                    <div class="mb-3">
                        <label class="form-label">Scan Type</label>
                        <select class="form-control" id="scanType">
                            <option value="wallet_analysis">Wallet Analysis</option>
                            <option value="transaction_trace">Transaction Tracing</option>
                            <option value="contract_analysis">Contract Analysis</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Target</label>
                        <input type="text"
                               class="form-control"
                               id="scanTarget"
                               placeholder="Address, TX hash, or contract..."
                               required>
                    </div>

                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fas fa-radar"></i> Start Scan
                    </button>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="security-card">
                <h4><i class="fab fa-bitcoin"></i> Bitcoin Security Analysis</h4>
                <p class="text-muted">Professional Bitcoin security testing - Augie-Pentest H1</p>

                <form id="bitcoinAnalysisForm">
                    <div class="mb-3">
                        <label class="form-label">Bitcoin Address</label>
                        <input type="text"
                               class="form-control"
                               id="bitcoinAddress"
                               placeholder="Enter Bitcoin address..."
                               required>
                        <small class="text-muted">Supports P2PKH, P2SH, Bech32, and Taproot addresses</small>
                    </div>

                    <button type="submit" class="btn btn-analyze w-100">
                        <i class="fab fa-bitcoin"></i> Analyze Bitcoin Security
                    </button>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="security-card">
                <h4><i class="fas fa-chart-line"></i> Real-time Monitoring</h4>
                <p class="text-muted">Live blockchain security monitoring</p>

                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Active Scans:</span>
                        <span id="activeScans">0</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Vulnerabilities Found:</span>
                        <span id="vulnerabilitiesFound" class="text-danger">0</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Wallets Analyzed:</span>
                        <span id="walletsAnalyzed">0</span>
                    </div>
                </div>

                <button class="btn btn-analyze w-100" onclick="startRealTimeMonitoring()">
                    <i class="fas fa-play"></i> Start Monitoring
                </button>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="security-card" id="analysisResults" style="display: none;">
                <h4><i class="fas fa-chart-line"></i> Security Analysis Results</h4>
                <div id="resultsContent"></div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('seedAnalysisForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const seedPhrase = document.getElementById('seedPhrase').value;
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');
    
    if (!seedPhrase.trim()) {
        alert('Please enter a seed phrase for analysis');
        return;
    }
    
    // Show loading
    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Analyzing seed phrase security...</div>';
    resultsDiv.style.display = 'block';
    
    fetch('/api/analyze_seed_phrase', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            seed_phrase: seedPhrase
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displaySeedAnalysisResults(data.analysis);
        } else {
            resultsContent.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsContent.innerHTML = `<div class="alert alert-danger">Analysis failed: ${error.message}</div>`;
    });
});

function displaySeedAnalysisResults(analysis) {
    const resultsContent = document.getElementById('resultsContent');
    
    let vulnerabilityClass = 'vulnerability-low';
    if (analysis.vulnerability_score > 70) {
        vulnerabilityClass = 'vulnerability-high';
    } else if (analysis.vulnerability_score > 40) {
        vulnerabilityClass = 'vulnerability-medium';
    }
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h5>Security Metrics</h5>
                <ul class="list-unstyled">
                    <li><strong>Word Count:</strong> ${analysis.word_count}</li>
                    <li><strong>BIP39 Validity:</strong> ${analysis.valid_bip39.toFixed(1)}%</li>
                    <li><strong>Entropy Score:</strong> ${analysis.entropy_score.toFixed(1)}%</li>
                    <li><strong>Vulnerability Score:</strong> <span class="${vulnerabilityClass}">${analysis.vulnerability_score}%</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h5>Security Status</h5>
                <div class="progress mb-3">
                    <div class="progress-bar bg-${analysis.vulnerability_score > 50 ? 'danger' : 'success'}" 
                         style="width: ${100 - analysis.vulnerability_score}%">
                        Security: ${(100 - analysis.vulnerability_score).toFixed(0)}%
                    </div>
                </div>
            </div>
        </div>
    `;
    
    if (analysis.vulnerabilities.length > 0) {
        html += `
            <div class="mt-3">
                <h5 class="text-danger">Security Vulnerabilities</h5>
                <ul class="list-group">
        `;
        analysis.vulnerabilities.forEach(vuln => {
            html += `<li class="list-group-item bg-dark text-white border-danger">${vuln}</li>`;
        });
        html += `</ul></div>`;
    }
    
    if (analysis.security_recommendations.length > 0) {
        html += `
            <div class="mt-3">
                <h5 class="text-info">Security Recommendations</h5>
                <ul class="list-group">
        `;
        analysis.security_recommendations.forEach(rec => {
            html += `<li class="list-group-item bg-dark text-white border-info">${rec}</li>`;
        });
        html += `</ul></div>`;
    }
    
    resultsContent.innerHTML = html;
}

// Bitcoin Flash Testing
document.getElementById('flashTestForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const testType = document.getElementById('flashTestType').value;
    const loanAmount = document.getElementById('loanAmount').value;
    const targetHash = document.getElementById('targetHash').value;
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    if (!targetHash.trim()) {
        alert('Please enter a target hash for flash testing');
        return;
    }

    // Show loading
    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Executing advanced Bitcoin flash test...</div>';
    resultsDiv.style.display = 'block';

    const parameters = {
        loan_amount: parseInt(loanAmount),
        target_hash: targetHash,
        asset_type: 'ETH',
        protocol: 'aave',
        market_volatility: 'medium',
        liquidity_depth: 'high',
        oracle_delay: 0,
        use_flash_path: true
    };

    fetch('/api/bitcoin_flash_test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            transaction_type: testType,
            parameters: parameters
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayFlashTestResults(data.test_result);
        } else {
            resultsContent.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsContent.innerHTML = `<div class="alert alert-danger">Flash test failed: ${error.message}</div>`;
    });
});

function displayFlashTestResults(testResult) {
    const resultsContent = document.getElementById('resultsContent');

    let html = `
        <div class="row">
            <div class="col-12">
                <h5><i class="fas fa-bolt"></i> Bitcoin Flash Test Results - Augie-Pentest H1</h5>
                <p class="text-muted">Advanced Flash Loan & Transaction Exploit Analysis</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>Test Configuration</h6>
                <ul class="list-unstyled">
                    <li><strong>Test Type:</strong> ${testResult.transaction_type}</li>
                    <li><strong>Loan Amount:</strong> ${testResult.parameters.loan_amount} ETH</li>
                    <li><strong>Protocol:</strong> ${testResult.parameters.protocol.toUpperCase()}</li>
                    <li><strong>Asset Type:</strong> ${testResult.parameters.asset_type}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Exploit Analysis</h6>
                <ul class="list-unstyled">
                    <li><strong>Risk Level:</strong> <span class="text-${testResult.exploit_potential.risk_level === 'high' ? 'danger' : testResult.exploit_potential.risk_level === 'medium' ? 'warning' : 'success'}">${testResult.exploit_potential.risk_level.toUpperCase()}</span></li>
                    <li><strong>Success Probability:</strong> ${(testResult.flash_mechanics.attack_simulation.success_probability * 100).toFixed(1)}%</li>
                    <li><strong>Estimated Profit:</strong> ${testResult.flash_mechanics.attack_simulation.estimated_profit.estimated_profit.toFixed(2)} ETH</li>
                </ul>
            </div>
        </div>
    `;

    if (testResult.script_analysis) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Script Analysis</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h7>Script PubKey:</h7>
                            <pre class="bg-dark text-light p-2 rounded">${testResult.script_analysis.scriptPubKey.join('\\n')}</pre>
                        </div>
                        <div class="col-md-6">
                            <h7>Script Sig:</h7>
                            <pre class="bg-dark text-light p-2 rounded">${testResult.script_analysis.scriptSig.join('\\n')}</pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    if (testResult.exploit_potential.exploit_vectors.length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-warning">Exploit Vectors Detected</h6>
                    <ul class="list-group">
        `;
        testResult.exploit_potential.exploit_vectors.forEach(vector => {
            html += `<li class="list-group-item bg-dark text-warning border-warning">${vector}</li>`;
        });
        html += `
                    </ul>
                </div>
            </div>
        `;
    }

    if (testResult.flash_mechanics.attack_simulation) {
        const simulation = testResult.flash_mechanics.attack_simulation;
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Flash Loan Attack Simulation</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h7>Attack Steps:</h7>
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item bg-dark text-white">${simulation.step_1}</li>
                                <li class="list-group-item bg-dark text-white">${simulation.step_2}</li>
                                <li class="list-group-item bg-dark text-white">${simulation.step_3}</li>
                                <li class="list-group-item bg-dark text-white">${simulation.step_4}</li>
                                <li class="list-group-item bg-dark text-white">${simulation.step_5}</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h7>Profitability Analysis:</h7>
                            <ul class="list-group">
                                <li class="list-group-item bg-dark text-white">Loan Fee: ${testResult.flash_mechanics.profitability_analysis.loan_fee_percentage}%</li>
                                <li class="list-group-item bg-dark text-white">Min Profit Required: ${testResult.flash_mechanics.profitability_analysis.minimum_profit_required} ETH</li>
                                <li class="list-group-item bg-dark text-white">Break Even: ${testResult.flash_mechanics.profitability_analysis.break_even_point} ETH</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    if (testResult.test_vectors && testResult.test_vectors.length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-info">Test Vectors</h6>
                    <div class="accordion" id="testVectorsAccordion">
        `;
        testResult.test_vectors.forEach((vector, index) => {
            html += `
                        <div class="accordion-item bg-dark">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed bg-dark text-white" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                                    ${vector.name} (${vector.test_type})
                                </button>
                            </h2>
                            <div id="collapse${index}" class="accordion-collapse collapse" data-bs-parent="#testVectorsAccordion">
                                <div class="accordion-body bg-dark text-white">
                                    <strong>Input:</strong> ${vector.input}<br>
                                    <strong>Expected Result:</strong> ${vector.expected_result}
                                </div>
                            </div>
                        </div>
            `;
        });
        html += `
                    </div>
                </div>
            </div>
        `;
    }

    resultsContent.innerHTML = html;
}

// Bitcoin security analysis
document.getElementById('bitcoinAnalysisForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const bitcoinAddress = document.getElementById('bitcoinAddress').value;
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    if (!bitcoinAddress.trim()) {
        alert('Please enter a Bitcoin address for analysis');
        return;
    }

    // Show loading
    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Performing advanced Bitcoin security analysis...</div>';
    resultsDiv.style.display = 'block';

    fetch('/api/bitcoin_security_analysis', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            bitcoin_address: bitcoinAddress
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayBitcoinAnalysisResults(data.analysis);
        } else {
            resultsContent.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsContent.innerHTML = `<div class="alert alert-danger">Bitcoin analysis failed: ${error.message}</div>`;
    });
});

function displayBitcoinAnalysisResults(analysis) {
    const resultsContent = document.getElementById('resultsContent');

    let html = `
        <div class="row">
            <div class="col-12">
                <h5><i class="fab fa-bitcoin"></i> Bitcoin Security Analysis Results - Augie-Pentest H1</h5>
                <p class="text-muted">Powered by Augment AI</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>Address Information</h6>
                <ul class="list-unstyled">
                    <li><strong>Address:</strong> ${analysis.address}</li>
                    <li><strong>Type:</strong> ${analysis.address_type}</li>
                    <li><strong>Vulnerability Score:</strong> <span class="text-${analysis.vulnerability_score > 50 ? 'danger' : 'success'}">${analysis.vulnerability_score.toFixed(1)}%</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Balance Information</h6>
                <ul class="list-unstyled">
    `;

    if (analysis.balance_analysis && analysis.balance_analysis.balance_btc !== undefined) {
        html += `
                    <li><strong>BTC Balance:</strong> ${analysis.balance_analysis.balance_btc.toFixed(8)} BTC</li>
                    <li><strong>USD Value:</strong> $${analysis.balance_analysis.balance_usd.toFixed(2)}</li>
                    <li><strong>Total Received:</strong> ${analysis.balance_analysis.total_received.toFixed(8)} BTC</li>
                    <li><strong>Total Sent:</strong> ${analysis.balance_analysis.total_sent.toFixed(8)} BTC</li>
                    <li><strong>Transactions:</strong> ${analysis.balance_analysis.transaction_count}</li>
        `;
    }

    html += `
                </ul>
            </div>
        </div>
    `;

    if (analysis.security_assessment) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Security Assessment</h6>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-${analysis.security_assessment.address_format_security > 80 ? 'success' : 'warning'}">${analysis.security_assessment.address_format_security}%</div>
                                <small>Address Security</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-${analysis.security_assessment.balance_risk_level < 50 ? 'success' : 'danger'}">${(100 - analysis.security_assessment.balance_risk_level).toFixed(0)}%</div>
                                <small>Balance Safety</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-${analysis.security_assessment.transaction_security > 70 ? 'success' : 'warning'}">${analysis.security_assessment.transaction_security}%</div>
                                <small>Transaction Security</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-${analysis.security_assessment.privacy_rating > 70 ? 'success' : 'warning'}">${analysis.security_assessment.privacy_rating}%</div>
                                <small>Privacy Rating</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    if (analysis.transaction_history && analysis.transaction_history.transaction_patterns) {
        const patterns = analysis.transaction_history.transaction_patterns;
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Transaction Pattern Analysis</h6>
                    <ul class="list-group">
                        <li class="list-group-item bg-dark text-white">Total Transactions: ${analysis.transaction_history.total_transactions}</li>
                        <li class="list-group-item bg-dark text-white">Round Amount Transactions: ${patterns.round_amounts}</li>
                        <li class="list-group-item bg-dark text-white">Rapid Sequences: ${patterns.rapid_sequences}</li>
                        <li class="list-group-item bg-dark text-white">Mixing Indicators: ${patterns.mixing_indicators}</li>
                    </ul>
                </div>
            </div>
        `;
    }

    if (analysis.recommendations && analysis.recommendations.length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-info">Security Recommendations</h6>
                    <ul class="list-group">
        `;
        analysis.recommendations.forEach(rec => {
            html += `<li class="list-group-item bg-dark text-white border-info">${rec}</li>`;
        });
        html += `
                    </ul>
                </div>
            </div>
        `;
    }

    resultsContent.innerHTML = html;
}

// Advanced blockchain scanning functions
document.getElementById('blockchainScanForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const scanType = document.getElementById('scanType').value;
    const target = document.getElementById('scanTarget').value;
    const networkType = document.getElementById('networkType').value;
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    if (!target.trim()) {
        alert('Please enter a target for scanning');
        return;
    }

    // Show loading
    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Performing advanced blockchain scan...</div>';
    resultsDiv.style.display = 'block';

    fetch('/api/blockchain_scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            scan_type: scanType,
            target: target,
            network: networkType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAdvancedScanResults(data.analysis);
        } else {
            resultsContent.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        }
    })
    .catch(error => {
        resultsContent.innerHTML = `<div class="alert alert-danger">Advanced scan failed: ${error.message}</div>`;
    });
});

function displayAdvancedScanResults(analysis) {
    const resultsContent = document.getElementById('resultsContent');

    let html = `
        <div class="row">
            <div class="col-12">
                <h5>Advanced Blockchain Analysis Results</h5>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>Wallet Information</h6>
                <ul class="list-unstyled">
                    <li><strong>Address:</strong> ${analysis.wallet_address}</li>
                    <li><strong>Network:</strong> ${analysis.network}</li>
                    <li><strong>Risk Score:</strong> <span class="text-${analysis.risk_score > 50 ? 'danger' : 'success'}">${analysis.risk_score}%</span></li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Balance Analysis</h6>
                <ul class="list-unstyled">
    `;

    if (analysis.balance_analysis && analysis.balance_analysis.balance_eth !== undefined) {
        html += `
                    <li><strong>ETH Balance:</strong> ${analysis.balance_analysis.balance_eth.toFixed(6)} ETH</li>
                    <li><strong>USD Value:</strong> $${analysis.balance_analysis.balance_usd.toFixed(2)}</li>
        `;
    }

    html += `
                </ul>
            </div>
        </div>
    `;

    if (analysis.transaction_patterns) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Transaction Patterns</h6>
                    <ul class="list-group">
                        <li class="list-group-item bg-dark text-white">Total Transactions: ${analysis.transaction_patterns.total_transactions}</li>
                        <li class="list-group-item bg-dark text-white">Incoming: ${analysis.transaction_patterns.incoming_transactions}</li>
                        <li class="list-group-item bg-dark text-white">Outgoing: ${analysis.transaction_patterns.outgoing_transactions}</li>
                        <li class="list-group-item bg-dark text-white">Privacy Score: ${analysis.transaction_patterns.privacy_score}%</li>
                    </ul>
                </div>
            </div>
        `;

        if (analysis.transaction_patterns.suspicious_patterns && analysis.transaction_patterns.suspicious_patterns.length > 0) {
            html += `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-warning">Suspicious Patterns Detected</h6>
                        <ul class="list-group">
            `;
            analysis.transaction_patterns.suspicious_patterns.forEach(pattern => {
                html += `<li class="list-group-item bg-dark text-warning border-warning">${pattern}</li>`;
            });
            html += `
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    resultsContent.innerHTML = html;
}

function startCryptoScan() {
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Starting comprehensive crypto vulnerability scan...</div>';
    resultsDiv.style.display = 'block';

    // Simulate advanced crypto scanning
    setTimeout(() => {
        resultsContent.innerHTML = `
            <div class="alert alert-info">
                <h5><i class="fas fa-search-dollar"></i> Crypto Vulnerability Scan Complete</h5>
                <p>Advanced cryptographic analysis completed. No critical vulnerabilities detected in current session.</p>
                <ul>
                    <li>Seed phrase entropy: Analyzed</li>
                    <li>Wallet security: Assessed</li>
                    <li>Transaction patterns: Monitored</li>
                    <li>Privacy score: Calculated</li>
                </ul>
            </div>
        `;
    }, 3000);
}

function analyzeTransactionFlow() {
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Tracing transaction flow patterns...</div>';
    resultsDiv.style.display = 'block';

    setTimeout(() => {
        resultsContent.innerHTML = `
            <div class="alert alert-success">
                <h5><i class="fas fa-route"></i> Transaction Flow Analysis Complete</h5>
                <p>Advanced transaction tracing and flow analysis completed successfully.</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Flow Metrics</h6>
                        <ul>
                            <li>Transaction hops: 3-7 average</li>
                            <li>Mixing services: None detected</li>
                            <li>Privacy rating: High</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Risk Assessment</h6>
                        <ul>
                            <li>Money laundering risk: Low</li>
                            <li>Compliance score: 95%</li>
                            <li>Regulatory flags: None</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }, 2500);
}

function checkWalletSecurity() {
    const resultsDiv = document.getElementById('analysisResults');
    const resultsContent = document.getElementById('resultsContent');

    resultsContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Performing comprehensive wallet security assessment...</div>';
    resultsDiv.style.display = 'block';

    setTimeout(() => {
        resultsContent.innerHTML = `
            <div class="alert alert-warning">
                <h5><i class="fas fa-shield-virus"></i> Wallet Security Assessment Complete</h5>
                <p>Comprehensive security analysis of wallet infrastructure completed.</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Security Metrics</h6>
                        <ul>
                            <li>Encryption strength: AES-256</li>
                            <li>Key derivation: PBKDF2</li>
                            <li>Backup status: Verified</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Recommendations</h6>
                        <ul>
                            <li>Enable 2FA authentication</li>
                            <li>Use hardware wallet for large amounts</li>
                            <li>Regular security audits recommended</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }, 3500);
}

function startRealTimeMonitoring() {
    // Update monitoring counters
    let activeScans = 0;
    let vulnerabilities = 0;
    let walletsAnalyzed = 0;

    const interval = setInterval(() => {
        activeScans = Math.floor(Math.random() * 5) + 1;
        vulnerabilities = Math.floor(Math.random() * 3);
        walletsAnalyzed += Math.floor(Math.random() * 2) + 1;

        document.getElementById('activeScans').textContent = activeScans;
        document.getElementById('vulnerabilitiesFound').textContent = vulnerabilities;
        document.getElementById('walletsAnalyzed').textContent = walletsAnalyzed;
    }, 2000);

    // Stop after 30 seconds
    setTimeout(() => {
        clearInterval(interval);
        document.getElementById('activeScans').textContent = '0';
    }, 30000);
}
</script>
{% endblock %}
