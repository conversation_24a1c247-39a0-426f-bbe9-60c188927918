#!/usr/bin/env python3
"""
Clean startup script for AMADIOHA-M257
Bypasses PowerShell profile issues
"""

import os
import sys

def main():
    """Clean startup without any external dependencies"""
    print("🚀 AMADIOHA-M257 CYBER OPS PLATFORM")
    print("=" * 50)
    
    try:
        # Import the app
        from app import app, db
        
        print("✅ App imported successfully")
        
        # Initialize database
        with app.app_context():
            db.create_all()
            print("✅ Database initialized")
        
        print("\n🌐 Starting server on http://localhost:5000")
        print("📱 Access the unified operations centers:")
        print("   🏠 Dashboard: http://localhost:5000")
        print("   📱 Phone Ops: http://localhost:5000/phone_operations")
        print("   🦠 Malware Ops: http://localhost:5000/malware_operations")
        print("   🎣 Phishing Ops: http://localhost:5000/phishing_operations")
        print("\n🚀 Server starting...")
        
        # Start the server
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=False,
            threaded=True
        )
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
