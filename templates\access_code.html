<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMADIOHA-M257 - Access Control</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: #000000;
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Preloader Styles */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: all 0.8s ease-out;
        }

        .preloader.fade-out {
            opacity: 0;
            transform: scale(1.1);
            pointer-events: none;
        }

        .preloader-logo {
            max-width: 150px;
            width: 70%;
            height: auto;
            filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.2));
            animation: logoFloat 3s ease-in-out infinite;
            margin-bottom: 25px;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .loading-spinner {
            width: 35px;
            height: 35px;
            border: 3px solid rgba(255, 255, 255, 0.2);
            border-top: 3px solid #dc3545;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .preloader-text {
            color: #cccccc;
            font-family: 'Inter', sans-serif;
            font-size: 0.85rem;
            text-align: center;
            opacity: 0.8;
        }

        .access-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .access-card {
            background: #000000;
            border: 2px solid #333333;
            border-radius: 8px;
            padding: 25px;
            max-width: 320px;
            width: 80%;
            box-shadow: 0 10px 25px rgba(255, 255, 255, 0.05);
            position: relative;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .logo {
            max-width: 120px;
            height: auto;
            opacity: 0.9;
            filter: brightness(1.2);
        }

        .access-title {
            color: #ffffff;
            font-family: 'Inter', sans-serif;
            font-size: 1.3rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 6px;
            letter-spacing: 1px;
        }

        .access-subtitle {
            color: #cccccc;
            text-align: center;
            margin-bottom: 20px;
            font-size: 0.85rem;
            font-weight: 400;
        }

        .form-control {
            background: #111111;
            border: 1px solid #333333;
            border-radius: 5px;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
            font-size: 0.95rem;
            padding: 10px 12px;
            text-align: center;
            letter-spacing: 2px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: #111111;
            border-color: #666666;
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.1);
            color: #ffffff;
            outline: none;
        }

        .form-control::placeholder {
            color: #666666;
            letter-spacing: 1px;
        }

        .btn-access {
            background: linear-gradient(45deg, #dc3545, #c82333);
            border: none;
            border-radius: 5px;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 0.95rem;
            padding: 10px 25px;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-access:hover {
            background: linear-gradient(45deg, #c82333, #dc3545);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
            color: #ffffff;
        }

        .btn-access:active {
            transform: translateY(0px);
        }

        .alert {
            border: none;
            border-radius: 5px;
            font-family: 'Inter', sans-serif;
            margin-bottom: 15px;
            font-size: 0.85rem;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
            border: 1px solid #dc3545;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
            border: 1px solid #28a745;
        }

        .security-notice {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid #ffc107;
            border-radius: 5px;
            padding: 10px;
            margin-top: 15px;
            font-size: 0.8rem;
            color: #ffc107;
        }

        .blocked-message {
            text-align: center;
            color: #dc3545;
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            margin-top: 15px;
        }

        .footer-text {
            text-align: center;
            margin-top: 20px;
            color: #666666;
            font-size: 0.7rem;
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader" id="preloader">
        <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}"
             alt="AMADIOHA-M257"
             class="preloader-logo">
        <div class="loading-spinner"></div>
        <div class="preloader-text">Loading Access Control...</div>
    </div>

    <div class="access-container">
        <div class="access-card">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}" alt="AMADIOHA-M257" class="logo">
            </div>
            
            <h1 class="access-title">ACCESS CONTROL</h1>
            <p class="access-subtitle">Enter authorization code to proceed</p>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% if not blocked %}
                <form method="POST" id="accessForm">
                    <div class="mb-4">
                        <input type="password" 
                               class="form-control" 
                               id="access_code" 
                               name="access_code" 
                               placeholder="ENTER ACCESS CODE"
                               maxlength="20"
                               required
                               autocomplete="off">
                    </div>
                    
                    <button type="submit" class="btn btn-access">
                        <i class="fas fa-unlock"></i> AUTHENTICATE
                    </button>
                </form>

                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <strong>Security Notice:</strong> This system is protected by advanced access controls. 
                    Unauthorized access attempts are logged and may result in IP blocking.
                </div>
            {% else %}
                <div class="blocked-message">
                    <i class="fas fa-ban"></i><br>
                    ACCESS BLOCKED<br>
                    <small>Too many failed attempts. Please try again later.</small>
                </div>
            {% endif %}

            <div class="footer-text">
                AMADIOHA-M257 v1.0.0 | Authorized Personnel Only
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Preloader functionality
        window.addEventListener('load', function() {
            const preloader = document.getElementById('preloader');
            setTimeout(function() {
                preloader.classList.add('fade-out');
                setTimeout(function() {
                    preloader.style.display = 'none';

                    // Auto-focus on access code input after preloader
                    const accessCodeInput = document.getElementById('access_code');
                    if (accessCodeInput) {
                        accessCodeInput.focus();
                    }
                }, 800);
            }, 1500);
        });

        // Form submission with loading state
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('accessForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
                    submitBtn.disabled = true;
                });
            }
        });
    </script>
</body>
</html>
