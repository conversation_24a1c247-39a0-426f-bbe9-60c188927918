<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMADIOHA-M257 - Access Control</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #ffffff;
            overflow-x: hidden;
        }

        .access-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .access-card {
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00ff00;
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            box-shadow: 
                0 0 30px rgba(0, 255, 0, 0.3),
                inset 0 0 30px rgba(0, 255, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .access-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ff00, #00cc00, #00ff00);
            border-radius: 15px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            max-width: 200px;
            height: auto;
            filter: drop-shadow(0 0 20px #00ff00);
            animation: logoFloat 4s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .access-title {
            color: #00ff00;
            font-family: 'Roboto Mono', monospace;
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 10px;
            text-shadow: 0 0 10px #00ff00;
            letter-spacing: 2px;
        }

        .access-subtitle {
            color: #cccccc;
            text-align: center;
            margin-bottom: 30px;
            font-size: 1rem;
        }

        .form-control {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #333;
            border-radius: 8px;
            color: #ffffff;
            font-family: 'Roboto Mono', monospace;
            font-size: 1.1rem;
            padding: 15px;
            text-align: center;
            letter-spacing: 3px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: rgba(0, 0, 0, 0.9);
            border-color: #00ff00;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            color: #00ff00;
        }

        .form-control::placeholder {
            color: #666;
            letter-spacing: 1px;
        }

        .btn-access {
            background: linear-gradient(45deg, #00ff00, #00cc00);
            border: none;
            border-radius: 8px;
            color: #000000;
            font-family: 'Roboto Mono', monospace;
            font-weight: 700;
            font-size: 1.1rem;
            padding: 15px 30px;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .btn-access:hover {
            background: linear-gradient(45deg, #00cc00, #00ff00);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 255, 0, 0.4);
            color: #000000;
        }

        .btn-access:active {
            transform: translateY(0px);
        }

        .alert {
            border: none;
            border-radius: 8px;
            font-family: 'Roboto Mono', monospace;
            margin-bottom: 20px;
        }

        .alert-danger {
            background: rgba(255, 0, 0, 0.2);
            color: #ff6b6b;
            border: 1px solid #ff6b6b;
        }

        .alert-success {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
            border: 1px solid #00ff00;
        }

        .security-notice {
            background: rgba(255, 165, 0, 0.1);
            border: 1px solid #ffa500;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #ffa500;
        }

        .blocked-message {
            text-align: center;
            color: #ff6b6b;
            font-family: 'Roboto Mono', monospace;
            font-size: 1.2rem;
            margin-top: 20px;
        }

        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #00ff00;
            border-radius: 50%;
            animation: particleFloat 10s linear infinite;
            opacity: 0.6;
        }

        @keyframes particleFloat {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 0.6;
            }
            90% {
                opacity: 0.6;
            }
            100% {
                transform: translateY(-100px) translateX(50px);
                opacity: 0;
            }
        }

        .footer-text {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 0.8rem;
            font-family: 'Roboto Mono', monospace;
        }
    </style>
</head>
<body>
    <!-- Particle Background -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 7s;"></div>
    </div>

    <div class="access-container">
        <div class="access-card">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}" alt="AMADIOHA-M257" class="logo">
            </div>
            
            <h1 class="access-title">ACCESS CONTROL</h1>
            <p class="access-subtitle">Enter authorization code to proceed</p>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% if not blocked %}
                <form method="POST" id="accessForm">
                    <div class="mb-4">
                        <input type="password" 
                               class="form-control" 
                               id="access_code" 
                               name="access_code" 
                               placeholder="ENTER ACCESS CODE"
                               maxlength="20"
                               required
                               autocomplete="off">
                    </div>
                    
                    <button type="submit" class="btn btn-access">
                        <i class="fas fa-unlock"></i> AUTHENTICATE
                    </button>
                </form>

                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <strong>Security Notice:</strong> This system is protected by advanced access controls. 
                    Unauthorized access attempts are logged and may result in IP blocking.
                </div>
            {% else %}
                <div class="blocked-message">
                    <i class="fas fa-ban"></i><br>
                    ACCESS BLOCKED<br>
                    <small>Too many failed attempts. Please try again later.</small>
                </div>
            {% endif %}

            <div class="footer-text">
                AMADIOHA-M257 v1.0.0 | Authorized Personnel Only
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-focus on access code input
        document.addEventListener('DOMContentLoaded', function() {
            const accessCodeInput = document.getElementById('access_code');
            if (accessCodeInput) {
                accessCodeInput.focus();
            }
        });

        // Form submission with loading state
        document.getElementById('accessForm')?.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AUTHENTICATING...';
            submitBtn.disabled = true;
        });

        // Add typing effect to placeholder
        const input = document.getElementById('access_code');
        if (input) {
            input.addEventListener('input', function() {
                this.style.color = this.value ? '#00ff00' : '#ffffff';
            });
        }
    </script>
</body>
</html>
