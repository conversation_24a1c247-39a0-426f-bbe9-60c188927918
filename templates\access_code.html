<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMADIOHA-M257 - Access Control</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Preloader Styles */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: all 0.8s ease-out;
        }

        .preloader.fade-out {
            opacity: 0;
            transform: scale(1.1);
            pointer-events: none;
        }

        .preloader-logo {
            max-width: 200px;
            width: 80%;
            height: auto;
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3));
            animation: logoFloat 3s ease-in-out infinite;
            margin-bottom: 30px;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .preloader-text {
            color: #ffffff;
            font-family: 'Roboto Mono', monospace;
            font-size: 0.9rem;
            text-align: center;
            opacity: 0.8;
        }

        .access-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .access-card {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 30px;
            max-width: 400px;
            width: 85%;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            position: relative;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 25px;
        }

        .logo {
            max-width: 150px;
            height: auto;
            opacity: 0.9;
        }

        .access-title {
            color: #2c3e50;
            font-family: 'Inter', sans-serif;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }

        .access-subtitle {
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 25px;
            font-size: 0.9rem;
            font-weight: 400;
        }

        .form-control {
            background: #ffffff;
            border: 1px solid #ddd;
            border-radius: 5px;
            color: #2c3e50;
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            padding: 12px 15px;
            text-align: center;
            letter-spacing: 2px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            background: #ffffff;
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
            color: #2c3e50;
            outline: none;
        }

        .form-control::placeholder {
            color: #95a5a6;
            letter-spacing: 1px;
        }

        .btn-access {
            background: linear-gradient(45deg, #3498db, #2980b9);
            border: none;
            border-radius: 5px;
            color: #ffffff;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 1rem;
            padding: 12px 30px;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-access:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            color: #ffffff;
        }

        .btn-access:active {
            transform: translateY(0px);
        }

        .alert {
            border: none;
            border-radius: 5px;
            font-family: 'Inter', sans-serif;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .security-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 12px;
            margin-top: 20px;
            font-size: 0.85rem;
            color: #856404;
        }

        .blocked-message {
            text-align: center;
            color: #e74c3c;
            font-family: 'Inter', sans-serif;
            font-size: 1.1rem;
            margin-top: 20px;
        }

        .footer-text {
            text-align: center;
            margin-top: 25px;
            color: #95a5a6;
            font-size: 0.75rem;
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader" id="preloader">
        <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}"
             alt="AMADIOHA-M257"
             class="preloader-logo">
        <div class="loading-spinner"></div>
        <div class="preloader-text">Loading Access Control...</div>
    </div>

    <div class="access-container">
        <div class="access-card">
            <div class="logo-container">
                <img src="{{ url_for('static', filename='images/amadiohalogo.svg') }}" alt="AMADIOHA-M257" class="logo">
            </div>
            
            <h1 class="access-title">ACCESS CONTROL</h1>
            <p class="access-subtitle">Enter authorization code to proceed</p>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }}">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% if not blocked %}
                <form method="POST" id="accessForm">
                    <div class="mb-4">
                        <input type="password" 
                               class="form-control" 
                               id="access_code" 
                               name="access_code" 
                               placeholder="ENTER ACCESS CODE"
                               maxlength="20"
                               required
                               autocomplete="off">
                    </div>
                    
                    <button type="submit" class="btn btn-access">
                        <i class="fas fa-unlock"></i> AUTHENTICATE
                    </button>
                </form>

                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <strong>Security Notice:</strong> This system is protected by advanced access controls. 
                    Unauthorized access attempts are logged and may result in IP blocking.
                </div>
            {% else %}
                <div class="blocked-message">
                    <i class="fas fa-ban"></i><br>
                    ACCESS BLOCKED<br>
                    <small>Too many failed attempts. Please try again later.</small>
                </div>
            {% endif %}

            <div class="footer-text">
                AMADIOHA-M257 v1.0.0 | Authorized Personnel Only
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Preloader functionality
        window.addEventListener('load', function() {
            const preloader = document.getElementById('preloader');
            setTimeout(function() {
                preloader.classList.add('fade-out');
                setTimeout(function() {
                    preloader.style.display = 'none';

                    // Auto-focus on access code input after preloader
                    const accessCodeInput = document.getElementById('access_code');
                    if (accessCodeInput) {
                        accessCodeInput.focus();
                    }
                }, 800);
            }, 1500);
        });

        // Form submission with loading state
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('accessForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Authenticating...';
                    submitBtn.disabled = true;
                });
            }
        });
    </script>
</body>
</html>
