{% extends "base.html" %}

{% block title %}AI Chat - AMADIOHA-M257{% endblock %}

{% block content %}
<style>
.chat-container {
    background: var(--dark-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    height: 70vh;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 15px 20px;
    border-radius: 6px 6px 0 0;
    font-weight: bold;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--card-bg);
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: var(--primary-color);
    color: white;
}

.message.ai .message-avatar {
    background: var(--terminal-green);
    color: black;
}

.message-content {
    background: var(--dark-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 12px 16px;
    max-width: 70%;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message.user .message-content {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.message.ai .message-content {
    background: var(--card-bg);
    color: var(--text-primary);
}

.chat-input-container {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: var(--dark-bg);
    border-radius: 0 0 6px 6px;
}

.chat-input-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.chat-input {
    flex: 1;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 25px;
    padding: 12px 20px;
    outline: none;
    font-size: 14px;
}

.chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(124,58,237,0.15);
}

.chat-send-btn {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border: none;
    color: white;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chat-send-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(124,58,237,0.3);
}

.typing-indicator {
    display: none;
    color: var(--text-secondary);
    font-style: italic;
    padding: 10px 0;
}

.quick-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.quick-action-btn {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-robot"></i> AI Assistant</h2>
            <p class="text-muted">AMADIOHA-M257 Intelligent Security Assistant</p>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="chat-container">
                <div class="chat-header">
                    <i class="fas fa-robot"></i> AMADIOHA-M257 AI Assistant v1.0.0
                    <span class="float-end">
                        <i class="fas fa-circle text-success" style="font-size: 8px;"></i> Online
                    </span>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="message ai">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            Welcome to AMADIOHA-M257 AI Assistant!

I'm here to help you with cybersecurity operations, threat analysis, and system management.

How can I assist you today?
                        </div>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-robot"></i> AI is typing...
                </div>

                <div class="chat-input-container">
                    <div class="quick-actions">
                        <button class="quick-action-btn" onclick="sendQuickMessage('System status')">System Status</button>
                        <button class="quick-action-btn" onclick="sendQuickMessage('Security scan')">Security Scan</button>
                        <button class="quick-action-btn" onclick="sendQuickMessage('Threat analysis')">Threat Analysis</button>
                        <button class="quick-action-btn" onclick="sendQuickMessage('Help')">Help</button>
                    </div>
                    <div class="chat-input-group">
                        <input type="text" class="chat-input" id="chatInput" placeholder="Type your message..." autocomplete="off">
                        <button class="chat-send-btn" id="sendBtn" onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> AI Capabilities</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="fas fa-shield-alt text-success"></i> Security Analysis</li>
                        <li><i class="fas fa-search text-info"></i> Threat Intelligence</li>
                        <li><i class="fas fa-chart-line text-warning"></i> Campaign Management</li>
                        <li><i class="fas fa-monitor-heart-rate text-danger"></i> System Monitoring</li>
                        <li><i class="fas fa-bug text-primary"></i> Vulnerability Assessment</li>
                        <li><i class="fas fa-brain text-purple"></i> Intelligent Recommendations</li>
                    </ul>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-history"></i> Recent Topics</h6>
                </div>
                <div class="card-body">
                    <div id="recentTopics" class="text-muted">
                        No recent conversations
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let messageHistory = [];

document.getElementById('chatInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        sendMessage();
    }
});

function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessage(message, 'user');
    
    // Clear input
    input.value = '';
    
    // Show typing indicator
    showTyping();
    
    // Send to server
    fetch('/chat/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        hideTyping();
        addMessage(data.response, 'ai');
        updateRecentTopics(message);
    })
    .catch(error => {
        hideTyping();
        addMessage('Sorry, I encountered an error. Please try again.', 'ai');
    });
}

function sendQuickMessage(message) {
    document.getElementById('chatInput').value = message;
    sendMessage();
}

function addMessage(content, sender) {
    const messagesContainer = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    messageDiv.innerHTML = `
        <div class="message-avatar">
            ${avatar}
        </div>
        <div class="message-content">
            ${content}
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    messageHistory.push({ content, sender, timestamp: new Date() });
}

function showTyping() {
    document.getElementById('typingIndicator').style.display = 'block';
}

function hideTyping() {
    document.getElementById('typingIndicator').style.display = 'none';
}

function updateRecentTopics(topic) {
    const recentTopicsDiv = document.getElementById('recentTopics');
    const topics = JSON.parse(localStorage.getItem('recentTopics') || '[]');
    
    if (!topics.includes(topic)) {
        topics.unshift(topic);
        topics.splice(5); // Keep only 5 recent topics
        localStorage.setItem('recentTopics', JSON.stringify(topics));
    }
    
    if (topics.length > 0) {
        recentTopicsDiv.innerHTML = topics.map(t => `<div class="mb-1">${t}</div>`).join('');
    }
}

// Load recent topics on page load
document.addEventListener('DOMContentLoaded', function() {
    const topics = JSON.parse(localStorage.getItem('recentTopics') || '[]');
    if (topics.length > 0) {
        document.getElementById('recentTopics').innerHTML = topics.map(t => `<div class="mb-1">${t}</div>`).join('');
    }
});

// Focus input on page load
document.getElementById('chatInput').focus();
</script>
{% endblock %}
