# 🚀 CYBER-OPS PLATFORM DEPLOYMENT GUIDE

## ⚠️ **CRITICAL SECURITY WARNING**

**THIS PLATFORM CONTAINS REAL FUNCTIONAL MALWARE AND PHISHING TOOLS**

- **FOR AUTHORIZED PENETRATION TESTING ONLY**
- **REQUIRES EXPLICIT WRITTEN AUTHORIZATION**
- **MUST BE DEPLOYED IN ISOLATED ENVIRONMENTS**
- **VIOLATING THESE GUIDELINES MAY RESULT IN LEGAL CONSEQUENCES**

---

## 📋 **PRE-DEPLOYMENT REQUIREMENTS**

### 🔐 **Legal & Authorization Requirements**
- [ ] **Written Authorization** from target organization
- [ ] **Penetration Testing Agreement** signed
- [ ] **Scope of Work** clearly defined
- [ ] **Legal Compliance** verified (local/international laws)
- [ ] **Insurance Coverage** for cybersecurity testing
- [ ] **Incident Response Plan** prepared

### 🛡️ **Security Requirements**
- [ ] **Isolated Network Environment** (air-gapped preferred)
- [ ] **Dedicated Testing Infrastructure**
- [ ] **Secure Communication Channels**
- [ ] **Data Encryption** for all stored information
- [ ] **Access Control** and authentication systems
- [ ] **Audit Logging** enabled

### 💻 **Technical Requirements**
- [ ] **Python 3.11+** installed
- [ ] **Administrative Privileges** on deployment system
- [ ] **Network Access** for C2 communication
- [ ] **SSL Certificates** for HTTPS (recommended)
- [ ] **Database System** (SQLite/PostgreSQL/MySQL)
- [ ] **Email Server Access** for phishing campaigns

---

## 🏗️ **DEPLOYMENT ARCHITECTURE**

### 🎯 **Recommended Infrastructure**

```
┌─────────────────────────────────────────────────────────────┐
│                    CYBER-OPS DEPLOYMENT                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   COMMAND &     │    │   PHISHING      │                │
│  │   CONTROL       │◄──►│   SERVER        │                │
│  │   SERVER        │    │   (HTTPS)       │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   MALWARE       │    │   CREDENTIAL    │                │
│  │   DEPLOYMENT    │    │   HARVESTING    │                │
│  │   SYSTEM        │    │   DATABASE      │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   TELEGRAM      │    │   NGROK         │                │
│  │   NOTIFICATIONS │    │   TUNNELING     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

---

## 🚀 **STEP-BY-STEP DEPLOYMENT**

### **Step 1: Environment Preparation**

#### 1.1 Create Isolated Environment
```bash
# Create dedicated user for CYBER-OPS
sudo useradd -m -s /bin/bash cyberops
sudo usermod -aG sudo cyberops

# Create secure directory structure
sudo mkdir -p /opt/cyber-ops/{logs,data,malware,credentials}
sudo chown -R cyberops:cyberops /opt/cyber-ops
sudo chmod 750 /opt/cyber-ops
```

#### 1.2 Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and required packages
sudo apt install python3.11 python3.11-pip python3.11-venv -y

# Install additional tools
sudo apt install git nginx certbot sqlite3 -y
```

### **Step 2: Platform Installation**

#### 2.1 Clone and Setup
```bash
# Switch to cyberops user
sudo su - cyberops

# Clone the platform
git clone <repository-url> /opt/cyber-ops/platform
cd /opt/cyber-ops/platform

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install requirements
pip install -r requirements.txt
```

#### 2.2 Configuration
```bash
# Copy configuration template
cp config.example.py config.py

# Edit configuration (see Configuration section below)
nano config.py
```

### **Step 3: Security Configuration**

#### 3.1 SSL/TLS Setup
```bash
# Generate SSL certificates
sudo certbot certonly --standalone -d your-domain.com

# Configure Nginx reverse proxy
sudo nano /etc/nginx/sites-available/cyber-ops
```

#### 3.2 Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 5000/tcp  # CYBER-OPS (internal only)
```

### **Step 4: Database Setup**

#### 4.1 Initialize Database
```bash
# Initialize SQLite database
python app.py --init-db

# Or setup PostgreSQL (recommended for production)
sudo apt install postgresql postgresql-contrib
sudo -u postgres createdb cyberops
sudo -u postgres createuser cyberops
```

### **Step 5: Service Configuration**

#### 5.1 Create Systemd Service
```bash
sudo nano /etc/systemd/system/cyber-ops.service
```

```ini
[Unit]
Description=CYBER-OPS Platform
After=network.target

[Service]
Type=simple
User=cyberops
WorkingDirectory=/opt/cyber-ops/platform
Environment=PATH=/opt/cyber-ops/platform/venv/bin
ExecStart=/opt/cyber-ops/platform/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 5.2 Enable and Start Service
```bash
sudo systemctl daemon-reload
sudo systemctl enable cyber-ops
sudo systemctl start cyber-ops
```

---

## ⚙️ **CONFIGURATION**

### 🔧 **Core Configuration (config.py)**

```python
# Security Configuration
SECRET_KEY = 'your-super-secret-key-here'
SECURITY_PASSWORD_SALT = 'your-password-salt'

# Database Configuration
DATABASE_URL = 'sqlite:///cyber-ops.db'
# Or PostgreSQL: 'postgresql://user:pass@localhost/cyberops'

# Email Configuration (for phishing campaigns)
SMTP_SERVER = 'smtp.gmail.com'
SMTP_PORT = 587
SMTP_USE_TLS = True

# Telegram Configuration
TELEGRAM_BOT_TOKEN = 'your-bot-token'
TELEGRAM_CHAT_ID = 'your-chat-id'

# Ngrok Configuration
NGROK_AUTH_TOKEN = 'your-ngrok-token'

# Security Settings
ENABLE_RATE_LIMITING = True
MAX_LOGIN_ATTEMPTS = 5
SESSION_TIMEOUT = 3600  # 1 hour

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FILE = '/opt/cyber-ops/logs/cyber-ops.log'
```

### 🛡️ **Security Hardening**

#### Enable Additional Security Features
```python
# Additional security headers
SECURITY_HEADERS = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
}

# IP Whitelisting (recommended)
ALLOWED_IPS = [
    '***********/24',  # Internal network
    '10.0.0.0/8',      # Private network
]

# Audit Logging
AUDIT_LOG_ENABLED = True
AUDIT_LOG_FILE = '/opt/cyber-ops/logs/audit.log'
```

---

## 🎯 **OPERATIONAL DEPLOYMENT**

### **Phase 1: Initial Setup and Testing**

#### 1.1 Platform Verification
```bash
# Test platform startup
sudo systemctl status cyber-ops

# Check logs
tail -f /opt/cyber-ops/logs/cyber-ops.log

# Verify web interface
curl -k https://localhost:5000/login
```

#### 1.2 Create Admin Account
```bash
# Access the platform
https://your-domain.com/login

# Default credentials (CHANGE IMMEDIATELY):
Username: admin
Password: admin123
```

#### 1.3 Initial Configuration
1. **Change default admin password**
2. **Configure SMTP settings**
3. **Setup Telegram bot**
4. **Test Ngrok integration**
5. **Verify database connectivity**

### **Phase 2: Malware Deployment**

#### 2.1 Keylogger Deployment
1. Navigate to **Real Malware Deployment**
2. Configure target OS and persistence options
3. Generate keylogger payload
4. Deploy to target systems (with authorization)
5. Monitor active sessions

#### 2.2 Credential Stealer Deployment
1. Select target browsers
2. Configure exfiltration URL
3. Generate stealer payload
4. Deploy and monitor results

### **Phase 3: Phishing Campaign**

#### 3.1 Email Campaign Setup
1. Configure SMTP credentials
2. Create target email list
3. Generate PDF attachments (if needed)
4. Launch phishing campaign
5. Monitor credential harvesting

#### 3.2 Perfect Login Replicas
1. Identify target email providers
2. Generate perfect replicas
3. Setup credential harvesting
4. Monitor access attempts

---

## 📊 **MONITORING & MAINTENANCE**

### 🔍 **System Monitoring**

#### Real-time Monitoring
```bash
# Monitor system resources
htop

# Check network connections
netstat -tulpn | grep :5000

# Monitor logs
tail -f /opt/cyber-ops/logs/*.log
```

#### Performance Metrics
- **CPU Usage**: < 80%
- **Memory Usage**: < 4GB
- **Disk Space**: > 10GB free
- **Network Latency**: < 100ms

### 🛠️ **Maintenance Tasks**

#### Daily Tasks
- [ ] Check system logs
- [ ] Monitor active sessions
- [ ] Verify credential harvesting
- [ ] Update threat intelligence

#### Weekly Tasks
- [ ] Database backup
- [ ] Security updates
- [ ] Performance optimization
- [ ] Audit log review

#### Monthly Tasks
- [ ] Full system backup
- [ ] Security assessment
- [ ] Configuration review
- [ ] Documentation update

---

## 🚨 **INCIDENT RESPONSE**

### **Security Incident Procedures**

#### 1. Detection
- Monitor for unauthorized access
- Check for unusual network activity
- Review audit logs regularly

#### 2. Response
- Isolate affected systems
- Preserve evidence
- Notify stakeholders
- Document incident

#### 3. Recovery
- Restore from backups
- Update security measures
- Conduct post-incident review

---

## 📚 **BEST PRACTICES**

### 🔐 **Security Best Practices**

1. **Principle of Least Privilege**
   - Grant minimal necessary access
   - Regular access reviews
   - Role-based permissions

2. **Defense in Depth**
   - Multiple security layers
   - Network segmentation
   - Endpoint protection

3. **Continuous Monitoring**
   - Real-time alerting
   - Log analysis
   - Threat hunting

### 🎯 **Operational Best Practices**

1. **Documentation**
   - Maintain detailed records
   - Document all procedures
   - Keep configuration current

2. **Testing**
   - Regular functionality tests
   - Security assessments
   - Disaster recovery drills

3. **Training**
   - Staff security awareness
   - Platform operation training
   - Incident response procedures

---

## ⚠️ **LEGAL & COMPLIANCE**

### 📋 **Legal Requirements**

1. **Authorization Documentation**
   - Written permission required
   - Scope clearly defined
   - Legal review completed

2. **Data Protection**
   - Encrypt all sensitive data
   - Secure data transmission
   - Proper data disposal

3. **Compliance Standards**
   - Follow industry regulations
   - Maintain audit trails
   - Regular compliance reviews

### 🛡️ **Ethical Guidelines**

1. **Responsible Disclosure**
   - Report vulnerabilities promptly
   - Follow disclosure timelines
   - Protect sensitive information

2. **Professional Conduct**
   - Maintain confidentiality
   - Respect privacy rights
   - Follow ethical standards

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### 🔧 **Common Issues**

#### Platform Won't Start
```bash
# Check service status
sudo systemctl status cyber-ops

# Check logs
journalctl -u cyber-ops -f

# Verify configuration
python -c "import config; print('Config OK')"
```

#### Database Connection Issues
```bash
# Test database connectivity
python -c "from app import db; db.create_all(); print('DB OK')"

# Check database permissions
ls -la cyber-ops.db
```

#### Network Connectivity Problems
```bash
# Test network connectivity
curl -I https://api.telegram.org

# Check firewall rules
sudo ufw status verbose

# Verify DNS resolution
nslookup your-domain.com
```

### 📧 **Getting Help**

For technical support and questions:
- Review documentation thoroughly
- Check system logs for errors
- Verify configuration settings
- Test in isolated environment first

---

## 🎯 **DEPLOYMENT CHECKLIST**

### Pre-Deployment
- [ ] Legal authorization obtained
- [ ] Infrastructure prepared
- [ ] Security measures implemented
- [ ] Team training completed

### Deployment
- [ ] Platform installed and configured
- [ ] Security hardening applied
- [ ] Monitoring systems active
- [ ] Backup procedures tested

### Post-Deployment
- [ ] Functionality verified
- [ ] Security assessment completed
- [ ] Documentation updated
- [ ] Team briefed on operations

---

**🚀 CYBER-OPS PLATFORM IS NOW READY FOR AUTHORIZED PENETRATION TESTING!**

Remember: **With great power comes great responsibility.** Use these capabilities ethically and legally.
