# AMADIOHA-M257 Private Deployment Guide

## 🔐 **SECURE PRIVATE VIRTUAL SERVER DEPLOYMENT**

### **Prerequisites**
- Private Virtual Server (VPS) with Ubuntu 20.04+ or CentOS 8+
- Minimum 4GB RAM, 2 CPU cores, 50GB storage
- Root access to the server
- Domain name for Cloudflare protection
- Cloudflare account with Pro plan (recommended)

---

## 🚀 **STEP 1: VPS SERVER SETUP**

### **1.1 Initial Server Configuration**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv nginx ufw fail2ban

# Create dedicated user
sudo adduser amadioha
sudo usermod -aG sudo amadioha
su - amadioha
```

### **1.2 Security Hardening**
```bash
# Configure firewall
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Configure fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

---

## 🌐 **STEP 2: CLOUDFLARE PROTECTION SETUP**

### **2.1 Domain Configuration**
1. **Add Domain to Cloudflare:**
   - Login to Cloudflare dashboard
   - Add your domain (e.g., `your-domain.com`)
   - Update nameservers at your domain registrar

2. **DNS Configuration:**
   ```
   Type: A
   Name: amadioha (or subdomain of choice)
   Content: YOUR_VPS_IP_ADDRESS
   Proxy Status: Proxied (Orange Cloud)
   ```

### **2.2 Cloudflare Security Settings**
```bash
# SSL/TLS Settings
- SSL/TLS encryption mode: Full (strict)
- Always Use HTTPS: ON
- Minimum TLS Version: 1.2

# Security Settings
- Security Level: High
- Bot Fight Mode: ON
- Browser Integrity Check: ON

# Firewall Rules (Create these rules)
Rule 1: Block all countries except authorized ones
Rule 2: Rate limiting - 10 requests per minute per IP
Rule 3: Challenge suspicious requests
```

### **2.3 Access Restrictions**
```bash
# Cloudflare Access (Zero Trust)
1. Go to Zero Trust > Access > Applications
2. Create new application:
   - Application name: AMADIOHA-M257
   - Subdomain: amadioha
   - Domain: your-domain.com
   - Path: /*

3. Create Access Policy:
   - Policy name: Authorized Users Only
   - Action: Allow
   - Include: Email addresses of authorized users
```

---

## 🔧 **STEP 3: APPLICATION DEPLOYMENT**

### **3.1 Application Setup**
```bash
# Clone/upload application files
cd /home/<USER>
mkdir amadioha-m257
cd amadioha-m257

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set environment variables
cat > .env << EOF
FLASK_ENV=production
SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
ACCESS_CODE=SHYJAYOBIBI
DATABASE_URL=sqlite:///amadioha.db
CLOUDFLARE_ZONE_ID=your_zone_id
CLOUDFLARE_API_TOKEN=your_api_token
EOF
```

### **3.2 Database Setup**
```bash
# Initialize database
python3 -c "
from app import app, db
with app.app_context():
    db.create_all()
    print('Database initialized')
"
```

### **3.3 Systemd Service**
```bash
# Create service file
sudo tee /etc/systemd/system/amadioha.service << EOF
[Unit]
Description=AMADIOHA-M257 Security Platform
After=network.target

[Service]
Type=simple
User=amadioha
WorkingDirectory=/home/<USER>/amadioha-m257
Environment=PATH=/home/<USER>/amadioha-m257/venv/bin
ExecStart=/home/<USER>/amadioha-m257/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable amadioha
sudo systemctl start amadioha
```

---

## 🔒 **STEP 4: NGINX REVERSE PROXY**

### **4.1 Nginx Configuration**
```bash
# Create nginx config
sudo tee /etc/nginx/sites-available/amadioha << EOF
server {
    listen 80;
    server_name amadioha.your-domain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name amadioha.your-domain.com;

    # SSL Configuration (Cloudflare Origin Certificate)
    ssl_certificate /etc/ssl/certs/cloudflare.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # Rate Limiting
    limit_req_zone \$binary_remote_addr zone=amadioha:10m rate=10r/m;
    limit_req zone=amadioha burst=5 nodelay;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/amadioha /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🛡️ **STEP 5: ADVANCED SECURITY MEASURES**

### **5.1 Intrusion Detection**
```bash
# Install and configure OSSEC
wget https://github.com/ossec/ossec-hids/archive/3.7.0.tar.gz
tar -xzf 3.7.0.tar.gz
cd ossec-hids-3.7.0
sudo ./install.sh

# Configure monitoring
sudo tee -a /var/ossec/etc/ossec.conf << EOF
<localfile>
    <log_format>apache</log_format>
    <location>/var/log/nginx/access.log</location>
</localfile>
EOF
```

### **5.2 Log Monitoring**
```bash
# Setup log rotation
sudo tee /etc/logrotate.d/amadioha << EOF
/home/<USER>/amadioha-m257/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 amadioha amadioha
}
EOF
```

---

## 🔐 **STEP 6: ACCESS CODE IMPLEMENTATION**

The application now requires the access code **SHYJAYOBIBI** before any tools can be used:

### **6.1 Access Code Features:**
- Required on first login
- Session-based authentication
- Automatic logout after inactivity
- Failed attempt logging
- IP-based restrictions

### **6.2 Usage:**
1. Navigate to your domain: `https://amadioha.your-domain.com`
2. Enter access code: `SHYJAYOBIBI`
3. Login with admin credentials
4. Access all security tools

---

## 📊 **STEP 7: MONITORING & MAINTENANCE**

### **7.1 System Monitoring**
```bash
# Check service status
sudo systemctl status amadioha
sudo systemctl status nginx

# View logs
sudo journalctl -u amadioha -f
tail -f /var/log/nginx/access.log
```

### **7.2 Backup Strategy**
```bash
# Create backup script
cat > /home/<USER>/backup.sh << EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
tar -czf /home/<USER>/backups/amadioha_\$DATE.tar.gz \\
    /home/<USER>/amadioha-m257 \\
    /etc/nginx/sites-available/amadioha
EOF

chmod +x /home/<USER>/backup.sh

# Schedule daily backups
echo "0 2 * * * /home/<USER>/backup.sh" | crontab -
```

---

## ⚠️ **SECURITY WARNINGS**

1. **Never expose the server IP directly**
2. **Always use Cloudflare proxy**
3. **Regularly update access codes**
4. **Monitor access logs daily**
5. **Use strong SSL certificates**
6. **Implement IP whitelisting when possible**

---

## 🎯 **DEPLOYMENT CHECKLIST**

- [ ] VPS configured and hardened
- [ ] Cloudflare protection enabled
- [ ] Domain properly configured
- [ ] Application deployed and running
- [ ] Nginx reverse proxy configured
- [ ] SSL certificates installed
- [ ] Access code system active
- [ ] Monitoring systems operational
- [ ] Backup strategy implemented
- [ ] Security measures verified

**🛡️ Your AMADIOHA-M257 platform is now securely deployed and protected!**
