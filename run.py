#!/usr/bin/env python3
"""
AMADIOHA-M257 Cyber Ops Platform Startup Script
"""

import os
import sys

def main():
    """Main startup function"""
    print("🚀 AMADIOHA-M257 CYBER OPS PLATFORM")
    print("=" * 50)
    print("🎯 Unified Operations Centers")
    print("📱 Phone Operations | 🦠 Malware Operations | 🎣 Phishing Operations")
    print("=" * 50)

    try:
        from app import app, db

        # Check if database exists, if not create it
        with app.app_context():
            db.create_all()
            print("✅ Database initialized")

        # Start the application
        print("🌐 Server starting on http://localhost:5000")
        print("📝 Access the platform:")
        print("   🏠 Dashboard: http://localhost:5000")
        print("   📱 Phone Ops: http://localhost:5000/phone_operations")
        print("   🦠 Malware Ops: http://localhost:5000/malware_operations")
        print("   🎣 Phishing Ops: http://localhost:5000/phishing_operations")
        print("\n⚠️  AUTHORIZED TESTING ONLY!")
        print("🔒 This platform is for cybersecurity professionals.")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5000)

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Install dependencies: pip install -r requirements_minimal.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Startup error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
