# 🚀 AMADIOHA-M257 SERVER STARTUP GUIDE

## 📋 **QUICK START**

### **Option 1: Automatic Startup (Recommended)**
```bash
# Run the batch file (Windows)
start_server.bat

# OR run PowerShell script
powershell -ExecutionPolicy Bypass -File start_server.ps1
```

### **Option 2: Manual Startup**
```bash
# 1. Install Python (if not installed)
# Download from: https://www.python.org/downloads/

# 2. Create virtual environment
python -m venv venv

# 3. Activate virtual environment
venv\Scripts\activate

# 4. Install requirements
pip install -r requirements.txt

# 5. Start server
python run.py
```

---

## 🐍 **PYTHON INSTALLATION**

### **Method 1: Official Python Website**
1. Go to: https://www.python.org/downloads/
2. Download Python 3.9+ for Windows
3. **IMPORTANT**: Check "Add Python to PATH" during installation
4. Verify installation: `python --version`

### **Method 2: Microsoft Store (Recommended)**
1. Open Microsoft Store
2. Search for "Python 3.11" or "Python 3.12"
3. Click "Install"
4. Verify installation: `python --version`

### **Method 3: Chocolatey (Advanced)**
```powershell
# Install Chocolatey first, then:
choco install python
```

---

## 🔧 **SYSTEM REQUIREMENTS**

### **Minimum Requirements**
- **OS**: Windows 10/11
- **Python**: 3.9 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **Network**: Internet connection for API calls

### **Required Python Packages**
```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
twilio==8.10.0
phonenumbers==8.13.22
requests==2.31.0
beautifulsoup4==4.12.2
python-dotenv==1.0.0
cryptography==41.0.7
```

---

## 🌐 **SERVER CONFIGURATION**

### **Default Server Settings**
- **Host**: localhost (127.0.0.1)
- **Port**: 5000
- **Debug Mode**: Enabled
- **Database**: SQLite (auto-created)

### **Access URLs**
```
🏠 Main Dashboard: http://localhost:5000
📱 Phone Operations: http://localhost:5000/phone_operations
🦠 Malware Operations: http://localhost:5000/malware_operations
🎣 Phishing Operations: http://localhost:5000/phishing_operations
🔐 Login: http://localhost:5000/login
```

---

## 🔑 **FIRST TIME SETUP**

### **1. Environment Configuration**
The `.env` file is already configured with:
```
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=58e7e9a407cd2b9e800efdb8c0705049
TWILIO_PHONE_NUMBER=+***********
NGROK_SUBDOMAIN=kodiak-pleasing-hornet.ngrok-free.app
SECRET_KEY=your-secret-key-here
```

### **2. Database Initialization**
The database will be automatically created on first run:
- **Location**: `instance/cybersecurity_platform.db`
- **Type**: SQLite
- **Auto-migration**: Enabled

### **3. Default Admin Account**
Create admin account on first login:
- **Username**: admin
- **Password**: (set during first run)

---

## 🚀 **STARTING THE SERVER**

### **Step-by-Step Process**

#### **1. Open Command Prompt/PowerShell**
```bash
# Navigate to project directory
cd c:\Users\<USER>\phishing-awareness-platform
```

#### **2. Run Startup Script**
```bash
# Option A: Batch file
start_server.bat

# Option B: PowerShell script
powershell -ExecutionPolicy Bypass -File start_server.ps1

# Option C: Manual
python run.py
```

#### **3. Wait for Server Start**
You should see:
```
🚀 AMADIOHA-M257 CYBER OPS PLATFORM
====================================
 * Running on http://127.0.0.1:5000
 * Debug mode: on
 * Restarting with stat
 * Debugger is active!
```

#### **4. Access the Platform**
Open browser and go to: http://localhost:5000

---

## 🔍 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **❌ "Python is not recognized"**
**Solution**: 
1. Reinstall Python with "Add to PATH" checked
2. OR manually add Python to PATH
3. Restart command prompt

#### **❌ "pip is not recognized"**
**Solution**:
```bash
# Reinstall pip
python -m ensurepip --upgrade
```

#### **❌ "Permission denied"**
**Solution**:
```bash
# Run as administrator
# OR use virtual environment
python -m venv venv
venv\Scripts\activate
```

#### **❌ "Port 5000 already in use"**
**Solution**:
```bash
# Kill process using port 5000
netstat -ano | findstr :5000
taskkill /PID <process_id> /F

# OR change port in run.py
app.run(host='0.0.0.0', port=5001, debug=True)
```

#### **❌ "Module not found"**
**Solution**:
```bash
# Install missing modules
pip install -r requirements.txt

# OR install individually
pip install flask twilio phonenumbers
```

---

## 🌐 **NGROK TUNNEL (OPTIONAL)**

### **For External Access**
If you need external access (for Twilio webhooks):

#### **1. Install Ngrok**
```bash
# Download from: https://ngrok.com/download
# OR use chocolatey: choco install ngrok
```

#### **2. Authenticate Ngrok**
```bash
ngrok authtoken *************************************************
```

#### **3. Start Tunnel**
```bash
# In separate terminal
ngrok http 5000
```

#### **4. Update Webhook URLs**
Update Twilio webhook URLs to use ngrok URL.

---

## 📊 **SERVER STATUS MONITORING**

### **Health Check Endpoints**
```
GET /health - Server health status
GET /api/status - API status
GET /api/twilio/status - Twilio integration status
```

### **Log Files**
- **Application logs**: `logs/app.log`
- **Error logs**: `logs/error.log`
- **Access logs**: `logs/access.log`

---

## 🔒 **SECURITY CONSIDERATIONS**

### **Development Mode**
- **Debug mode**: Enabled (disable in production)
- **Secret key**: Change for production
- **Database**: SQLite (use PostgreSQL for production)

### **Production Deployment**
```bash
# Disable debug mode
export FLASK_ENV=production

# Use production WSGI server
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

---

## ✅ **VERIFICATION CHECKLIST**

After starting the server, verify:

- [ ] Server starts without errors
- [ ] Can access http://localhost:5000
- [ ] Login page loads correctly
- [ ] Database is created
- [ ] All unified operation pages load
- [ ] Twilio configuration is valid
- [ ] No missing dependencies

---

## 🎯 **READY TO USE**

Once the server is running, you can access:

### **📱 Phone Operations**
- Real-time location tracking
- SMS campaigns with Nigerian templates
- Twilio voice calls for location capture
- Unified phone targeting interface

### **🦠 Malware Operations**
- FUD malware generation
- Advanced keyloggers
- Document weaponization
- AV evasion techniques

### **🎣 Phishing Operations**
- Intelligent website cloning
- Automatic logo/asset extraction
- Credential harvesting
- Nigerian-specific targeting

**🚀 AMADIOHA-M257 is ready for authorized cybersecurity testing!**
