{% extends 'base.html' %}
{% block title %}Phishing Link Builder | CYBER-OPS{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-link text-warning"></i> PHISHING LINK BUILDER</h2>
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-fish"></i> DOCUMENT SPOOFING
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Phishing Link Generator -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt text-primary"></i> DOCUMENT PHISHING GENERATOR</h5>
                </div>
                <div class="card-body">
                    <form id="phishingForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">TARGET EMAIL</label>
                                    <input type="email" class="form-control" id="targetEmail" placeholder="<EMAIL>" required>
                                    <div class="form-text">Email address of the target</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">FILE TYPE SPOOFING</label>
                                    <select class="form-select" id="fileType" required>
                                        <option value="pdf">📄 PDF Document</option>
                                        <option value="xlsx">📊 Excel Spreadsheet</option>
                                        <option value="docx">📝 Word Document</option>
                                    </select>
                                    <div class="form-text">Link will appear as this file type</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">TARGET SERVICE</label>
                                    <select class="form-select" id="targetService" required>
                                        <option value="gmail">Gmail</option>
                                        <option value="outlook">Outlook/Office 365</option>
                                        <option value="custom">Custom Service</option>
                                    </select>
                                    <div class="form-text">Webmail service to replicate</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">PHISHING SCENARIO</label>
                                    <select class="form-select" id="scenario">
                                        <option value="document_share">Shared Document</option>
                                        <option value="invoice">Invoice/Receipt</option>
                                        <option value="contract">Contract/Agreement</option>
                                        <option value="report">Security Report</option>
                                        <option value="update">System Update</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">URGENCY LEVEL</label>
                                    <select class="form-select" id="urgency">
                                        <option value="low">Low - Informational</option>
                                        <option value="medium">Medium - Action Required</option>
                                        <option value="high">High - Urgent Response</option>
                                        <option value="critical">Critical - Immediate Action</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">SENDER SPOOFING</label>
                                    <input type="text" class="form-control" id="senderName" placeholder="IT Department" value="IT Security">
                                    <div class="form-text">Apparent sender name</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>How it works:</strong> Creates a link that appears to be a document file. When clicked, 
                            shows a realistic file download page, then redirects to a replicated login page to capture credentials.
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-link"></i> GENERATE PHISHING LINK
                        </button>
                    </form>
                    
                    <div id="phishingResult" class="mt-4" style="display: none;">
                        <h6>GENERATED PHISHING LINK:</h6>
                        <div class="alert alert-dark">
                            <div class="mb-2">
                                <strong>Phishing URL:</strong>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="phishingUrl" readonly>
                                    <button class="btn btn-outline-light" onclick="copyToClipboard('phishingUrl')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-2">
                                <strong>Display Name:</strong>
                                <input type="text" class="form-control" id="displayName" readonly>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <button class="btn btn-sm btn-outline-primary w-100 mb-2" onclick="previewPhishingPage()">
                                    <i class="fas fa-eye"></i> PREVIEW LANDING PAGE
                                </button>
                            </div>
                            <div class="col-md-6">
                                <button class="btn btn-sm btn-outline-success w-100 mb-2" onclick="generateEmailTemplate()">
                                    <i class="fas fa-envelope"></i> EMAIL TEMPLATE
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaign Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie text-success"></i> CAMPAIGN STATS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Links Generated:</span>
                            <span class="badge bg-primary" id="linksCount">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Clicks:</span>
                            <span class="badge bg-warning" id="clicksCount">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Credentials Captured:</span>
                            <span class="badge bg-success" id="capturedCount">0</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>SUCCESS RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-success" style="width: 85%">85%</div>
                    </div>
                    <small class="text-muted">Average click-through rate</small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt text-info"></i> BEST PRACTICES</h5>
                </div>
                <div class="card-body">
                    <ul class="small">
                        <li><strong>Timing:</strong> Send during business hours</li>
                        <li><strong>Context:</strong> Use relevant business scenarios</li>
                        <li><strong>Urgency:</strong> Create time pressure</li>
                        <li><strong>Authority:</strong> Spoof trusted sources</li>
                        <li><strong>Personalization:</strong> Use target's information</li>
                        <li><strong>Follow-up:</strong> Monitor and analyze results</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Email Template Generator -->
    <div class="row">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-envelope"></i> EMAIL TEMPLATE GENERATOR</h5>
                </div>
                <div class="card-body" id="emailTemplateSection" style="display: none;">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Generated Email Template:</h6>
                            <div class="alert alert-light">
                                <pre id="emailTemplate" style="white-space: pre-wrap; color: #000;"></pre>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>Email Guidelines:</h6>
                            <ul class="small">
                                <li>Use company branding if available</li>
                                <li>Include realistic sender information</li>
                                <li>Create urgency without being obvious</li>
                                <li>Use professional language</li>
                                <li>Include contact information for credibility</li>
                            </ul>
                            
                            <button class="btn btn-sm btn-outline-dark w-100 mt-3" onclick="copyEmailTemplate()">
                                <i class="fas fa-copy"></i> COPY EMAIL TEMPLATE
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let linksGenerated = 0;
let currentPhishingData = null;

document.getElementById('phishingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_email: document.getElementById('targetEmail').value,
        file_type: document.getElementById('fileType').value,
        target_service: document.getElementById('targetService').value
    };
    
    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> GENERATING...';
    submitBtn.disabled = true;
    
    fetch('/api/create_phishing_link', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentPhishingData = data;
            document.getElementById('phishingUrl').value = data.phishing_url;
            document.getElementById('displayName').value = data.display_name;
            document.getElementById('phishingResult').style.display = 'block';
            
            // Update statistics
            linksGenerated++;
            document.getElementById('linksCount').textContent = linksGenerated;
            
            // Scroll to result
            document.getElementById('phishingResult').scrollIntoView({ behavior: 'smooth' });
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error generating phishing link: ' + error);
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    document.execCommand('copy');
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 2000);
}

function previewPhishingPage() {
    if (currentPhishingData) {
        window.open(currentPhishingData.phishing_url, '_blank');
    }
}

function generateEmailTemplate() {
    const scenario = document.getElementById('scenario').value;
    const urgency = document.getElementById('urgency').value;
    const senderName = document.getElementById('senderName').value;
    const fileType = document.getElementById('fileType').value;
    const displayName = document.getElementById('displayName').value;
    const phishingUrl = document.getElementById('phishingUrl').value;
    
    const templates = {
        document_share: `Subject: Document Shared - Action Required

Dear Team Member,

A document has been shared with you that requires your immediate attention.

Document: ${displayName}
Shared by: ${senderName}
Access Level: Confidential

Please click the link below to access the document:
${phishingUrl}

Note: You may need to sign in to verify your identity before accessing the document.

Best regards,
${senderName}
IT Security Department`,

        invoice: `Subject: Invoice #${Math.floor(Math.random() * 10000)} - Payment Required

Dear Customer,

Please find attached your invoice for recent services. Payment is due within 30 days.

Invoice: ${displayName}
Amount: $${Math.floor(Math.random() * 5000) + 500}
Due Date: ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}

View Invoice: ${phishingUrl}

If you have any questions, please contact our billing department.

Best regards,
Billing Department`,

        contract: `Subject: Contract Review Required - ${displayName}

Dear ${document.getElementById('targetEmail').value.split('@')[0]},

Please review the attached contract document. Your signature is required by end of business today.

Contract: ${displayName}
Review Deadline: Today, 5:00 PM

Access Document: ${phishingUrl}

Please sign in with your corporate credentials to access the secure document.

Best regards,
Legal Department`,

        report: `Subject: Security Alert - Immediate Review Required

CONFIDENTIAL - SECURITY REPORT

A security incident has been detected that may affect your account. Please review the attached security report immediately.

Report: ${displayName}
Incident ID: SEC-${Math.floor(Math.random() * 100000)}
Priority: HIGH

View Report: ${phishingUrl}

You must sign in to access this confidential security information.

IT Security Team`,

        update: `Subject: System Update Required - Action Needed

Important System Update Notice

A critical system update is available for your account. Please download and install the update package immediately.

Update Package: ${displayName}
Version: ${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 100)}
Release Date: ${new Date().toLocaleDateString()}

Download Update: ${phishingUrl}

This update addresses critical security vulnerabilities and must be installed within 24 hours.

System Administrator`
    };
    
    const template = templates[scenario] || templates.document_share;
    document.getElementById('emailTemplate').textContent = template;
    document.getElementById('emailTemplateSection').style.display = 'block';
    
    // Scroll to email template
    document.getElementById('emailTemplateSection').scrollIntoView({ behavior: 'smooth' });
}

function copyEmailTemplate() {
    const template = document.getElementById('emailTemplate').textContent;
    navigator.clipboard.writeText(template).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> COPIED!';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    });
}
</script>

{% endblock %}
