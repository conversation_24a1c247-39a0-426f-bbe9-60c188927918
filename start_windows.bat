@echo off
title Augie-Pentest H1 v2.0 - Windows Edition
color 0A

echo.
echo ========================================
echo   AUGIE-PENTEST H1 v2.0 - WINDOWS EDITION
echo   Powered by Augment AI
echo ========================================
echo.
echo Starting Windows-compatible security platform...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python 3.8+ from https://python.org
    echo Make sure Python is added to your PATH
    pause
    exit /b 1
)

REM Check if windows_tools.py exists
if not exist "windows_tools.py" (
    echo ERROR: windows_tools.py not found!
    echo Please ensure all files are in the same directory
    pause
    exit /b 1
)

REM Set environment variables for Windows
set FLASK_APP=app.py
set FLASK_ENV=development
set PYTHONPATH=%CD%

echo Checking Windows tools...
python -c "from windows_tools import WindowsPortScanner; print('Windows tools OK')" 2>nul
if errorlevel 1 (
    echo Warning: Windows tools may not be fully functional
    echo Installing required packages...
    pip install requests psutil
)

echo.
echo Starting Augie-Pentest H1 v2.0...
echo.
echo The platform will be available at:
echo http://localhost:5000
echo.
echo Access Code: SHYJAYOBIBI
echo Default Login: admin / admin123
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the Flask application
python app.py

echo.
echo Server stopped.
pause
