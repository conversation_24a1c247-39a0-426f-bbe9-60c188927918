{% extends "base.html" %}

{% block title %}New Template - Phishing Awareness Platform{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-plus"></i> New Template</h2>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-envelope"></i> Template Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="template_name" class="form-label">Template Name *</label>
                                <input type="text" class="form-control" id="template_name" name="template_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="campaign_id" class="form-label">Campaign *</label>
                                <select class="form-control" id="campaign_id" name="campaign_id" required>
                                    <option value="">Select campaign...</option>
                                    {% for campaign in campaigns %}
                                        <option value="{{ campaign.id }}">{{ campaign.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="subject" class="form-label">Email Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject" 
                                       placeholder="Urgent: Security Update Required">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="attachment_type" class="form-label">Attachment Type</label>
                                <select class="form-control" id="attachment_type" name="attachment_type">
                                    <option value="">No attachment</option>
                                    <option value="PDF">PDF Document</option>
                                    <option value="XLSX">Excel Spreadsheet</option>
                                    <option value="DOCX">Word Document</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="body" class="form-label">Email Body *</label>
                            <textarea class="form-control" id="body" name="body" rows="12" required
                                      placeholder="Enter your email template content here..."></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i> Use HTML tags for formatting. Available variables: {{name}}, {{email}}, {{company}}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Template Features</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="tracking_enabled" checked>
                                    <label class="form-check-label" for="tracking_enabled">
                                        Enable click tracking
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="credential_capture" checked>
                                    <label class="form-check-label" for="credential_capture">
                                        Capture credentials
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Security Settings</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sandbox_mode" checked>
                                    <label class="form-check-label" for="sandbox_mode">
                                        Sandbox execution
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="virus_scan" checked>
                                    <label class="form-check-label" for="virus_scan">
                                        VirusTotal scanning
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('templates') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Template
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 