{% extends "base.html" %}

{% block title %}CVEs | Phishing Awareness Platform{% endblock %}

{% block content %}
<style>
.card {
    background: var(--card-bg);
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
    box-shadow: 0 12px 36px rgba(124,58,237,0.18), var(--shadow);
    transform: scale(1.015);
}
.table {
    color: var(--text-primary);
}
.table th {
    background: rgba(124,58,237,0.08);
    color: var(--primary-color);
    border-color: var(--border-color);
}
.table td {
    border-color: var(--border-color);
}
.btn-cyber {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border: var(--border-width) solid var(--border-color);
    color: #fff;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: background 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.btn-cyber:hover {
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    box-shadow: 0 4px 16px rgba(124,58,237,0.18);
}
</style>
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-bug"></i> Common Vulnerabilities and Exposures (CVEs)</h2>
            <button class="btn btn-primary" onclick="refreshCVEs()">
                <i class="fas fa-sync"></i> Refresh
            </button>
        </div>
    </div>

    <!-- CVE Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ cves|length }}</div>
                <div class="stats-label">Total CVEs</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ cves|selectattr('severity', 'equalto', 'CRITICAL')|list|length }}</div>
                <div class="stats-label">Critical</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ cves|selectattr('severity', 'equalto', 'HIGH')|list|length }}</div>
                <div class="stats-label">High</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ cves|selectattr('severity', 'equalto', 'MEDIUM')|list|length }}</div>
                <div class="stats-label">Medium</div>
            </div>
        </div>
    </div>

    <!-- CVE List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> Latest CVEs</h5>
                </div>
                <div class="card-body">
                    {% if cves %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>CVE ID</th>
                                        <th>Description</th>
                                        <th>Severity</th>
                                        <th>Published</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for cve in cves %}
                                    <tr>
                                        <td>
                                            <strong>{{ cve.cve_id }}</strong>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 400px;" title="{{ cve.description }}">
                                                {{ cve.description[:100] }}...
                                            </div>
                                        </td>
                                        <td>
                                            {% if cve.severity == 'CRITICAL' %}
                                                <span class="badge bg-danger">Critical</span>
                                            {% elif cve.severity == 'HIGH' %}
                                                <span class="badge bg-warning">High</span>
                                            {% elif cve.severity == 'MEDIUM' %}
                                                <span class="badge bg-info">Medium</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ cve.severity }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ cve.published_date.strftime('%Y-%m-%d') if cve.published_date else 'N/A' }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-info" title="Search Exploits">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning" title="Check Impact">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-bug fa-3x text-secondary mb-3"></i>
                            <h5 class="text-secondary">No CVEs available</h5>
                            <p class="text-secondary">CVE database is being updated...</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Threat Intelligence -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shield-alt"></i> Threat Intelligence</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Latest Threat Trends</h6>
                        <ul class="mb-0">
                            <li>Ransomware attacks increased by 150% in Q4 2023</li>
                            <li>Phishing campaigns targeting remote workers</li>
                            <li>Supply chain attacks on rise</li>
                            <li>Zero-day exploits in popular software</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Active Threats</h6>
                        <ul class="mb-0">
                            <li>APT groups targeting healthcare sector</li>
                            <li>Credential stuffing attacks</li>
                            <li>Social engineering via social media</li>
                            <li>Malware distribution via fake updates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Vulnerability Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="cveChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshCVEs() {
    // Simulate CVE refresh
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        location.reload();
    }, 2000);
}

// CVE Trends Chart
const cveCtx = document.getElementById('cveChart').getContext('2d');
const cveChart = new Chart(cveCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Critical CVEs',
            data: [12, 19, 8, 15, 22, 18],
            borderColor: '#ff4757',
            backgroundColor: 'rgba(255, 71, 87, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'High CVEs',
            data: [45, 52, 38, 47, 55, 48],
            borderColor: '#ffaa00',
            backgroundColor: 'rgba(255, 170, 0, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Medium CVEs',
            data: [120, 135, 110, 125, 140, 130],
            borderColor: '#00aaff',
            backgroundColor: 'rgba(0, 170, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#ffffff'
                }
            },
            x: {
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#ffffff'
                }
            }
        },
        plugins: {
            legend: {
                labels: {
                    color: '#ffffff'
                }
            }
        }
    }
});
</script>
{% endblock %} 