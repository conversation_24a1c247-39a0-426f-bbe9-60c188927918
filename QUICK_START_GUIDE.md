# ⚡ CYBER-OPS PLATFORM QUICK START GUIDE

## 🚨 **CRITICAL WARNING**
**THIS PLATFORM CONTAINS REAL FUNCTIONAL MALWARE AND PHISHING TOOLS**
- **AUTHORIZED USE ONLY** - Obtain written permission before deployment
- **ISOLATED ENVIRONMENT** - Deploy in secure, isolated networks only
- **LEGAL COMPLIANCE** - Ensure compliance with all applicable laws

---

## 🚀 **5-MINUTE QUICK DEPLOYMENT**

### **Step 1: Prerequisites Check** ⏱️ 1 minute
```bash
# Verify Python 3.11+
python3 --version

# Check system requirements
free -h  # Minimum 4GB RAM
df -h    # Minimum 10GB disk space

# Verify network connectivity
ping google.com
```

### **Step 2: Platform Installation** ⏱️ 2 minutes
```bash
# Clone repository
git clone <repository-url> cyber-ops
cd cyber-ops

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt
```

### **Step 3: Basic Configuration** ⏱️ 1 minute
```bash
# Copy configuration template
cp config.example.py config.py

# Edit basic settings (use nano/vim/notepad)
nano config.py
```

**Minimum required configuration:**
```python
SECRET_KEY = 'your-super-secret-key-change-this'
DATABASE_URL = 'sqlite:///cyber-ops.db'
DEBUG = True  # Only for testing
```

### **Step 4: Initialize Database** ⏱️ 30 seconds
```bash
# Initialize database and create admin user
python app.py --init-db
```

### **Step 5: Start Platform** ⏱️ 30 seconds
```bash
# Start the platform
python app.py
```

**✅ Platform should now be running at: http://localhost:5000**

---

## 🔐 **FIRST LOGIN**

### **Default Credentials**
```
URL: http://localhost:5000/login
Username: admin
Password: admin123
```

### **⚠️ IMMEDIATE SECURITY ACTIONS**
1. **Change default password** immediately
2. **Configure HTTPS** for production use
3. **Set up firewall rules**
4. **Enable audit logging**

---

## 🎯 **ESSENTIAL CONFIGURATION**

### **1. SMTP Configuration (for phishing)**
```python
# In config.py
SMTP_SERVER = 'smtp.gmail.com'
SMTP_PORT = 587
SMTP_USERNAME = '<EMAIL>'
SMTP_PASSWORD = 'your-app-password'  # Use app-specific password
```

### **2. Telegram Bot Setup (for notifications)**
```python
# In config.py
TELEGRAM_BOT_TOKEN = 'your-bot-token'
TELEGRAM_CHAT_ID = 'your-chat-id'
```

**How to get Telegram credentials:**
1. Message @BotFather on Telegram
2. Create new bot: `/newbot`
3. Get bot token
4. Get chat ID: Message @userinfobot

### **3. Ngrok Integration (for public access)**
```python
# In config.py
NGROK_AUTH_TOKEN = 'your-ngrok-token'
```

**Get Ngrok token:**
1. Sign up at https://ngrok.com
2. Get auth token from dashboard
3. Configure in platform

---

## 🦠 **QUICK MALWARE DEPLOYMENT**

### **Deploy Keylogger** ⏱️ 2 minutes
1. Navigate to: **Real Malware Deployment**
2. Configure:
   - Target OS: Windows
   - Persistence: Enabled
   - Stealth Mode: Enabled
3. Click **DEPLOY KEYLOGGER**
4. Download generated payload
5. Deploy to target system (with authorization)

### **Deploy Credential Stealer** ⏱️ 1 minute
1. Select target browsers: Chrome, Firefox, Edge
2. Configure callback URL
3. Click **DEPLOY CREDENTIAL STEALER**
4. Download and deploy payload

---

## 🎣 **QUICK PHISHING CAMPAIGN**

### **Send Phishing Email** ⏱️ 3 minutes
1. Navigate to: **Enhanced Social Engineering**
2. Configure:
   ```
   Target Email: <EMAIL>
   Template: Urgent Security Alert
   SMTP Settings: [Your SMTP credentials]
   ```
3. Optional: Enable PDF attachment
4. Click **SEND PHISHING EMAIL**

### **Generate Perfect Login Replica** ⏱️ 1 minute
1. Access: `http://localhost:5000/perfect_replica/gmail?target=<EMAIL>`
2. Share URL with target (authorized testing only)
3. Monitor credential harvesting in dashboard

---

## 📄 **QUICK PDF ATTACK**

### **Generate Malicious PDF** ⏱️ 2 minutes
1. Navigate to: **PDF Attachment Generator**
2. Configure:
   ```
   Target Email: <EMAIL>
   Document Type: Invoice
   Target Service: Gmail
   ```
3. Click **GENERATE PDF**
4. Download and distribute PDF (authorized testing only)

---

## 🔍 **QUICK NETWORK SCAN**

### **Subdomain Enumeration** ⏱️ 2 minutes
1. Navigate to: **Network Reconnaissance**
2. Enter target domain: `company.com`
3. Select method: DNS Bruteforce
4. Click **START ENUMERATION**

### **Port Scanning** ⏱️ 3 minutes
1. Enter target IP: `*************`
2. Port range: `1-1000`
3. Scan type: Stealth SYN
4. Click **START SCAN**

---

## 📊 **MONITORING DASHBOARD**

### **Key Metrics to Monitor**
- **Active Malware Sessions**: Live keylogger/stealer instances
- **Credentials Harvested**: Real-time credential capture
- **Phishing Success Rate**: Campaign effectiveness
- **Network Discoveries**: Found services and vulnerabilities

### **Real-time Alerts**
- **Telegram Notifications**: Instant alerts for all activities
- **Dashboard Alerts**: In-platform notifications
- **Email Alerts**: Critical event notifications

---

## 🛠️ **TROUBLESHOOTING**

### **Common Issues & Quick Fixes**

#### **Platform Won't Start**
```bash
# Check Python version
python3 --version  # Must be 3.11+

# Check dependencies
pip list | grep Flask

# Check logs
tail -f cyber-ops.log
```

#### **Database Errors**
```bash
# Reinitialize database
rm cyber-ops.db
python app.py --init-db
```

#### **SMTP Not Working**
- Verify Gmail app password (not regular password)
- Check firewall blocking port 587
- Test SMTP connectivity: `telnet smtp.gmail.com 587`

#### **Ngrok Connection Issues**
- Verify auth token is correct
- Check internet connectivity
- Try different tunnel type (HTTP/TCP)

### **Performance Issues**
```bash
# Check system resources
htop
df -h
free -h

# Optimize database
sqlite3 cyber-ops.db "VACUUM;"
```

---

## 🔐 **SECURITY CHECKLIST**

### **Before Going Live**
- [ ] Change default admin password
- [ ] Configure HTTPS/SSL certificates
- [ ] Set up firewall rules
- [ ] Enable audit logging
- [ ] Configure backup procedures
- [ ] Test incident response procedures

### **Network Security**
```bash
# Basic firewall setup (Ubuntu/Debian)
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 5000/tcp   # Block direct access
```

### **Access Control**
- Use strong passwords (12+ characters)
- Enable two-factor authentication
- Implement IP whitelisting
- Regular access reviews

---

## 📚 **NEXT STEPS**

### **Advanced Configuration**
1. **Read Full Deployment Guide**: `DEPLOYMENT_GUIDE.md`
2. **Review Security Guidelines**: `SECURITY_COMPLIANCE_GUIDE.md`
3. **Study Operational Manual**: `OPERATIONAL_MANUAL.md`

### **Production Deployment**
1. **Set up dedicated server**
2. **Configure SSL/TLS certificates**
3. **Implement proper backup procedures**
4. **Set up monitoring and alerting**
5. **Conduct security assessment**

### **Team Training**
1. **Security awareness training**
2. **Platform operation training**
3. **Incident response procedures**
4. **Legal and ethical guidelines**

---

## 🎯 **TESTING SCENARIOS**

### **Scenario 1: Basic Keylogger Test** ⏱️ 5 minutes
1. Deploy keylogger to test system
2. Type some test data
3. Verify data capture in dashboard
4. Test persistence after reboot

### **Scenario 2: Phishing Campaign** ⏱️ 10 minutes
1. Send phishing email to test account
2. Click phishing link
3. Enter test credentials
4. Verify credential capture

### **Scenario 3: Network Reconnaissance** ⏱️ 15 minutes
1. Scan internal network range
2. Enumerate discovered services
3. Identify potential vulnerabilities
4. Generate reconnaissance report

---

## 📞 **SUPPORT & RESOURCES**

### **Documentation**
- **Deployment Guide**: Complete installation instructions
- **Operational Manual**: Day-to-day operation procedures
- **Security Guide**: Security and compliance requirements
- **API Documentation**: Technical API reference

### **Best Practices**
- Always obtain written authorization
- Use isolated testing environments
- Monitor all activities continuously
- Maintain detailed audit logs
- Follow responsible disclosure practices

### **Emergency Procedures**
- **Security Incident**: Isolate systems, preserve evidence
- **Legal Issues**: Contact legal department immediately
- **Technical Problems**: Check logs, restart services
- **Performance Issues**: Monitor resources, optimize database

---

## ⚡ **QUICK REFERENCE COMMANDS**

### **Platform Management**
```bash
# Start platform
python app.py

# Initialize database
python app.py --init-db

# Check status
curl http://localhost:5000/health

# View logs
tail -f cyber-ops.log
```

### **System Monitoring**
```bash
# Check resources
htop
df -h
netstat -tulpn | grep :5000

# Database maintenance
sqlite3 cyber-ops.db "VACUUM;"
```

### **Security Operations**
```bash
# Check firewall
sudo ufw status

# Monitor connections
ss -tulpn

# Review audit logs
tail -f /var/log/audit/audit.log
```

---

**🚀 CYBER-OPS PLATFORM IS NOW READY FOR AUTHORIZED TESTING!**

Remember: **Always ensure proper authorization before conducting any security testing activities.**

For detailed information, refer to the complete documentation suite:
- `DEPLOYMENT_GUIDE.md` - Full deployment instructions
- `OPERATIONAL_MANUAL.md` - Comprehensive operation guide
- `SECURITY_COMPLIANCE_GUIDE.md` - Security and compliance requirements
