#!/usr/bin/env python3
"""
Test script for Nigerian Phone Number Tracking (+234)
AMADIOHA-M257 Enhanced Nigerian Phone Tracking System
"""

import requests
import json
import time
import sys

# Test configuration for Nigerian numbers
BASE_URL = "http://localhost:5000"
USERNAME = "admin"
PASSWORD = "admin123"

# Nigerian test phone numbers (various carriers)
NIGERIAN_TEST_NUMBERS = [
    "+2348031234567",  # MTN Nigeria
    "+2348021234567",  # Airtel Nigeria  
    "+2348051234567",  # Glo Mobile
    "+2348091234567",  # 9mobile
    "+2348041234567",  # Ntel
    "08031234567",     # Local format (MTN)
    "2348031234567",   # International without +
]

def login():
    """Login to get session"""
    session = requests.Session()
    
    # Get login page first
    response = session.get(f"{BASE_URL}/login")
    if response.status_code != 200:
        print(f"❌ Failed to access login page: {response.status_code}")
        return None
    
    # Login
    login_data = {
        'username': USERNAME,
        'password': PASSWORD
    }
    
    response = session.post(f"{BASE_URL}/login", data=login_data)
    if response.status_code == 200 and "dashboard" in response.url:
        print("✅ Login successful")
        return session
    else:
        print(f"❌ Login failed: {response.status_code}")
        return None

def test_nigerian_phone_validation(session):
    """Test Nigerian phone number validation and formatting"""
    print("\n🇳🇬 Testing Nigerian Phone Number Validation...")
    print("-" * 50)
    
    for phone_number in NIGERIAN_TEST_NUMBERS:
        print(f"\n📱 Testing: {phone_number}")
        
        data = {'phone_number': phone_number}
        
        response = session.post(f"{BASE_URL}/api/phone_info", json=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                phone_data = result['data']
                print(f"   ✅ Validation successful")
                print(f"   📍 Country: {phone_data.get('country', 'Unknown')}")
                print(f"   📡 Carrier: {phone_data.get('carrier', 'Unknown')}")
                print(f"   🔢 Format: {phone_data.get('number', 'Unknown')}")
                print(f"   ✓ Valid: {phone_data.get('is_valid', False)}")
                
                # Check Nigerian-specific data
                if 'nigerian_carrier' in phone_data:
                    print(f"   🇳🇬 Nigerian Carrier: {phone_data.get('nigerian_carrier')}")
                    print(f"   📶 Network Prefix: {phone_data.get('network_prefix')}")
                    print(f"   📱 Network Type: {phone_data.get('network_type')}")
                
                # Check NumVerify data
                if 'carrier_info' in phone_data:
                    print(f"   🔍 NumVerify Carrier: {phone_data.get('carrier_info')}")
                    print(f"   📍 NumVerify Location: {phone_data.get('location')}")
                    print(f"   📞 Line Type: {phone_data.get('line_type')}")
                
            else:
                print(f"   ❌ Validation failed: {result.get('error')}")
        else:
            print(f"   ❌ API request failed: {response.status_code}")

def test_nigerian_location_tracking(session):
    """Test location tracking for Nigerian numbers"""
    print("\n🛰️ Testing Nigerian Location Tracking...")
    print("-" * 50)
    
    # Use MTN number for tracking test
    test_number = "+2348031234567"
    print(f"📱 Tracking Nigerian number: {test_number}")
    
    # Start tracking
    data = {
        'phone_number': test_number,
        'duration': 2  # 2 minutes for testing
    }
    
    response = session.post(f"{BASE_URL}/api/start_tracking", json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            tracking_id = result.get('tracking_id')
            print(f"✅ Tracking started successfully")
            print(f"🆔 Tracking ID: {tracking_id}")
            
            # Monitor tracking for a few updates
            print("\n📊 Monitoring location updates...")
            for i in range(3):
                time.sleep(10)  # Wait 10 seconds between checks
                
                status_response = session.get(f"{BASE_URL}/api/tracking_status/{tracking_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    if status_data.get('success'):
                        print(f"\n📍 Update #{i+1}:")
                        print(f"   Status: {status_data.get('status')}")
                        print(f"   Total Locations: {status_data.get('total_locations')}")
                        
                        current_loc = status_data.get('current_location')
                        if current_loc:
                            print(f"   📍 Latitude: {current_loc.get('latitude')}")
                            print(f"   📍 Longitude: {current_loc.get('longitude')}")
                            print(f"   🎯 Accuracy: ±{current_loc.get('accuracy')}m")
                            print(f"   📡 Source: {current_loc.get('source')}")
                            
                            # Nigerian-specific location data
                            if 'estimated_city' in current_loc:
                                print(f"   🏙️ Estimated City: {current_loc.get('estimated_city')}")
                                print(f"   🇳🇬 Country: {current_loc.get('country')}")
                                print(f"   📶 Carrier: {current_loc.get('carrier')}")
                                print(f"   📡 Network: {current_loc.get('network_type')}")
            
            # Stop tracking
            stop_data = {'tracking_id': tracking_id}
            stop_response = session.post(f"{BASE_URL}/api/stop_tracking", json=stop_data)
            if stop_response.status_code == 200:
                print(f"\n🛑 Tracking stopped successfully")
            
        else:
            print(f"❌ Failed to start tracking: {result.get('error')}")
    else:
        print(f"❌ Tracking request failed: {response.status_code}")

def test_nigerian_carrier_detection(session):
    """Test Nigerian carrier detection accuracy"""
    print("\n📶 Testing Nigerian Carrier Detection...")
    print("-" * 50)
    
    # Test numbers for each major Nigerian carrier
    carrier_tests = {
        "MTN Nigeria": ["+2348031234567", "+2348061234567", "+2348131234567"],
        "Airtel Nigeria": ["+2348021234567", "+2348081234567", "+2348121234567"],
        "Glo Mobile": ["+2348051234567", "+2348071234567", "+2348111234567"],
        "9mobile": ["+2348091234567", "+2348171234567", "+2348181234567"],
        "Ntel": ["+2348041234567"]
    }
    
    for expected_carrier, numbers in carrier_tests.items():
        print(f"\n📡 Testing {expected_carrier}:")
        
        for number in numbers:
            data = {'phone_number': number}
            response = session.post(f"{BASE_URL}/api/phone_info", json=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    phone_data = result['data']
                    detected_carrier = phone_data.get('nigerian_carrier', 'Unknown')
                    prefix = phone_data.get('network_prefix', 'Unknown')
                    
                    if expected_carrier in detected_carrier:
                        print(f"   ✅ {number} → {detected_carrier} (Prefix: {prefix})")
                    else:
                        print(f"   ⚠️ {number} → {detected_carrier} (Expected: {expected_carrier})")
                else:
                    print(f"   ❌ {number} → Validation failed")

def test_numverify_integration(session):
    """Test NumVerify API integration with Nigerian numbers"""
    print("\n🔍 Testing NumVerify API Integration...")
    print("-" * 50)
    
    test_number = "+2348031234567"
    print(f"📱 Testing NumVerify with: {test_number}")
    
    data = {'phone_number': test_number}
    response = session.post(f"{BASE_URL}/api/phone_info", json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            phone_data = result['data']
            
            print("📊 NumVerify Response Data:")
            numverify_fields = [
                'valid', 'international_format', 'local_format',
                'country_prefix', 'country_name', 'country_code',
                'line_type', 'carrier_info', 'location'
            ]
            
            for field in numverify_fields:
                if field in phone_data:
                    print(f"   {field}: {phone_data[field]}")
            
            # Check if fallback mode was used
            if phone_data.get('fallback_mode'):
                print("   ⚠️ Using fallback mode (NumVerify API may be unavailable)")
            else:
                print("   ✅ NumVerify API responded successfully")
                
        else:
            print(f"❌ Phone info failed: {result.get('error')}")
    else:
        print(f"❌ API request failed: {response.status_code}")

def main():
    """Main test function for Nigerian phone tracking"""
    print("🇳🇬 AMADIOHA-M257 Nigerian Phone Tracking Test")
    print("=" * 60)
    print("Testing enhanced Nigerian phone number tracking capabilities")
    print("Supported carriers: MTN, Airtel, Glo, 9mobile, Ntel")
    print("=" * 60)
    
    # Login
    session = login()
    if not session:
        print("❌ Cannot proceed without login")
        sys.exit(1)
    
    # Run Nigerian-specific tests
    test_nigerian_phone_validation(session)
    test_nigerian_carrier_detection(session)
    test_numverify_integration(session)
    test_nigerian_location_tracking(session)
    
    print("\n" + "=" * 60)
    print("🎯 Nigerian Phone Tracking Test Complete!")
    print("\n📝 Test Summary:")
    print("   ✅ Nigerian phone number validation")
    print("   ✅ Carrier detection (MTN, Airtel, Glo, 9mobile, Ntel)")
    print("   ✅ NumVerify API integration")
    print("   ✅ Location tracking with Nigerian cities")
    print("   ✅ Enhanced accuracy for Nigerian mobile networks")
    print("\n🔒 Remember: Use only for authorized cybersecurity testing!")
    print("🇳🇬 Optimized for Nigerian telecommunications infrastructure")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
