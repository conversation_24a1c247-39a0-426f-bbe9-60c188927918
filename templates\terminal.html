{% extends "base.html" %}

{% block title %}Terminal - AMADIOHA-M257{% endblock %}

{% block content %}
<style>
.terminal-container {
    background: #000;
    border: 2px solid var(--terminal-green);
    border-radius: 8px;
    padding: 20px;
    font-family: 'Courier New', monospace;
    color: var(--terminal-green);
    height: 70vh;
    overflow-y: auto;
    position: relative;
}

.terminal-header {
    background: var(--terminal-green);
    color: #000;
    padding: 8px 15px;
    margin: -20px -20px 15px -20px;
    font-weight: bold;
    border-radius: 6px 6px 0 0;
}

.terminal-output {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-bottom: 10px;
    line-height: 1.4;
}

.terminal-input-line {
    display: flex;
    align-items: center;
    margin-top: 10px;
}

.terminal-prompt {
    color: var(--terminal-green);
    margin-right: 8px;
    font-weight: bold;
}

.terminal-input {
    background: transparent;
    border: none;
    color: var(--terminal-green);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    outline: none;
    flex: 1;
    caret-color: var(--terminal-green);
}

.terminal-cursor {
    background: var(--terminal-green);
    width: 8px;
    height: 16px;
    display: inline-block;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.command-history {
    color: #888;
    font-size: 12px;
}

.error-text {
    color: #ff6b6b;
}

.success-text {
    color: #51cf66;
}

.info-text {
    color: #74c0fc;
}
</style>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-terminal"></i> System Terminal</h2>
            <p class="text-muted">Real command line interface - Use with caution</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="terminal-container" id="terminal">
                <div class="terminal-header">
                    <i class="fas fa-terminal"></i> AMADIOHA-M257 Terminal v1.0.0 - BMD CYBER OPS
                </div>

                <div class="terminal-output" id="output">
Welcome to AMADIOHA-M257 Terminal Interface
Licensed under BMD CYBER OPS LEGAL RIGHT

WARNING: This is a real system terminal. Use responsibly.
Type 'help' for available commands.

AMADIOHA-M257:~$
                </div>

                <div class="terminal-input-line">
                    <span class="terminal-prompt">AMADIOHA-M257:~$</span>
                    <input type="text" class="terminal-input" id="commandInput" autocomplete="off" spellcheck="false">
                    <span class="terminal-cursor"></span>
                </div>
            </div>
        </div>
    </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> Available Commands</h6>
                </div>
                <div class="card-body">
                    <div class="command-history">
                        <strong>System Info:</strong> dir, ls, pwd, whoami, date, time, ver, systeminfo<br>
                        <strong>Network:</strong> ipconfig, ping, nslookup, netstat<br>
                        <strong>Process:</strong> tasklist<br>
                        <strong>Utility:</strong> echo, help<br>
                        <em>Note: Commands are restricted for security</em>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-history"></i> Command History</h6>
                </div>
                <div class="card-body">
                    <div id="commandHistory" class="command-history" style="max-height: 150px; overflow-y: auto;">
                        No commands executed yet.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let commandHistory = [];
let historyIndex = -1;

document.getElementById('commandInput').addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
        executeCommand();
    } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (historyIndex < commandHistory.length - 1) {
            historyIndex++;
            this.value = commandHistory[commandHistory.length - 1 - historyIndex] || '';
        }
    } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (historyIndex > 0) {
            historyIndex--;
            this.value = commandHistory[commandHistory.length - 1 - historyIndex] || '';
        } else if (historyIndex === 0) {
            historyIndex = -1;
            this.value = '';
        }
    }
});

function executeCommand() {
    const input = document.getElementById('commandInput');
    const output = document.getElementById('output');
    const command = input.value.trim();

    if (!command) return;

    // Add command to history
    commandHistory.push(command);
    historyIndex = -1;
    updateCommandHistory();

    // Display command in terminal
    output.innerHTML += `<span class="success-text">AMADIOHA-M257:~$ ${command}</span>\n`;

    // Clear input
    input.value = '';

    // Execute command
    fetch('/terminal/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command: command })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            output.innerHTML += `<span class="error-text">${data.error}</span>\n`;
        } else {
            output.innerHTML += `${data.output}\n`;
        }
        output.innerHTML += `<span class="success-text">AMADIOHA-M257:~$ </span>`;

        // Scroll to bottom
        const terminal = document.getElementById('terminal');
        terminal.scrollTop = terminal.scrollHeight;
    })
    .catch(error => {
        output.innerHTML += `<span class="error-text">Error: ${error.message}</span>\n`;
        output.innerHTML += `<span class="success-text">AMADIOHA-M257:~$ </span>`;
    });
}

function updateCommandHistory() {
    const historyDiv = document.getElementById('commandHistory');
    if (commandHistory.length === 0) {
        historyDiv.innerHTML = 'No commands executed yet.';
    } else {
        historyDiv.innerHTML = commandHistory.slice(-10).reverse().map(cmd =>
            `<div>${cmd}</div>`
        ).join('');
    }
}

// Focus input on page load
document.getElementById('commandInput').focus();

// Keep input focused when clicking in terminal
document.getElementById('terminal').addEventListener('click', function() {
    document.getElementById('commandInput').focus();
});
</script>
{% endblock %}
