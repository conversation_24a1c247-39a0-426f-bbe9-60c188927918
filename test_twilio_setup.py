#!/usr/bin/env python3
"""
Test Twilio Setup for AMADIOHA-M257
Test SMS and Voice capabilities with Nigerian number +*************
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_twilio_credentials():
    """Test if Twilio credentials are properly configured"""
    print("🔧 TWILIO CONFIGURATION TEST")
    print("=" * 50)
    
    account_sid = os.getenv('TWILIO_ACCOUNT_SID')
    auth_token = os.getenv('TWILIO_AUTH_TOKEN')
    from_number = os.getenv('TWILIO_PHONE_NUMBER')
    
    print(f"Account SID: {account_sid}")
    print(f"From Number: {from_number}")
    print(f"Auth Token: {'*' * 20}...{auth_token[-4:] if auth_token else 'Not set'}")
    
    if all([account_sid, auth_token, from_number]):
        print("✅ All Twilio credentials are configured!")
        return True
    else:
        print("❌ Missing Twilio credentials!")
        return False

def test_sms_to_nigerian_number():
    """Test SMS sending to Nigerian number"""
    print("\n📱 SMS TEST TO NIGERIAN NUMBER")
    print("=" * 50)
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        from_number = os.getenv('TWILIO_PHONE_NUMBER')
        
        # Target Nigerian number
        target_number = '+*************'
        
        # Create Twilio client
        client = Client(account_sid, auth_token)
        
        # Test message with MTN branding
        test_message = "MTN Alert: This is a test message from AMADIOHA-M257 cybersecurity platform. Your line is secure."
        
        print(f"📞 From: {from_number}")
        print(f"📱 To: {target_number}")
        print(f"💬 Message: {test_message}")
        print("\n🚀 Sending SMS...")
        
        # Send SMS
        message = client.messages.create(
            body=test_message,
            from_=from_number,
            to=target_number
        )
        
        print(f"✅ SMS sent successfully!")
        print(f"📋 Message SID: {message.sid}")
        print(f"📊 Status: {message.status}")
        print(f"💰 Price: {message.price} {message.price_unit}")
        print(f"🌍 Direction: {message.direction}")
        
        return True
        
    except Exception as e:
        print(f"❌ SMS test failed: {str(e)}")
        return False

def test_voice_call_to_nigerian_number():
    """Test voice call to Nigerian number"""
    print("\n📞 VOICE CALL TEST TO NIGERIAN NUMBER")
    print("=" * 50)
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        from_number = os.getenv('TWILIO_PHONE_NUMBER')
        
        # Target Nigerian number
        target_number = '+*************'
        
        # Create Twilio client
        client = Client(account_sid, auth_token)
        
        # Voice script for Nigerian target
        voice_script = """
        <Response>
            <Say voice="alice">
                Hello, this is a security alert from MTN Nigeria. 
                Your account requires immediate verification. 
                Press 1 to verify your account, or press 2 to speak with customer service.
            </Say>
            <Gather numDigits="1" timeout="10">
                <Say voice="alice">Please press a number now.</Say>
            </Gather>
            <Say voice="alice">Thank you. Your response has been recorded.</Say>
        </Response>
        """
        
        print(f"📞 From: {from_number}")
        print(f"📱 To: {target_number}")
        print(f"🎙️ Script: MTN Security Alert (English)")
        print("\n🚀 Initiating voice call...")
        
        # Make voice call
        call = client.calls.create(
            twiml=voice_script,
            to=target_number,
            from_=from_number
        )
        
        print(f"✅ Voice call initiated successfully!")
        print(f"📋 Call SID: {call.sid}")
        print(f"📊 Status: {call.status}")
        print(f"🌍 Direction: {call.direction}")
        print(f"⏱️ Duration: {call.duration} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice call test failed: {str(e)}")
        return False

def test_account_balance():
    """Check Twilio account balance"""
    print("\n💰 TWILIO ACCOUNT BALANCE")
    print("=" * 50)
    
    try:
        from twilio.rest import Client
        
        account_sid = os.getenv('TWILIO_ACCOUNT_SID')
        auth_token = os.getenv('TWILIO_AUTH_TOKEN')
        
        client = Client(account_sid, auth_token)
        
        # Get account balance
        balance = client.api.accounts(account_sid).balance.fetch()
        
        print(f"💵 Current Balance: {balance.balance} {balance.currency}")
        print(f"📊 Account Status: Active")
        
        # Check if sufficient balance for Nigerian SMS/calls
        balance_amount = float(balance.balance)
        
        if balance_amount >= 1.0:
            sms_count = int(balance_amount / 0.04)  # $0.04 per SMS to Nigeria
            call_minutes = int(balance_amount / 0.20)  # $0.20 per minute to Nigeria
            
            print(f"📱 Estimated SMS to Nigeria: ~{sms_count} messages")
            print(f"📞 Estimated call minutes to Nigeria: ~{call_minutes} minutes")
            print("✅ Sufficient balance for testing!")
        else:
            print("⚠️ Low balance - consider adding funds for extensive testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Balance check failed: {str(e)}")
        return False

def generate_nigerian_sms_templates():
    """Generate Nigerian-specific SMS templates for testing"""
    print("\n🇳🇬 NIGERIAN SMS TEMPLATES FOR TESTING")
    print("=" * 50)
    
    templates = {
        "MTN Data Alert": "MTN Alert: Your 5GB data bundle expires in 24hrs. Renew now to avoid service interruption: https://mtn-ng.com/renew/abc123",
        
        "MTN NIN Verification": "MTN Nigeria: Complete your NIN verification within 48hrs to avoid line barring. Verify here: https://mtn-verify.ng/nin/xyz789",
        
        "MTN Security Alert": "MTN Security: Unusual activity detected on your line. Secure your account immediately: https://mtn-secure.ng/verify/def456",
        
        "MTN Airtime Bonus": "MTN: You've won ₦1,000 airtime! Claim your bonus before it expires: https://mtn-bonus.ng/claim/ghi789",
        
        "MTN KYC Update": "MTN: Update your KYC details to continue enjoying our services. Complete here: https://mtn-kyc.ng/update/jkl012"
    }
    
    for name, template in templates.items():
        print(f"\n📱 {name}:")
        print(f"   {template}")
    
    print(f"\n💡 These templates are optimized for MTN Nigeria users")
    print(f"🎯 Target +************* is an MTN number (906 prefix)")
    print(f"✅ High engagement expected with MTN-branded messages")

def main():
    """Main test function"""
    print("🚀 AMADIOHA-M257 TWILIO SETUP TEST")
    print("=" * 60)
    print("Testing Twilio integration for Nigerian phone targeting")
    print("Target: +************* (MTN Nigeria)")
    print("=" * 60)
    
    # Test 1: Verify credentials
    if not test_twilio_credentials():
        print("\n❌ Cannot proceed without proper Twilio credentials")
        return
    
    # Test 2: Check account balance
    test_account_balance()
    
    # Test 3: Generate templates
    generate_nigerian_sms_templates()
    
    # Test 4: SMS test (optional - will actually send)
    print(f"\n⚠️ WARNING: The following tests will send real SMS/calls to +*************")
    print(f"💰 This will consume Twilio credits")
    
    user_input = input("\nDo you want to send a test SMS? (y/n): ").lower().strip()
    if user_input == 'y':
        test_sms_to_nigerian_number()
    
    user_input = input("\nDo you want to make a test voice call? (y/n): ").lower().strip()
    if user_input == 'y':
        test_voice_call_to_nigerian_number()
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"1. Start AMADIOHA-M257: python run.py")
    print(f"2. Navigate to Contact Methods: http://localhost:5000/contact_methods")
    print(f"3. Enter target: +*************")
    print(f"4. Use MTN-branded SMS templates")
    print(f"5. Monitor campaign responses")
    
    print(f"\n✅ Twilio setup is ready for Nigerian phone targeting!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
