# AMADIOHA-M257 CYBER OPS SERVER STARTUP SCRIPT
# PowerShell version for advanced users

Write-Host "🚀 AMADIOHA-M257 CYBER OPS SERVER STARTUP" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found. Please install Python first." -ForegroundColor Red
    Write-Host "📦 Download from: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host "   OR from Microsoft Store: ms-windows-store://pdp/?productid=9NRWMJP3717K" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "📦 Creating virtual environment..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host "✅ Virtual environment created" -ForegroundColor Green
}

# Activate virtual environment
Write-Host "🔧 Activating virtual environment..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# Install requirements
Write-Host "📦 Installing/updating requirements..." -ForegroundColor Yellow
pip install -r requirements.txt

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️ .env file not found. Creating default configuration..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
    } else {
        Write-Host "❌ .env.example not found. Please configure .env manually." -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎯 AMADIOHA-M257 CYBER OPS PLATFORM" -ForegroundColor Magenta
Write-Host "====================================" -ForegroundColor Magenta
Write-Host "📱 Phone Operations: Location tracking, SMS campaigns, Voice calls" -ForegroundColor White
Write-Host "🦠 Malware Operations: FUD malware, keyloggers, document weaponization" -ForegroundColor White
Write-Host "🎣 Phishing Operations: Website cloning, credential harvesting" -ForegroundColor White
Write-Host "🌐 Web Scraping: Automatic logo/asset extraction" -ForegroundColor White
Write-Host ""

# Start the server
Write-Host "🚀 Starting AMADIOHA-M257 server..." -ForegroundColor Green
Write-Host ""

# Check if ngrok is needed
$ngrokUrl = Select-String -Path ".env" -Pattern "NGROK_SUBDOMAIN" | ForEach-Object { $_.Line.Split('=')[1] }
if ($ngrokUrl) {
    Write-Host "🌐 Ngrok tunnel configured: https://$ngrokUrl" -ForegroundColor Cyan
}

# Start the main application
python run.py
