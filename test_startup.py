#!/usr/bin/env python3
"""
Test startup script for AMADIOHA-M257
Checks if the application can start without errors
"""

import sys
import os

def test_imports():
    """Test if all critical imports work"""
    print("🧪 Testing critical imports...")
    
    try:
        import flask
        print("✅ Flask imported successfully")
    except ImportError as e:
        print(f"❌ Flask import failed: {e}")
        return False
    
    try:
        import flask_sqlalchemy
        print("✅ Flask-SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ Flask-SQLAlchemy import failed: {e}")
        return False
    
    try:
        import flask_login
        print("✅ Flask-Login imported successfully")
    except ImportError as e:
        print(f"❌ Flask-Login import failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ python-dotenv import failed: {e}")
        return False
    
    return True

def test_env_file():
    """Test if .env file exists and is readable"""
    print("\n🔧 Testing .env configuration...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    print("✅ .env file found")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        # Check critical environment variables
        secret_key = os.getenv('SECRET_KEY')
        if secret_key:
            print("✅ SECRET_KEY configured")
        else:
            print("⚠️ SECRET_KEY not configured")
        
        return True
    except Exception as e:
        print(f"❌ Error loading .env: {e}")
        return False

def test_basic_app():
    """Test if basic Flask app can be created"""
    print("\n🚀 Testing basic Flask app creation...")
    
    try:
        from flask import Flask
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-key'
        
        @app.route('/')
        def hello():
            return "AMADIOHA-M257 Test OK"
        
        print("✅ Basic Flask app created successfully")
        return True
    except Exception as e:
        print(f"❌ Flask app creation failed: {e}")
        return False

def test_database():
    """Test if database can be initialized"""
    print("\n💾 Testing database initialization...")
    
    try:
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        
        app = Flask(__name__)
        app.config['SECRET_KEY'] = 'test-key'
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        db = SQLAlchemy(app)
        
        class TestModel(db.Model):
            id = db.Column(db.Integer, primary_key=True)
            name = db.Column(db.String(100))
        
        with app.app_context():
            db.create_all()
        
        print("✅ Database initialization successful")
        
        # Clean up test database
        if os.path.exists('test.db'):
            os.remove('test.db')
        
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 AMADIOHA-M257 STARTUP TEST")
    print("=" * 50)
    
    tests = [
        ("Critical Imports", test_imports),
        ("Environment Configuration", test_env_file),
        ("Basic Flask App", test_basic_app),
        ("Database Initialization", test_database)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("🎉 TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎯 ALL TESTS PASSED - READY TO START SERVER")
        print("\nRun: python run.py")
    else:
        print("\n⚠️ SOME TESTS FAILED - INSTALL MISSING DEPENDENCIES")
        print("\nRun: pip install -r requirements_minimal.txt")
    
    return passed == len(results)

if __name__ == "__main__":
    main()
