{% extends 'base.html' %}
{% block title %}Network Reconnaissance | CyberSec Platform{% endblock %}
{% block content %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-search text-info"></i> Network Reconnaissance</h2>
                <div class="alert alert-info mb-0">
                    <i class="fas fa-eye"></i> AUTHORIZED SCANNING ONLY
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Subdomain Enumeration -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-sitemap text-primary"></i> Subdomain Enumeration</h5>
                </div>
                <div class="card-body">
                    <form id="subdomainForm">
                        <div class="mb-3">
                            <label class="form-label">Target Domain</label>
                            <input type="text" class="form-control" id="targetDomain" placeholder="example.com" required>
                            <div class="form-text">Enter domain without protocol (e.g., example.com)</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Enumerate Subdomains
                        </button>
                    </form>
                    
                    <div id="subdomainResults" class="mt-3" style="display: none;">
                        <h6>Found Subdomains:</h6>
                        <div class="alert alert-dark">
                            <div id="subdomainList"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Port Scanner -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-network-wired text-success"></i> Port Scanner</h5>
                </div>
                <div class="card-body">
                    <form id="portScanForm">
                        <div class="mb-3">
                            <label class="form-label">Target IP/Host</label>
                            <input type="text" class="form-control" id="scanTarget" placeholder="***********" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Ports to Scan</label>
                            <input type="text" class="form-control" id="portList" placeholder="21,22,23,80,443,3389">
                            <div class="form-text">Comma-separated list (leave empty for common ports)</div>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-radar"></i> Scan Ports
                        </button>
                    </form>
                    
                    <div id="portScanResults" class="mt-3" style="display: none;">
                        <h6>Open Ports:</h6>
                        <div class="alert alert-dark">
                            <div id="portList"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Dashboard -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar text-warning"></i> Reconnaissance Results</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3 id="subdomainCount">0</h3>
                                    <p>Subdomains Found</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3 id="openPortCount">0</h3>
                                    <p>Open Ports</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3 id="scanCount">0</h3>
                                    <p>Scans Performed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3 id="targetCount">0</h3>
                                    <p>Targets Scanned</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Educational Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-graduation-cap"></i> Reconnaissance Techniques</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Passive Reconnaissance:</h6>
                            <ul>
                                <li><strong>OSINT:</strong> Open Source Intelligence gathering</li>
                                <li><strong>DNS Enumeration:</strong> Discovering subdomains and records</li>
                                <li><strong>WHOIS Lookup:</strong> Domain registration information</li>
                                <li><strong>Social Media:</strong> Information from public profiles</li>
                            </ul>
                            
                            <h6>Active Reconnaissance:</h6>
                            <ul>
                                <li><strong>Port Scanning:</strong> Identifying open services</li>
                                <li><strong>Service Enumeration:</strong> Banner grabbing</li>
                                <li><strong>Vulnerability Scanning:</strong> Automated testing</li>
                                <li><strong>Network Mapping:</strong> Topology discovery</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Common Ports & Services:</h6>
                            <ul>
                                <li><strong>21:</strong> FTP (File Transfer Protocol)</li>
                                <li><strong>22:</strong> SSH (Secure Shell)</li>
                                <li><strong>23:</strong> Telnet</li>
                                <li><strong>25:</strong> SMTP (Email)</li>
                                <li><strong>53:</strong> DNS</li>
                                <li><strong>80:</strong> HTTP</li>
                                <li><strong>443:</strong> HTTPS</li>
                                <li><strong>3389:</strong> RDP (Remote Desktop)</li>
                            </ul>
                            
                            <h6>Detection & Prevention:</h6>
                            <ul>
                                <li>Intrusion Detection Systems (IDS)</li>
                                <li>Firewall logging and monitoring</li>
                                <li>Rate limiting and IP blocking</li>
                                <li>Network segmentation</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-danger mt-3">
                        <strong>Legal Notice:</strong> Only perform reconnaissance on systems you own or have explicit permission to test. 
                        Unauthorized scanning may be illegal and could trigger security alerts.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let scanCount = 0;
let targetCount = 0;
let targets = new Set();

document.getElementById('subdomainForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const domain = document.getElementById('targetDomain').value;
    targets.add(domain);
    scanCount++;
    updateCounters();
    
    const data = { domain: domain };
    
    fetch('/api/subdomain_enum', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const subdomainList = document.getElementById('subdomainList');
            subdomainList.innerHTML = '';
            
            if (data.subdomains.length > 0) {
                data.subdomains.forEach(subdomain => {
                    subdomainList.innerHTML += `<div class="mb-1"><i class="fas fa-globe text-primary"></i> ${subdomain}</div>`;
                });
            } else {
                subdomainList.innerHTML = '<div class="text-muted">No subdomains found</div>';
            }
            
            document.getElementById('subdomainResults').style.display = 'block';
            document.getElementById('subdomainCount').textContent = data.subdomains.length;
        }
    });
});

document.getElementById('portScanForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const target = document.getElementById('scanTarget').value;
    const portListInput = document.getElementById('portList').value;
    
    targets.add(target);
    scanCount++;
    updateCounters();
    
    const data = {
        target: target,
        ports: portListInput ? portListInput.split(',').map(p => parseInt(p.trim())) : null
    };
    
    fetch('/api/basic_port_scan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const portList = document.getElementById('portList');
            portList.innerHTML = '';
            
            if (data.open_ports.length > 0) {
                data.open_ports.forEach(port => {
                    portList.innerHTML += `<div class="mb-1"><i class="fas fa-door-open text-success"></i> Port ${port}</div>`;
                });
            } else {
                portList.innerHTML = '<div class="text-muted">No open ports found</div>';
            }
            
            document.getElementById('portScanResults').style.display = 'block';
            document.getElementById('openPortCount').textContent = data.open_ports.length;
        }
    });
});

function updateCounters() {
    document.getElementById('scanCount').textContent = scanCount;
    document.getElementById('targetCount').textContent = targets.size;
}
</script>

{% endblock %}
