@echo off
REM AMADIOHA-M257 CMD Startup Script
REM Bypasses PowerShell profile issues

echo 🚀 AMADIOHA-M257 CYBER OPS PLATFORM
echo ==================================================
echo 🎯 Starting server via CMD (bypassing PowerShell)
echo ==================================================

REM Change to the correct directory
cd /d "c:\Users\<USER>\phishing-awareness-platform"

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found
    echo Please install Python from Microsoft Store
    pause
    exit /b 1
)

echo ✅ Python found
echo 🚀 Starting AMADIOHA-M257 server...
echo.

REM Start the server using the clean startup script
python start_clean.py

pause
