{% extends 'base.html' %}
{% block title %}Phone Operations | AMADIOHA-M257{% endblock %}
{% block content %}

<div class="container-fluid">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-mobile-alt text-primary"></i> PHONE OPERATIONS CENTER</h2>
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-triangle"></i> AUTHORIZED TESTING ONLY
                </div>
            </div>
        </div>
    </div>

    <!-- Target Configuration -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crosshairs"></i> Target Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="targetPhone" class="form-label">Target Phone Number</label>
                            <input type="text" class="form-control" id="targetPhone" placeholder="+2349063978612" required>
                            <div class="form-text">Nigerian format: +234803... or 0803...</div>
                        </div>
                        <div class="col-md-3">
                            <label for="operationType" class="form-label">Operation Type</label>
                            <select class="form-control" id="operationType">
                                <option value="contactless_tracking">🛰️ Contactless Tracking (Advanced)</option>
                                <option value="tower_triangulation">📡 Cell Tower Triangulation</option>
                                <option value="ss7_exploitation">🔓 SS7 Protocol Exploitation</option>
                                <option value="imsi_catcher">📶 IMSI Catcher Simulation</option>
                                <option value="full_analysis">🎯 Full Analysis (All Methods)</option>
                                <option value="location_tracking">📍 Location Tracking Only</option>
                                <option value="sms_campaign">📱 SMS Campaign Only</option>
                                <option value="voice_call">📞 Voice Call Only</option>
                                <option value="osint_only">🔍 OSINT Only</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="priority" class="form-label">Priority Level</label>
                            <select class="form-control" id="priority">
                                <option value="high">🔴 High Priority</option>
                                <option value="medium">🟡 Medium Priority</option>
                                <option value="low">🟢 Low Priority</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" id="analyzeTargetBtn">
                                <i class="fas fa-search"></i> ANALYZE TARGET
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Operations Dashboard -->
    <div class="row">
        <!-- Left Column: Operations -->
        <div class="col-lg-8">
            <!-- Phone Intelligence -->
            <div class="card mb-4" id="phoneIntelCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle"></i> Phone Intelligence</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2"><strong>📱 Number:</strong> <span id="phoneNumber">-</span></div>
                            <div class="mb-2"><strong>🌍 Country:</strong> <span id="phoneCountry">-</span></div>
                            <div class="mb-2"><strong>📡 Carrier:</strong> <span id="phoneCarrier">-</span></div>
                            <div class="mb-2"><strong>🇳🇬 Nigerian Carrier:</strong> <span id="nigerianCarrier">-</span></div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2"><strong>🔢 Prefix:</strong> <span id="networkPrefix">-</span></div>
                            <div class="mb-2"><strong>📶 Network Type:</strong> <span id="networkType">-</span></div>
                            <div class="mb-2"><strong>✅ Valid:</strong> <span id="phoneValid">-</span></div>
                            <div class="mb-2"><strong>📞 Line Type:</strong> <span id="lineType">-</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Tracking -->
            <div class="card mb-4" id="locationCard" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-map-marker-alt"></i> Real-Time Location Tracking</h5>
                    <div>
                        <button class="btn btn-sm btn-success" id="startTrackingBtn">
                            <i class="fas fa-play"></i> Start Tracking
                        </button>
                        <button class="btn btn-sm btn-danger" id="stopTrackingBtn" style="display: none;">
                            <i class="fas fa-stop"></i> Stop Tracking
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-2"><strong>📍 Latitude:</strong> <span id="currentLat" class="font-monospace">-</span></div>
                            <div class="mb-2"><strong>📍 Longitude:</strong> <span id="currentLon" class="font-monospace">-</span></div>
                            <div class="mb-2"><strong>🎯 Accuracy:</strong> <span id="currentAccuracy">-</span></div>
                            <div class="mb-2"><strong>📡 Source:</strong> <span id="currentSource">-</span></div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2"><strong>🏙️ City:</strong> <span id="currentCity">-</span></div>
                            <div class="mb-2"><strong>🗺️ State:</strong> <span id="currentState">-</span></div>
                            <div class="mb-2"><strong>📍 LGA:</strong> <span id="currentLGA">-</span></div>
                            <div class="mb-2"><strong>🏛️ Landmark:</strong> <span id="currentLandmark">-</span></div>
                        </div>
                        <div class="col-12 mt-2">
                            <div class="mb-2"><strong>🏠 Street Address:</strong><br>
                                <span id="currentAddress" class="text-primary">-</span>
                            </div>
                            <div class="mb-2"><strong>🎯 Precision:</strong> <span id="precisionLevel" class="badge bg-secondary">-</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Contactless Tracking -->
            <div class="card mb-4" id="contactlessTrackingCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-satellite"></i> ADVANCED CONTACTLESS TRACKING</h5>
                    <small class="text-warning">No SMS/Call Required - Silent Location Detection</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-broadcast-tower"></i> Cell Tower Analysis</h6>
                            <div class="mb-2"><strong>📡 Primary Tower:</strong> <span id="primaryTower">-</span></div>
                            <div class="mb-2"><strong>📶 Signal Strength:</strong> <span id="signalStrength">-</span></div>
                            <div class="mb-2"><strong>🎯 Tower LAT/LON:</strong> <span id="towerCoords">-</span></div>
                            <div class="mb-2"><strong>📏 Distance:</strong> <span id="towerDistance">-</span></div>
                            <div class="mb-2"><strong>🔢 Cell ID:</strong> <span id="cellId">-</span></div>
                            <div class="mb-2"><strong>🌐 LAC:</strong> <span id="lacCode">-</span></div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-shield-alt"></i> SS7 Protocol Analysis</h6>
                            <div class="mb-2"><strong>🔓 HLR Status:</strong> <span id="hlrStatus">-</span></div>
                            <div class="mb-2"><strong>📱 IMSI:</strong> <span id="imsiNumber">-</span></div>
                            <div class="mb-2"><strong>🔐 MSC:</strong> <span id="mscNumber">-</span></div>
                            <div class="mb-2"><strong>🌍 Roaming:</strong> <span id="roamingStatus">-</span></div>
                            <div class="mb-2"><strong>📞 Call Status:</strong> <span id="callStatus">-</span></div>
                            <div class="mb-2"><strong>💬 SMS Status:</strong> <span id="smsStatus">-</span></div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-search"></i> OSINT Intelligence</h6>
                            <div class="mb-2"><strong>🔍 Social Media:</strong> <span id="socialMedia">-</span></div>
                            <div class="mb-2"><strong>💼 LinkedIn:</strong> <span id="linkedinProfile">-</span></div>
                            <div class="mb-2"><strong>📘 Facebook:</strong> <span id="facebookProfile">-</span></div>
                            <div class="mb-2"><strong>🐦 Twitter:</strong> <span id="twitterProfile">-</span></div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-database"></i> Database Lookups</h6>
                            <div class="mb-2"><strong>🏦 Banking:</strong> <span id="bankingInfo">-</span></div>
                            <div class="mb-2"><strong>🆔 Identity:</strong> <span id="identityInfo">-</span></div>
                            <div class="mb-2"><strong>📧 Email:</strong> <span id="emailInfo">-</span></div>
                            <div class="mb-2"><strong>🏠 Address:</strong> <span id="addressInfo">-</span></div>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-bug"></i> Threat Intelligence</h6>
                            <div class="mb-2"><strong>🚨 VirusTotal:</strong> <span id="virusTotalStatus">-</span></div>
                            <div class="mb-2"><strong>🔒 Breach Data:</strong> <span id="breachData">-</span></div>
                            <div class="mb-2"><strong>⚠️ Risk Score:</strong> <span id="riskScore">-</span></div>
                            <div class="mb-2"><strong>🕵️ Dark Web:</strong> <span id="darkWebMentions">-</span></div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>Advanced Tracking Methods:</strong> This system uses cutting-edge techniques including
                        SS7 protocol exploitation, IMSI catcher simulation, cell tower triangulation, and real-time
                        OSINT correlation for precise contactless location tracking.
                    </div>

                    <div class="d-flex gap-2">
                        <button class="btn btn-danger" onclick="startContactlessTracking()">
                            <i class="fas fa-satellite"></i> START CONTACTLESS TRACKING
                        </button>
                        <button class="btn btn-outline-warning" onclick="performSS7Attack()">
                            <i class="fas fa-unlock"></i> SS7 EXPLOITATION
                        </button>
                        <button class="btn btn-outline-info" onclick="triangulateLocation()">
                            <i class="fas fa-map-marked-alt"></i> TOWER TRIANGULATION
                        </button>
                        <button class="btn btn-outline-success" onclick="deepOSINT()">
                            <i class="fas fa-search-plus"></i> DEEP OSINT
                        </button>
                    </div>
                </div>
            </div>

            <!-- Contact Operations -->
            <div class="card mb-4" id="contactOpsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-phone-alt"></i> Contact Operations</h5>
                </div>
                <div class="card-body">
                    <!-- SMS Campaign -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6><i class="fas fa-sms"></i> SMS Campaign</h6>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="smsCategory">
                                <option value="banking">🏦 Banking</option>
                                <option value="telecom">📱 Telecom</option>
                                <option value="government">🏛️ Government</option>
                                <option value="ecommerce">🛒 E-commerce</option>
                                <option value="social">📱 Social Media</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="smsTemplate">
                                <option value="">Select template...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-warning w-100" id="sendSMSBtn">
                                <i class="fas fa-paper-plane"></i> Send SMS
                            </button>
                        </div>
                    </div>

                    <!-- Voice Call -->
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="fas fa-phone"></i> Voice Call (Location Capture)</h6>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="voiceType">
                                <option value="data_unsubscribe">💰 Data Charges Unsubscribe</option>
                                <option value="network_survey">📊 Network Survey</option>
                                <option value="delivery_confirm">📦 Delivery Confirmation</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="voiceLanguage">
                                <option value="english">🇬🇧 English</option>
                                <option value="pidgin">🇳🇬 Nigerian Pidgin</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger w-100" id="makeCallBtn">
                                <i class="fas fa-phone"></i> Make Call
                            </button>
                        </div>
                        <div class="col-md-3">
                            <span id="callStatus" class="badge bg-secondary w-100">Ready</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Results & History -->
        <div class="col-lg-4">
            <!-- Operation Status -->
            <div class="card mb-4" id="statusCard">
                <div class="card-header">
                    <h5><i class="fas fa-tachometer-alt"></i> Operation Status</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>🎯 Target:</strong> <span id="statusTarget">Not selected</span>
                    </div>
                    <div class="mb-2">
                        <strong>📊 Operation:</strong> <span id="statusOperation">Idle</span>
                    </div>
                    <div class="mb-2">
                        <strong>⏰ Started:</strong> <span id="statusStarted">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>📈 Progress:</strong>
                        <div class="progress mt-1">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="mb-2">
                        <strong>📍 Location Status:</strong> <span id="locationStatus" class="badge bg-secondary">Inactive</span>
                    </div>
                    <div class="mb-2">
                        <strong>📱 SMS Status:</strong> <span id="smsStatus" class="badge bg-secondary">Ready</span>
                    </div>
                    <div class="mb-2">
                        <strong>📞 Call Status:</strong> <span id="voiceStatus" class="badge bg-secondary">Ready</span>
                    </div>
                </div>
            </div>

            <!-- Quick Results -->
            <div class="card mb-4" id="quickResultsCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Quick Results</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>📍 Last Location:</strong><br>
                        <small id="lastLocation" class="text-muted">No location data</small>
                    </div>
                    <div class="mb-2">
                        <strong>📱 SMS Response:</strong> <span id="smsResponse" class="badge bg-secondary">None</span>
                    </div>
                    <div class="mb-2">
                        <strong>📞 Voice Response:</strong> <span id="voiceResponse" class="badge bg-secondary">None</span>
                    </div>
                    <div class="mb-2">
                        <strong>🎯 Confidence:</strong> <span id="confidenceLevel" class="badge bg-secondary">-</span>
                    </div>
                </div>
            </div>

            <!-- Nigerian Intelligence -->
            <div class="card mb-4" id="nigerianIntelCard" style="display: none;">
                <div class="card-header">
                    <h5><i class="fas fa-flag"></i> 🇳🇬 Nigerian Intelligence</h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>📡 Network:</strong> <span id="nigerianNetwork">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>🏙️ Likely Cities:</strong><br>
                        <small id="likelyCities" class="text-muted">-</small>
                    </div>
                    <div class="mb-2">
                        <strong>📊 Success Rate:</strong> <span id="successRate" class="badge bg-info">-</span>
                    </div>
                    <div class="mb-2">
                        <strong>💡 Recommendations:</strong><br>
                        <small id="recommendations" class="text-muted">-</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Operations History -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> Operations History</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" id="refreshHistoryBtn">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-sm btn-outline-success" id="exportHistoryBtn">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Phone Number</th>
                                    <th>Operation</th>
                                    <th>Location</th>
                                    <th>SMS Status</th>
                                    <th>Voice Status</th>
                                    <th>Success Rate</th>
                                    <th>Started</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- History will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentTarget = null;
let trackingInterval = null;
let operationStartTime = null;

// SMS Templates by category
const smsTemplates = {
    banking: {
        'gtbank_alert': 'GTBank Alert: Your account has been temporarily restricted. Verify immediately: https://gtbank-verify.ng/secure',
        'firstbank_security': 'First Bank: Unusual login detected. Secure your account: https://firstbank-ng.com/verify',
        'zenith_kyc': 'Zenith Bank: Complete your KYC update to avoid account suspension: https://zenith-kyc.ng/update'
    },
    telecom: {
        'mtn_data': 'MTN Alert: Your 5GB data bundle expires in 24hrs. Renew now: https://mtn-ng.com/renew',
        'airtel_bonus': 'Airtel: You have won ₦1,000 airtime! Claim here: https://airtel-ng.com/claim',
        'glo_nin': 'Glo: Complete NIN verification to avoid line barring: https://glo-verify.ng/nin'
    },
    government: {
        'nin_update': 'NIMC: Update your NIN details within 48hrs: https://nimc-update.gov.ng/verify',
        'bvn_verification': 'CBN: Verify your BVN to avoid account restrictions: https://cbn-verify.gov.ng/bvn',
        'tax_notice': 'FIRS: Complete your tax filing to avoid penalties: https://firs-online.gov.ng/file'
    },
    ecommerce: {
        'jumia_delivery': 'Jumia: Your package is ready for delivery. Confirm address: https://jumia-ng.com/delivery',
        'konga_payment': 'Konga: Payment confirmation required for your order: https://konga-pay.ng/confirm',
        'paystack_alert': 'Paystack: Verify your payment method: https://paystack-verify.ng/payment'
    },
    social: {
        'whatsapp_security': 'WhatsApp: Your account login from new device. Verify: https://whatsapp-verify.com/secure',
        'facebook_alert': 'Facebook: Suspicious activity detected. Secure account: https://fb-security.com/verify',
        'instagram_login': 'Instagram: New login detected. Confirm identity: https://ig-verify.com/login'
    }
};

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateSMSTemplates();

    // Event listeners
    document.getElementById('analyzeTargetBtn').addEventListener('click', analyzeTarget);
    document.getElementById('startTrackingBtn').addEventListener('click', startTracking);
    document.getElementById('stopTrackingBtn').addEventListener('click', stopTracking);
    document.getElementById('sendSMSBtn').addEventListener('click', sendSMS);
    document.getElementById('makeCallBtn').addEventListener('click', makeCall);
    document.getElementById('smsCategory').addEventListener('change', updateSMSTemplates);
    document.getElementById('refreshHistoryBtn').addEventListener('click', loadHistory);
    document.getElementById('exportHistoryBtn').addEventListener('click', exportHistory);
});

function updateSMSTemplates() {
    const category = document.getElementById('smsCategory').value;
    const templateSelect = document.getElementById('smsTemplate');

    templateSelect.innerHTML = '<option value="">Select template...</option>';

    if (smsTemplates[category]) {
        Object.entries(smsTemplates[category]).forEach(([key, template]) => {
            const option = document.createElement('option');
            option.value = key;
            option.textContent = template.substring(0, 50) + '...';
            templateSelect.appendChild(option);
        });
    }
}

function analyzeTarget() {
    const phoneNumber = document.getElementById('targetPhone').value;
    const operationType = document.getElementById('operationType').value;

    if (!phoneNumber) {
        alert('Please enter a phone number');
        return;
    }

    currentTarget = phoneNumber;
    operationStartTime = new Date();

    // Update status
    document.getElementById('statusTarget').textContent = phoneNumber;
    document.getElementById('statusOperation').textContent = operationType.replace('_', ' ').toUpperCase();
    document.getElementById('statusStarted').textContent = operationStartTime.toLocaleTimeString();

    // Show loading
    const btn = document.getElementById('analyzeTargetBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';
    btn.disabled = true;

    // Get phone information
    fetch('/api/phone_info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone_number: phoneNumber })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPhoneIntelligence(data.data);

            // Show appropriate cards based on operation type
            showOperationCards(operationType);

            // Start automatic operations if full analysis
            if (operationType === 'full_analysis') {
                setTimeout(() => startTracking(), 1000);
            }
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to analyze target');
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-search"></i> ANALYZE TARGET';
        btn.disabled = false;
    });
}

function displayPhoneIntelligence(data) {
    document.getElementById('phoneNumber').textContent = data.number || '-';
    document.getElementById('phoneCountry').textContent = data.country || '-';
    document.getElementById('phoneCarrier').textContent = data.carrier || '-';
    document.getElementById('nigerianCarrier').textContent = data.nigerian_carrier || '-';
    document.getElementById('networkPrefix').textContent = data.network_prefix || '-';
    document.getElementById('networkType').textContent = data.network_type || '-';
    document.getElementById('phoneValid').textContent = data.is_valid ? 'Yes' : 'No';
    document.getElementById('lineType').textContent = data.line_type || '-';

    // Show phone intelligence card
    document.getElementById('phoneIntelCard').style.display = 'block';

    // Update Nigerian intelligence if applicable
    if (data.nigerian_carrier) {
        updateNigerianIntelligence(data);
    }

    updateProgress(25);
}

function updateNigerianIntelligence(data) {
    document.getElementById('nigerianNetwork').textContent = data.nigerian_carrier;

    // Set likely cities based on carrier
    let cities = '';
    let successRate = '';
    let recommendations = '';

    if (data.nigerian_carrier.includes('MTN')) {
        cities = 'Lagos, Abuja, Kano, Ibadan, Port Harcourt';
        successRate = '90%';
        recommendations = 'Use MTN-branded templates. High response rate expected.';
    } else if (data.nigerian_carrier.includes('Airtel')) {
        cities = 'Abuja, Kano, Lagos, Kaduna, Jos';
        successRate = '85%';
        recommendations = 'Focus on northern regions. Use Airtel templates.';
    } else if (data.nigerian_carrier.includes('Glo')) {
        cities = 'Lagos, Port Harcourt, Benin, Warri, Aba';
        successRate = '80%';
        recommendations = 'Strong in southern regions. Use Glo templates.';
    } else {
        cities = 'Major Nigerian cities';
        successRate = '75%';
        recommendations = 'Use generic telecom templates.';
    }

    document.getElementById('likelyCities').textContent = cities;
    document.getElementById('successRate').textContent = successRate;
    document.getElementById('successRate').className = `badge bg-${getSuccessRateColor(successRate)}`;
    document.getElementById('recommendations').textContent = recommendations;

    document.getElementById('nigerianIntelCard').style.display = 'block';
}

function showOperationCards(operationType) {
    // Hide all cards first
    document.getElementById('locationCard').style.display = 'none';
    document.getElementById('contactOpsCard').style.display = 'none';
    document.getElementById('quickResultsCard').style.display = 'block';

    // Show cards based on operation type
    switch(operationType) {
        case 'full_analysis':
            document.getElementById('locationCard').style.display = 'block';
            document.getElementById('contactOpsCard').style.display = 'block';
            break;
        case 'location_tracking':
            document.getElementById('locationCard').style.display = 'block';
            break;
        case 'sms_campaign':
        case 'voice_call':
            document.getElementById('contactOpsCard').style.display = 'block';
            break;
    }
}

function startTracking() {
    if (!currentTarget) {
        alert('Please analyze a target first');
        return;
    }

    // Update UI
    document.getElementById('startTrackingBtn').style.display = 'none';
    document.getElementById('stopTrackingBtn').style.display = 'inline-block';
    document.getElementById('locationStatus').textContent = 'Active';
    document.getElementById('locationStatus').className = 'badge bg-success';

    // Start tracking
    fetch('/api/start_tracking', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            phone_number: currentTarget,
            duration: 60 // 60 minutes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Start polling for location updates
            trackingInterval = setInterval(updateLocation, 5000);
            updateProgress(50);
        } else {
            alert('Failed to start tracking: ' + data.error);
            stopTracking();
        }
    })
    .catch(error => {
        console.error('Tracking error:', error);
        stopTracking();
    });
}

function stopTracking() {
    if (trackingInterval) {
        clearInterval(trackingInterval);
        trackingInterval = null;
    }

    // Update UI
    document.getElementById('startTrackingBtn').style.display = 'inline-block';
    document.getElementById('stopTrackingBtn').style.display = 'none';
    document.getElementById('locationStatus').textContent = 'Inactive';
    document.getElementById('locationStatus').className = 'badge bg-secondary';
}

function updateLocation() {
    if (!currentTarget) return;

    // Simulate location update (replace with actual API call)
    const mockLocation = {
        latitude: 6.5244 + (Math.random() - 0.5) * 0.01,
        longitude: 3.3792 + (Math.random() - 0.5) * 0.01,
        accuracy: Math.random() * 20 + 5,
        source: ['gps', 'cell_tower', 'wifi'][Math.floor(Math.random() * 3)],
        city: 'Lagos',
        state: 'Lagos State',
        lga: 'Ikeja',
        landmark: 'Computer Village',
        street_address: '45 Allen Avenue, Ikeja, Lagos, Lagos State, Nigeria',
        precision_level: 'street_level'
    };

    updateLocationDisplay(mockLocation);
}

function updateLocationDisplay(location) {
    document.getElementById('currentLat').textContent = location.latitude.toFixed(7);
    document.getElementById('currentLon').textContent = location.longitude.toFixed(7);
    document.getElementById('currentAccuracy').textContent = `±${location.accuracy.toFixed(1)}m`;
    document.getElementById('currentSource').textContent = location.source;
    document.getElementById('currentCity').textContent = location.city || '-';
    document.getElementById('currentState').textContent = location.state || '-';
    document.getElementById('currentLGA').textContent = location.lga || '-';
    document.getElementById('currentLandmark').textContent = location.landmark || '-';
    document.getElementById('currentAddress').textContent = location.street_address || '-';

    if (location.precision_level) {
        const precision = document.getElementById('precisionLevel');
        precision.textContent = location.precision_level.replace('_', ' ').toUpperCase();
        precision.className = `badge bg-${location.precision_level === 'street_level' ? 'success' : 'warning'}`;
    }

    // Update quick results
    document.getElementById('lastLocation').textContent = `${location.city}, ${location.state}`;

    updateProgress(75);
}

function sendSMS() {
    const category = document.getElementById('smsCategory').value;
    const template = document.getElementById('smsTemplate').value;

    if (!currentTarget) {
        alert('Please analyze a target first');
        return;
    }

    if (!template) {
        alert('Please select an SMS template');
        return;
    }

    const btn = document.getElementById('sendSMSBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    btn.disabled = true;

    document.getElementById('smsStatus').textContent = 'Sending';
    document.getElementById('smsStatus').className = 'badge bg-warning';

    // Get template content
    const templateContent = smsTemplates[category][template];

    fetch('/api/sms_campaign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            phone_number: currentTarget,
            message: templateContent,
            category: category,
            template: template
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('smsStatus').textContent = 'Sent';
            document.getElementById('smsStatus').className = 'badge bg-success';
            document.getElementById('smsResponse').textContent = 'Delivered';
            document.getElementById('smsResponse').className = 'badge bg-success';
            updateProgress(85);
        } else {
            document.getElementById('smsStatus').textContent = 'Failed';
            document.getElementById('smsStatus').className = 'badge bg-danger';
            alert('SMS failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('SMS error:', error);
        document.getElementById('smsStatus').textContent = 'Error';
        document.getElementById('smsStatus').className = 'badge bg-danger';
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-paper-plane"></i> Send SMS';
        btn.disabled = false;
    });
}

function makeCall() {
    const voiceType = document.getElementById('voiceType').value;
    const language = document.getElementById('voiceLanguage').value;

    if (!currentTarget) {
        alert('Please analyze a target first');
        return;
    }

    const btn = document.getElementById('makeCallBtn');
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calling...';
    btn.disabled = true;

    document.getElementById('callStatus').textContent = 'Initiating';
    document.getElementById('callStatus').className = 'badge bg-warning';
    document.getElementById('voiceStatus').textContent = 'Calling';
    document.getElementById('voiceStatus').className = 'badge bg-warning';

    // Map voice types to API capture types
    const captureTypeMap = {
        'data_unsubscribe': 'emergency_verification',
        'network_survey': 'network_survey',
        'delivery_confirm': 'delivery_confirmation'
    };

    fetch('/api/twilio/location_call', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            phone_number: currentTarget,
            capture_type: captureTypeMap[voiceType],
            language: language
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('callStatus').textContent = 'In Progress';
            document.getElementById('callStatus').className = 'badge bg-info';
            document.getElementById('voiceStatus').textContent = 'Connected';
            document.getElementById('voiceStatus').className = 'badge bg-success';

            // Monitor call status
            monitorCall(data.call_sid);
            updateProgress(95);
        } else {
            document.getElementById('callStatus').textContent = 'Failed';
            document.getElementById('callStatus').className = 'badge bg-danger';
            document.getElementById('voiceStatus').textContent = 'Failed';
            document.getElementById('voiceStatus').className = 'badge bg-danger';
            alert('Call failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Call error:', error);
        document.getElementById('callStatus').textContent = 'Error';
        document.getElementById('callStatus').className = 'badge bg-danger';
    })
    .finally(() => {
        btn.innerHTML = '<i class="fas fa-phone"></i> Make Call';
        btn.disabled = false;
    });
}

function monitorCall(callSid) {
    const checkStatus = () => {
        fetch(`/api/twilio/call_status/${callSid}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('callStatus').textContent = data.status;

                if (data.status === 'completed') {
                    document.getElementById('callStatus').className = 'badge bg-success';
                    document.getElementById('voiceStatus').textContent = 'Completed';
                    document.getElementById('voiceStatus').className = 'badge bg-success';
                    document.getElementById('voiceResponse').textContent = 'Location Captured';
                    document.getElementById('voiceResponse').className = 'badge bg-success';
                    updateProgress(100);
                } else if (data.status === 'failed') {
                    document.getElementById('callStatus').className = 'badge bg-danger';
                    document.getElementById('voiceStatus').textContent = 'Failed';
                    document.getElementById('voiceStatus').className = 'badge bg-danger';
                } else {
                    // Continue monitoring
                    setTimeout(checkStatus, 3000);
                }
            }
        })
        .catch(error => console.error('Status check error:', error));
    };

    setTimeout(checkStatus, 3000);
}

function updateProgress(percentage) {
    document.getElementById('progressBar').style.width = percentage + '%';

    if (percentage >= 100) {
        document.getElementById('confidenceLevel').textContent = 'High';
        document.getElementById('confidenceLevel').className = 'badge bg-success';
    } else if (percentage >= 50) {
        document.getElementById('confidenceLevel').textContent = 'Medium';
        document.getElementById('confidenceLevel').className = 'badge bg-warning';
    }
}

function getSuccessRateColor(rate) {
    const percentage = parseInt(rate);
    if (percentage >= 85) return 'success';
    if (percentage >= 70) return 'warning';
    return 'danger';
}

function loadHistory() {
    // Load operation history
    fetch('/api/phone_operations_history')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayHistory(data.operations);
        }
    })
    .catch(error => console.error('History load error:', error));
}

function displayHistory(operations) {
    const tbody = document.getElementById('historyTableBody');
    tbody.innerHTML = '';

    operations.forEach(op => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td class="font-monospace">${op.phone_number}</td>
            <td>${op.operation_type}</td>
            <td>${op.location || 'Not captured'}</td>
            <td><span class="badge bg-${op.sms_status === 'sent' ? 'success' : 'secondary'}">${op.sms_status || 'None'}</span></td>
            <td><span class="badge bg-${op.voice_status === 'completed' ? 'success' : 'secondary'}">${op.voice_status || 'None'}</span></td>
            <td>${op.success_rate || '-'}%</td>
            <td>${new Date(op.started_at).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewOperation('${op.id}')">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
    });
}

function exportHistory() {
    // Export operation history
    window.open('/api/export_phone_operations', '_blank');
}

function viewOperation(operationId) {
    // View detailed operation information
    alert(`View operation details: ${operationId}`);
}

// Advanced Contactless Tracking Functions
function startContactlessTracking() {
    const phoneNumber = document.getElementById('targetPhone').value;
    if (!phoneNumber) {
        alert('Please enter a phone number first');
        return;
    }

    // Show contactless tracking card
    document.getElementById('contactlessTrackingCard').style.display = 'block';

    // Start comprehensive tracking
    showTrackingProgress('Initializing contactless tracking systems...');

    // Step 1: Cell Tower Analysis
    setTimeout(() => {
        showTrackingProgress('Analyzing cell tower infrastructure...');
        performCellTowerAnalysis(phoneNumber);
    }, 1000);

    // Step 2: SS7 Protocol Exploitation
    setTimeout(() => {
        showTrackingProgress('Exploiting SS7 protocol vulnerabilities...');
        performSS7Attack();
    }, 3000);

    // Step 3: OSINT Correlation
    setTimeout(() => {
        showTrackingProgress('Correlating OSINT intelligence...');
        deepOSINT();
    }, 5000);

    // Step 4: Final triangulation
    setTimeout(() => {
        showTrackingProgress('Performing final location triangulation...');
        triangulateLocation();
    }, 7000);
}

function performCellTowerAnalysis(phoneNumber) {
    // Simulate advanced cell tower analysis
    const towerData = {
        primaryTower: 'MTN-LAG-001',
        signalStrength: '-65 dBm',
        towerCoords: '6.5244° N, 3.3792° E',
        towerDistance: '0.8 km',
        cellId: '23401',
        lacCode: 'LAC-001'
    };

    // Update UI with tower data
    document.getElementById('primaryTower').textContent = towerData.primaryTower;
    document.getElementById('signalStrength').textContent = towerData.signalStrength;
    document.getElementById('towerCoords').textContent = towerData.towerCoords;
    document.getElementById('towerDistance').textContent = towerData.towerDistance;
    document.getElementById('cellId').textContent = towerData.cellId;
    document.getElementById('lacCode').textContent = towerData.lacCode;

    // Fetch real tower data from API
    fetch('/api/analyze_cell_towers', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: phoneNumber,
            analysis_type: 'contactless'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update with real data
            Object.keys(data.tower_data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = data.tower_data[key];
                }
            });
        }
    })
    .catch(error => {
        console.error('Tower analysis error:', error);
    });
}

function performSS7Attack() {
    // Simulate SS7 protocol exploitation
    const ss7Data = {
        hlrStatus: 'Active',
        imsiNumber: '234-01-xxxxxxxxx',
        mscNumber: 'MSC-234-01',
        roamingStatus: 'Home Network',
        callStatus: 'Available',
        smsStatus: 'Enabled'
    };

    // Update UI with SS7 data
    document.getElementById('hlrStatus').textContent = ss7Data.hlrStatus;
    document.getElementById('imsiNumber').textContent = ss7Data.imsiNumber;
    document.getElementById('mscNumber').textContent = ss7Data.mscNumber;
    document.getElementById('roamingStatus').textContent = ss7Data.roamingStatus;
    document.getElementById('callStatus').textContent = ss7Data.callStatus;
    document.getElementById('smsStatus').textContent = ss7Data.smsStatus;
}

function deepOSINT() {
    const phoneNumber = document.getElementById('targetPhone').value;

    // Simulate OSINT data gathering
    const osintData = {
        socialMedia: 'Found profiles',
        linkedinProfile: 'Active profile detected',
        facebookProfile: 'Profile found',
        twitterProfile: 'Account located',
        bankingInfo: 'Bank records found',
        identityInfo: 'Identity verified',
        emailInfo: 'Email addresses found',
        addressInfo: 'Home address located'
    };

    // Update UI with OSINT data
    Object.keys(osintData).forEach(key => {
        const element = document.getElementById(key);
        if (element) {
            element.textContent = osintData[key];
        }
    });
}

function triangulateLocation() {
    const phoneNumber = document.getElementById('targetPhone').value;

    // Perform advanced triangulation
    fetch('/api/triangulate_location', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            phone_number: phoneNumber,
            method: 'contactless_triangulation'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update location data
            document.getElementById('currentLat').textContent = data.location.latitude;
            document.getElementById('currentLon').textContent = data.location.longitude;
            document.getElementById('currentAccuracy').textContent = data.location.accuracy + 'm';
            document.getElementById('currentSource').textContent = 'Contactless Triangulation';
            showTrackingProgress('Contactless tracking completed successfully!');
        } else {
            showTrackingProgress('Triangulation failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Triangulation error:', error);
        showTrackingProgress('Triangulation error: ' + error.message);
    });
}

function showTrackingProgress(message) {
    // Create or update progress indicator
    let progressDiv = document.getElementById('trackingProgress');
    if (!progressDiv) {
        progressDiv = document.createElement('div');
        progressDiv.id = 'trackingProgress';
        progressDiv.className = 'alert alert-info mt-3';
        document.querySelector('#contactlessTrackingCard .card-body').appendChild(progressDiv);
    }

    progressDiv.innerHTML = `
        <i class="fas fa-spinner fa-spin"></i> ${message}
    `;

    // Auto-hide after completion
    if (message.includes('completed') || message.includes('failed')) {
        setTimeout(() => {
            progressDiv.innerHTML = `<i class="fas fa-check text-success"></i> ${message}`;
        }, 1000);
    }
}

</script>

{% endblock %}
